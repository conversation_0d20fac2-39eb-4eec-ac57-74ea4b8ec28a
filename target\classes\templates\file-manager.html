<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>胜大科技智联管理系统 - 文件管理</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/button-styles.css}">
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        
        /* 导航条美化样式 */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --transition: all 0.3s ease;
        }

        .navbar.navbar-expand-lg {
            background: var(--primary-gradient) !important;
            background-color: transparent !important;
            padding: 0.5rem 1rem !important;
            margin-bottom: 1rem !important;
            height: 56px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            border: none !important;
        }

        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.9);
        }

        .navbar .user-info {
            display: flex;
            align-items: center;
        }

        .navbar .text-white {
            color: white !important;
            margin-right: 15px;
            font-weight: 500;
        }

        .navbar .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: var(--transition);
            font-weight: 500;
        }

        .navbar .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-1px);
            color: white;
        }

        .navbar .btn-outline-light.active {
            background-color: rgba(255, 255, 255, 0.3);
            border-color: white;
            color: white;
        }
        
        .main-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-title {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-thumbnail {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .file-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .video-thumbnail {
            width: 100px;
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: transform 0.2s ease;
            background-color: #f8f9fa;
        }

        .video-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #e9ecef;
        }

        .file-address {
            word-break: break-all;
            font-size: 0.9rem;
            color: #0d6efd;
        }
        
        .action-buttons .btn {
            margin-right: 5px;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 50px;
        }
        
        .image-count {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .empty-message {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-style: italic;
        }
        
        .copy-success {
            color: #198754;
            font-size: 0.8rem;
            margin-left: 5px;
            display: none;
        }
        
        /* 使用状态徽章样式 */
        .usage-badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
            margin-right: 0.5rem;
        }
        
        .usage-badge.in-use {
            color: #fff;
            background-color: #198754;
        }
        
        .usage-badge.not-used {
            color: #fff;
            background-color: #6c757d;
        }
        
        .reference-info-btn {
            cursor: pointer;
            font-size: 0.9rem;
            color: #0d6efd;
            margin-left: 0.25rem;
        }
        
        .reference-info-btn:hover {
            color: #0a58ca;
        }
        
        .reference-item {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        
        .reference-icon {
            margin-right: 0.5rem;
        }
        
        /* 图片预览模态框样式 */
        .image-preview-modal .modal-dialog {
            max-width: 90%;
            margin: 1.75rem auto;
            transition: transform 0.3s ease-out;
        }
        
        .image-preview-modal .modal-content {
            background-color: rgba(0, 0, 0, 0.85);
            border: none;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        }
        
        .image-preview-modal .modal-header {
            border-bottom: none;
            padding: 0.5rem 1rem;
            background-color: rgba(0, 0, 0, 0.7);
        }
        
        .image-preview-modal .modal-title {
            color: #fff;
            font-size: 1rem;
            max-width: 80%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .image-preview-modal .btn-close {
            color: #fff;
            opacity: 0.8;
            filter: invert(1) grayscale(100%) brightness(200%);
        }
        
        .image-preview-modal .modal-body {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            position: relative;
            height: 70vh;
            overflow: hidden;
        }
        
        .image-preview-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            user-select: none; /* 防止拖动时选中图片 */
        }
        
        .image-preview-modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.2s ease;
            transform-origin: center center; /* 确保缩放始终以中心为原点 */
            user-drag: none; /* 防止浏览器默认的图片拖动行为 */
            -webkit-user-drag: none;
            transform: translate(0, 0) scale(1) rotate(0deg); /* 初始变换状态 */
        }
        
        .image-preview-modal .modal-footer {
            border-top: none;
            padding: 0.5rem 1rem;
            justify-content: space-between;
            background-color: rgba(0, 0, 0, 0.7);
        }
        
        .image-preview-modal .image-info {
            color: #fff;
            font-size: 0.9rem;
        }
        
        .image-preview-nav {
            position: absolute;
            top: 50%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            transform: translateY(-50%);
            z-index: 1030;
            padding: 0 20px;
        }
        
        .image-preview-nav button {
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0.7;
            transition: opacity 0.3s, background-color 0.3s;
        }
        
        .image-preview-nav button:hover {
            opacity: 1;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .image-preview-counter {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            z-index: 1030;
        }
        
        .image-toolbar {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 20px;
            padding: 5px 10px;
            display: flex;
            gap: 10px;
            z-index: 1030;
        }
        
        .image-toolbar button {
            background: none;
            border: none;
            color: white;
            font-size: 1.1rem;
            opacity: 0.8;
            transition: opacity 0.3s, transform 0.3s;
            cursor: pointer;
            padding: 5px;
        }
        
        .image-toolbar button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        
        .image-preview-modal .zoom-level {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1030;
        }
        
        .image-preview-modal .zoom-level.show {
            opacity: 1;
        }
        
        /* 移动设备样式优化 */
        @media (max-width: 768px) {
            .image-preview-modal .modal-dialog {
                max-width: 100%;
                margin: 0;
                min-height: 100vh;
            }

            .image-preview-modal .modal-content {
                min-height: 100vh;
            }

            .image-preview-nav button {
                width: 36px;
                height: 36px;
                font-size: 1rem;
            }

            .image-toolbar {
                bottom: 60px;
            }
        }

        /* 素材管理样式 */
        .material-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 1rem;
            position: relative;
        }

        .material-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .material-card-header {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .material-card:hover .material-card-header {
            opacity: 1;
        }

        .material-actions {
            display: flex;
            gap: 4px;
        }

        .material-actions .btn {
            padding: 4px 6px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .material-preview {
            width: 100%;
            height: 120px;
            object-fit: cover;
            background-color: #f8f9fa;
        }

        .material-info {
            padding: 0.75rem;
            background-color: white;
        }

        .material-name {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .material-meta {
            font-size: 0.75rem;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .material-type-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
        }

        .material-type-decoration {
            background-color: #e7f3ff;
            color: #0066cc;
        }

        .material-type-border {
            background-color: #fff3e0;
            color: #e65100;
        }

        .category-item {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .category-item:hover {
            background-color: #f8f9fa;
        }

        .category-item.active {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }

        .category-count {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* HTML代码管理样式 */
        .html-code-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 1rem;
            position: relative;
            background: white;
        }

        .html-code-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .html-code-card-header {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .html-code-card:hover .html-code-card-header {
            opacity: 1;
        }

        .html-code-actions {
            display: flex;
            gap: 4px;
        }

        .html-code-actions .btn {
            padding: 4px 6px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .html-code-preview {
            width: 100%;
            height: 120px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            overflow: hidden;
            position: relative;
        }

        .html-code-preview iframe {
            width: 100%;
            height: 100%;
            border: none;
            transform: scale(0.8);
            transform-origin: top left;
        }

        .html-code-info {
            padding: 0.75rem;
            background-color: white;
        }

        .html-code-title {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .html-code-description {
            font-size: 0.75rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .html-code-meta {
            font-size: 0.75rem;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .html-code-tags {
            display: flex;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .html-code-tag {
            font-size: 0.65rem;
            padding: 0.15rem 0.3rem;
            border-radius: 0.25rem;
            background-color: #e9ecef;
            color: #495057;
        }



        /* 代码编辑器样式 */
        .code-editor {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .code-preview-container {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
            min-height: 200px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <!-- 引入导航条片段 -->
    <nav th:replace="fragments/navbar :: navbar('胜大科技智联管理系统 - 文件管理', 'fileManager', true, true, true, null)"></nav>
    
    <div class="main-container">
        <div class="page-title">
            <h2><i class="bi bi-images"></i> 文件管理</h2>
            <div>
                <span class="file-count" id="fileCount">共 0 个文件</span>
                <button class="btn btn-danger" id="deleteUnusedBtn" title="删除所有未使用的图片">
                    <i class="bi bi-trash"></i> 删除未使用图片
                </button>
                <button class="btn btn-primary" id="refreshBtn">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 标签页导航 -->
        <ul class="nav nav-tabs mb-3" id="fileManagerTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="images-tab" data-bs-toggle="tab" data-bs-target="#images-panel" type="button" role="tab">
                    <i class="bi bi-images"></i> 图片文件
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="videos-tab" data-bs-toggle="tab" data-bs-target="#videos-panel" type="button" role="tab">
                    <i class="bi bi-play-circle"></i> 视频文件
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials-panel" type="button" role="tab">
                    <i class="bi bi-stars"></i> 素材管理
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="html-codes-tab" data-bs-toggle="tab" data-bs-target="#html-codes-panel" type="button" role="tab">
                    <i class="bi bi-code-slash"></i> HTML代码
                </button>
            </li>
        </ul>

        <!-- 标签页内容 -->
        <div class="tab-content" id="fileManagerTabContent">
            <!-- 图片文件面板 -->
            <div class="tab-pane fade show active" id="images-panel" role="tabpanel">
                <div class="table-responsive" id="filesTableContainer">
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <span class="ms-2">加载图片中...</span>
            </div>
            
            <div class="empty-message" id="emptyMessage" style="display: none;">
                <i class="bi bi-exclamation-circle fs-1"></i>
                <p class="mt-3">没有找到图片文件</p>
            </div>
            
            <table class="table table-hover" id="filesTable" style="display: none;">
                <thead class="table-light">
                    <tr>
                        <th scope="col" style="width: 120px;">预览</th>
                        <th scope="col">文件名</th>
                        <th scope="col">文件地址</th>
                        <th scope="col" style="width: 100px;">大小</th>
                        <th scope="col" style="width: 120px;">使用状态</th>
                        <th scope="col" style="width: 180px;">操作</th>
                    </tr>
                </thead>
                <tbody id="filesTableBody">
                    <!-- 文件列表将动态加载 -->
                </tbody>
            </table>
                </div>
            </div>

            <!-- 视频文件面板 -->
            <div class="tab-pane fade" id="videos-panel" role="tabpanel">
                <!-- 视频上传按钮 -->
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="document.getElementById('videoFile').click()">
                        <i class="bi bi-upload"></i> 上传视频
                    </button>
                </div>

                <div class="table-responsive" id="videosTableContainer">
                    <div class="loading-spinner" id="videosLoadingSpinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="ms-2">加载视频中...</span>
                    </div>

                    <div class="empty-message" id="videosEmptyMessage" style="display: none;">
                        <i class="bi bi-play-circle fs-1"></i>
                        <p>暂无视频文件</p>
                        <button class="btn btn-primary" onclick="document.getElementById('videoFile').click()">
                            <i class="bi bi-upload"></i> 上传视频
                        </button>
                    </div>

                    <table class="table table-hover" id="videosTable" style="display: none;">
                        <thead>
                            <tr>
                                <th width="120">预览</th>
                                <th>文件名</th>
                                <th>文件地址</th>
                                <th width="100">文件大小</th>
                                <th width="120">使用状态</th>
                                <th width="150">修改时间</th>
                                <th width="100">操作</th>
                            </tr>
                        </thead>
                        <tbody id="videosTableBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 素材管理面板 -->
            <div class="tab-pane fade" id="materials-panel" role="tabpanel">
                <div class="row">
                    <!-- 左侧分类管理 -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="bi bi-folder"></i> 素材分类</h6>
                                <button class="btn btn-sm btn-primary" id="addCategoryBtn">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush" id="categoryList">
                                    <!-- 分类列表将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧素材文件 -->
                    <div class="col-md-9">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0" id="materialsPanelTitle">
                                    <i class="bi bi-images"></i> 素材文件
                                </h6>
                                <div>
                                    <button class="btn btn-sm btn-success" id="uploadMaterialBtn">
                                        <i class="bi bi-upload"></i> 上传素材
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="loading-spinner" id="materialsLoadingSpinner" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2">加载素材中...</span>
                                </div>

                                <div class="empty-message" id="materialsEmptyMessage" style="display: none;">
                                    <i class="bi bi-exclamation-circle fs-1"></i>
                                    <p class="mt-3">没有找到素材文件</p>
                                </div>

                                <div class="row" id="materialsGrid">
                                    <!-- 素材网格将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HTML代码管理面板 -->
            <div class="tab-pane fade" id="html-codes-panel" role="tabpanel">
                <div class="row">
                    <!-- 左侧分类管理 -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="bi bi-folder"></i> 代码分类</h6>
                                <button class="btn btn-sm btn-primary" id="addHtmlCategoryBtn">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush" id="htmlCategoryList">
                                    <!-- HTML代码分类列表将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧HTML代码片段 -->
                    <div class="col-md-9">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0" id="htmlCodesPanelTitle">
                                    <i class="bi bi-code-slash"></i> HTML代码片段
                                </h6>
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary" id="searchHtmlCodesBtn">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                    <button class="btn btn-sm btn-success" id="addHtmlCodeBtn">
                                        <i class="bi bi-plus"></i> 添加代码
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="loading-spinner" id="htmlCodesLoadingSpinner" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2">加载HTML代码中...</span>
                                </div>

                                <div class="empty-message" id="htmlCodesEmptyMessage" style="display: none;">
                                    <i class="bi bi-exclamation-circle fs-1"></i>
                                    <p class="mt-3">没有找到HTML代码片段</p>
                                </div>

                                <div class="row" id="htmlCodesGrid">
                                    <!-- HTML代码网格将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加分类模态框 -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加素材分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="addCategoryForm">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">分类名称</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="categoryType" class="form-label">分类类型</label>
                            <select class="form-select" id="categoryType" required>
                                <option value="">请选择类型</option>
                                <option value="decoration">装饰素材</option>
                                <option value="border">边框素材</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">分类描述</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveCategoryBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传素材模态框 -->
    <div class="modal fade" id="uploadMaterialModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传素材文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadMaterialForm">
                        <!-- 拖拽上传区域 -->
                        <div id="dropZone" class="border border-2 border-dashed rounded p-4 text-center mb-3" style="border-color: #dee2e6; background-color: #f8f9fa;">
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <p class="mb-2">拖拽文件到此处或点击选择文件</p>
                            <p class="text-muted small">
                                支持 JPG、PNG、GIF、WEBP、APNG 格式，支持批量上传<br>
                                <strong>多选提示：</strong>按住 <kbd>Ctrl</kbd> 键（Windows）或 <kbd>Cmd</kbd> 键（Mac）可选择多个文件
                            </p>
                            <input type="file" class="d-none" id="materialFile" accept="image/*" multiple>
                            <button type="button" class="btn btn-outline-primary" id="selectFilesBtn">选择文件</button>
                        </div>

                        <!-- 文件列表 -->
                        <div id="fileListContainer" class="mb-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">已选择的文件</label>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="addMoreFilesBtn">
                                    <i class="bi bi-plus-circle"></i> 继续添加文件
                                </button>
                            </div>
                            <div id="fileList" class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                                <!-- 文件列表将动态生成 -->
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="materialCategory" class="form-label">所属分类</label>
                            <select class="form-select" id="materialCategory">
                                <option value="">无分类</option>
                                <!-- 分类选项将动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isDefaultMaterial">
                                <label class="form-check-label" for="isDefaultMaterial">
                                    设为默认素材
                                </label>
                            </div>
                        </div>

                        <!-- 上传进度 -->
                        <div id="uploadProgress" class="mb-3" style="display: none;">
                            <label class="form-label">上传进度</label>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="uploadStatus" class="mt-2 small text-muted"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="uploadMaterialSubmitBtn" disabled>上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑素材模态框 -->
    <div class="modal fade" id="editMaterialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑素材文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="editMaterialForm">
                        <input type="hidden" id="editMaterialId">
                        <div class="mb-3">
                            <label for="editMaterialName" class="form-label">文件名称</label>
                            <input type="text" class="form-control" id="editMaterialName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editMaterialCategory" class="form-label">所属分类</label>
                            <select class="form-select" id="editMaterialCategory">
                                <option value="">无分类</option>
                                <!-- 分类选项将动态加载 -->
                            </select>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsDefaultMaterial">
                            <label class="form-check-label" for="editIsDefaultMaterial">
                                设为默认素材
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="updateMaterialSubmitBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑分类模态框 -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑素材分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="editCategoryForm">
                        <input type="hidden" id="editCategoryId">
                        <div class="mb-3">
                            <label for="editCategoryName" class="form-label">分类名称</label>
                            <input type="text" class="form-control" id="editCategoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editCategoryType" class="form-label">分类类型</label>
                            <select class="form-select" id="editCategoryType" required disabled>
                                <option value="">请选择类型</option>
                                <option value="decoration">装饰素材</option>
                                <option value="border">边框素材</option>
                            </select>
                            <div class="form-text">分类类型创建后不可修改</div>
                        </div>
                        <div class="mb-3">
                            <label for="editCategoryDescription" class="form-label">分类描述</label>
                            <textarea class="form-control" id="editCategoryDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="updateCategorySubmitBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除此文件吗？此操作无法撤销。</p>
                    <div class="d-flex align-items-center">
                        <img id="deleteImagePreview" src="" alt="预览图" style="max-width: 100px; max-height: 100px; object-fit: cover; margin-right: 15px;">
                        <div>
                            <p class="mb-1" id="deleteFileName">filename.jpg</p>
                            <small id="deleteFileSize" class="text-muted">1.2MB</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除文件</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引用信息模态框 -->
    <div class="modal fade" id="referenceInfoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片引用详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>此图片被以下组件使用：</p>
                    <ul class="list-group" id="referencesList">
                        <!-- 引用列表将动态加载 -->
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图片预览模态框 -->
    <div class="modal fade image-preview-modal" id="imagePreviewModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewImageName">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="image-preview-container">
                        <img id="previewImage" src="" alt="图片预览">
                    </div>
                    
                    <!-- 导航按钮 -->
                    <div class="image-preview-nav">
                        <button type="button" id="prevImageBtn">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <button type="button" id="nextImageBtn">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                    
                    <!-- 图片计数 -->
                    <div class="image-preview-counter" id="imageCounter">1/1</div>
                    
                    <!-- 图片缩放指示器 -->
                    <div class="zoom-level" id="zoomLevel">100%</div>
                    
                    <!-- 图片工具栏 -->
                    <div class="image-toolbar">
                        <button type="button" id="zoomInBtn" title="放大">
                            <i class="bi bi-zoom-in"></i>
                        </button>
                        <button type="button" id="zoomOutBtn" title="缩小">
                            <i class="bi bi-zoom-out"></i>
                        </button>
                        <button type="button" id="resetZoomBtn" title="重置大小">
                            <i class="bi bi-aspect-ratio"></i>
                        </button>
                        <button type="button" id="rotateBtn" title="旋转">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button type="button" id="downloadBtn" title="下载">
                            <i class="bi bi-download"></i>
                        </button>
                        <button type="button" id="copyUrlBtn" title="复制地址">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="image-info">
                        <span id="previewImageSize">--</span>
                        <span id="previewImageDimensions">--</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-light" id="openInNewTab">
                        <i class="bi bi-box-arrow-up-right"></i> 新窗口打开
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量删除未使用图片确认弹窗 -->
    <div class="modal fade" id="deleteUnusedConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认批量删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <strong>警告：</strong> 此操作将删除所有未被使用的图片文件。此操作无法撤销。
                    </div>
                    <p>确定要继续删除所有未使用的图片吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteUnusedBtn">删除所有未使用图片</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量删除结果模态框 -->
    <div class="modal fade" id="deleteResultModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">删除结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div id="deleteResultContent">
                        <!-- 删除结果将动态添加到这里 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- HTML代码分类管理模态框 -->
    <div class="modal fade" id="addHtmlCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加HTML代码分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="addHtmlCategoryForm">
                        <div class="mb-3">
                            <label for="htmlCategoryName" class="form-label">分类名称</label>
                            <input type="text" class="form-control" id="htmlCategoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="htmlCategoryDescription" class="form-label">分类描述</label>
                            <textarea class="form-control" id="htmlCategoryDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveHtmlCategoryBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑HTML代码片段模态框 -->
    <div class="modal fade" id="htmlCodeModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="htmlCodeModalTitle">添加HTML代码片段</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="htmlCodeForm">
                        <input type="hidden" id="htmlCodeId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="htmlCodeTitle" class="form-label">标题</label>
                                    <input type="text" class="form-control" id="htmlCodeTitle" required>
                                </div>
                                <div class="mb-3">
                                    <label for="htmlCodeDescription" class="form-label">描述</label>
                                    <textarea class="form-control" id="htmlCodeDescription" rows="2"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="htmlCodeCategory" class="form-label">分类</label>
                                    <select class="form-select" id="htmlCodeCategory">
                                        <option value="">无分类</option>
                                        <!-- 分类选项将动态加载 -->
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="htmlCodeTags" class="form-label">标签</label>
                                    <input type="text" class="form-control" id="htmlCodeTags" placeholder="用逗号分隔多个标签">
                                    <div class="form-text">例如：按钮,渐变,现代</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="htmlCodeContent" class="form-label">HTML代码</label>
                                    <textarea class="form-control code-editor" id="htmlCodeContent" rows="10" required placeholder="请输入HTML代码..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">实时预览</label>
                                    <div class="code-preview-container" id="htmlCodePreview">
                                        <p class="text-muted text-center">在上方输入HTML代码查看预览效果</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveHtmlCodeBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- HTML代码预览模态框 -->
    <div class="modal fade" id="htmlCodePreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalTitle">HTML代码预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label mb-0">预览效果</label>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="copyHtmlCodeBtn">
                                    <i class="bi bi-clipboard"></i> 复制代码
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="openInNewWindowBtn">
                                    <i class="bi bi-box-arrow-up-right"></i> 新窗口打开
                                </button>
                            </div>
                        </div>
                        <div class="border rounded p-3" id="fullPreviewContainer" style="min-height: 200px;">
                            <!-- 预览内容将在这里显示 -->
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">HTML代码</label>
                        <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"><code id="htmlCodeDisplay"></code></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索HTML代码模态框 -->
    <div class="modal fade" id="searchHtmlCodesModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">搜索HTML代码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="searchKeyword" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="searchKeyword" placeholder="输入标题、描述或标签进行搜索">
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="performSearchBtn">搜索</button>
                        <button type="button" class="btn btn-outline-secondary" id="clearSearchBtn">清除搜索</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局函数：复制文件URL到剪贴板
        function copyToClipboard(text, successElementId) {
            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功消息
                const successElement = document.getElementById(successElementId);
                if (successElement) {
                    successElement.style.display = 'inline';
                    setTimeout(() => {
                        successElement.style.display = 'none';
                    }, 2000);
                }
            }).catch(err => {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制地址');
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 获取元素
            const filesTable = document.getElementById('filesTable');
            const filesTableBody = document.getElementById('filesTableBody');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const emptyMessage = document.getElementById('emptyMessage');
            const imageCount = document.getElementById('imageCount');
            const refreshBtn = document.getElementById('refreshBtn');
            
            // 删除确认框元素
            const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            const deleteImagePreview = document.getElementById('deleteImagePreview');
            const deleteFileName = document.getElementById('deleteFileName');
            const deleteFileSize = document.getElementById('deleteFileSize');
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            
            // 引用信息模态框
            const referenceInfoModal = new bootstrap.Modal(document.getElementById('referenceInfoModal'));
            
            // 图片预览模态框相关元素
            const imagePreviewModal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            const previewImage = document.getElementById('previewImage');
            const previewImageName = document.getElementById('previewImageName');
            const previewImageSize = document.getElementById('previewImageSize');
            const previewImageDimensions = document.getElementById('previewImageDimensions');
            const openInNewTabBtn = document.getElementById('openInNewTab');
            const prevImageBtn = document.getElementById('prevImageBtn');
            const nextImageBtn = document.getElementById('nextImageBtn');
            const imageCounter = document.getElementById('imageCounter');
            const zoomInBtn = document.getElementById('zoomInBtn');
            const zoomOutBtn = document.getElementById('zoomOutBtn');
            const resetZoomBtn = document.getElementById('resetZoomBtn');
            const rotateBtn = document.getElementById('rotateBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const copyUrlBtn = document.getElementById('copyUrlBtn');
            const zoomLevel = document.getElementById('zoomLevel');
            
            // 批量删除未使用图片相关元素
            const deleteUnusedBtn = document.getElementById('deleteUnusedBtn');
            const deleteUnusedConfirmModal = new bootstrap.Modal(document.getElementById('deleteUnusedConfirmModal'));
            const confirmDeleteUnusedBtn = document.getElementById('confirmDeleteUnusedBtn');
            const deleteResultModal = new bootstrap.Modal(document.getElementById('deleteResultModal'));
            const deleteResultContent = document.getElementById('deleteResultContent');
            
            let currentDeleteFile = null;
            
            // 图片预览状态变量
            let allImages = [];
            let currentImageIndex = 0;
            let currentZoom = 1;
            let currentRotation = 0;
            // 图片位置变量
            let currentTranslateX = 0;
            let currentTranslateY = 0;
            // 防止滚轮事件频繁触发的节流标志
            let wheelThrottleTimeout = null;
            
            // 加载图片文件列表
            async function loadImageFiles() {
                // 显示加载中
                loadingSpinner.style.display = 'flex';
                filesTable.style.display = 'none';
                emptyMessage.style.display = 'none';
                
                try {
                    const response = await fetch('/api/files/images');
                    if (!response.ok) {
                        throw new Error('获取图片列表失败');
                    }
                    
                    const files = await response.json();
                    
                    // 获取所有图片后保存到全局变量
                    allImages = files;
                    
                    // 更新图片计数
                    document.getElementById('fileCount').textContent = `共 ${files.length} 个图片`;
                    
                    // 计算未使用图片数量并更新删除按钮
                    const unusedImages = files.filter(file => !file.isUsed);
                    if (unusedImages.length > 0) {
                        deleteUnusedBtn.innerHTML = `<i class="bi bi-trash"></i> 删除未使用图片 <span class="badge bg-light text-danger">${unusedImages.length}</span>`;
                        deleteUnusedBtn.disabled = false;
                    } else {
                        deleteUnusedBtn.innerHTML = `<i class="bi bi-trash"></i> 删除未使用图片`;
                        deleteUnusedBtn.disabled = true;
                    }
                    
                    if (files.length === 0) {
                        loadingSpinner.style.display = 'none';
                        emptyMessage.style.display = 'block';
                        return;
                    }
                    
                    // 清空现有表格内容
                    filesTableBody.innerHTML = '';
                    
                    // 填充表格
                    files.forEach(file => {
                        const row = document.createElement('tr');
                        
                        // 预览图
                        let imgCell = document.createElement('td');
                        let img = document.createElement('img');
                        img.src = file.url;
                        img.alt = file.name;
                        img.className = 'file-thumbnail';
                        img.onclick = () => showImagePreview(file);
                        imgCell.appendChild(img);
                        row.appendChild(imgCell);
                        
                        // 文件名
                        let nameCell = document.createElement('td');
                        nameCell.textContent = file.name;
                        row.appendChild(nameCell);
                        
                        // 文件URL
                        let urlCell = document.createElement('td');
                        let urlGroup = document.createElement('div');
                        urlGroup.className = 'd-flex align-items-center';
                        
                        let urlText = document.createElement('span');
                        urlText.className = 'file-address';
                        urlText.textContent = file.url;
                        
                        let copySuccess = document.createElement('span');
                        copySuccess.className = 'copy-success';
                        copySuccess.innerHTML = '<i class="bi bi-check-circle"></i> 已复制';
                        copySuccess.id = `copy-success-${file.name.replace(/[^a-zA-Z0-9]/g, '_')}`;
                        
                        urlGroup.appendChild(urlText);
                        urlGroup.appendChild(copySuccess);
                        urlCell.appendChild(urlGroup);
                        row.appendChild(urlCell);
                        
                        // 文件大小
                        let sizeCell = document.createElement('td');
                        sizeCell.textContent = file.size;
                        row.appendChild(sizeCell);
                        
                        // 使用状态
                        let usageCell = document.createElement('td');
                        
                        // 创建使用状态徽章
                        const badgeSpan = document.createElement('span');
                        if (file.isUsed) {
                            badgeSpan.className = 'usage-badge in-use';
                            badgeSpan.textContent = '使用中';
                            
                            // 添加引用信息按钮
                            if (file.references && file.references.length > 0) {
                                const infoButton = document.createElement('i');
                                infoButton.className = 'bi bi-info-circle reference-info-btn';
                                infoButton.title = '查看引用详情';
                                infoButton.addEventListener('click', () => showReferenceInfo(file));
                                usageCell.appendChild(badgeSpan);
                                usageCell.appendChild(infoButton);
                            } else {
                                usageCell.appendChild(badgeSpan);
                            }
                        } else {
                            badgeSpan.className = 'usage-badge not-used';
                            badgeSpan.textContent = '未使用';
                            usageCell.appendChild(badgeSpan);
                        }
                        
                        row.appendChild(usageCell);
                        
                        // 操作按钮
                        let actionCell = document.createElement('td');
                        actionCell.className = 'action-buttons';
                        
                        let copyBtn = document.createElement('button');
                        copyBtn.className = 'btn btn-sm btn-outline-primary';
                        copyBtn.innerHTML = '<i class="bi bi-clipboard"></i> 复制地址';
                        copyBtn.onclick = () => copyToClipboard(file.url, copySuccess.id);
                        
                        let deleteBtn = document.createElement('button');
                        deleteBtn.className = 'btn btn-sm btn-outline-danger';
                        deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 删除';
                        deleteBtn.onclick = () => showDeleteConfirm(file);
                        
                        // 如果图片正在使用中，添加警告提示
                        if (file.isUsed) {
                            deleteBtn.title = '警告：该图片正在使用中，删除可能会导致引用它的组件显示异常';
                            deleteBtn.classList.add('position-relative');
                            
                            // 添加警告图标
                            const warningBadge = document.createElement('span');
                            warningBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark';
                            warningBadge.innerHTML = '!';
                            warningBadge.style.fontSize = '0.6rem';
                            deleteBtn.appendChild(warningBadge);
                        }
                        
                        actionCell.appendChild(copyBtn);
                        actionCell.appendChild(deleteBtn);
                        row.appendChild(actionCell);
                        
                        filesTableBody.appendChild(row);
                    });
                    
                    // 显示表格
                    loadingSpinner.style.display = 'none';
                    filesTable.style.display = 'table';
                    
                } catch (error) {
                    console.error('加载图片文件失败:', error);
                    loadingSpinner.style.display = 'none';
                    emptyMessage.style.display = 'block';
                    emptyMessage.innerHTML = `
                        <i class="bi bi-exclamation-triangle fs-1 text-danger"></i>
                        <p class="mt-3">加载图片文件时出错: ${error.message}</p>
                    `;
                }
            }
            
            // 显示引用信息
            function showReferenceInfo(file) {
                const referencesList = document.getElementById('referencesList');
                referencesList.innerHTML = '';
                
                if (file.references && file.references.length > 0) {
                    file.references.forEach(ref => {
                        const li = document.createElement('li');
                        li.className = 'list-group-item reference-item';
                        
                        let icon, text;
                        
                        switch(ref.type) {
                            case 'device':
                                icon = '<i class="bi bi-cpu reference-icon"></i>';
                                text = `设备 "${ref.name}" 的图片`;
                                break;
                            case 'topology_device':
                                icon = '<i class="bi bi-diagram-3 reference-icon"></i>';
                                text = `拓扑 "${ref.topology}" 中设备 "${ref.nodeName}" 的图片`;
                                break;
                            case 'topology_image':
                                icon = '<i class="bi bi-image reference-icon"></i>';
                                text = `拓扑 "${ref.topology}" 中的图片 "${ref.title}"`;
                                break;
                            case 'system':
                                icon = '<i class="bi bi-gear reference-icon"></i>';
                                text = `${ref.page} "${ref.description}"`;
                                break;
                            case 'bi_dashboard':
                                icon = '<i class="bi bi-bar-chart-line reference-icon"></i>';
                                text = `BI大屏 (ID: ${ref.dashboardId}) 中的图片组件`;
                                break;
                            case 'bi_dashboard_background':
                                icon = '<i class="bi bi-palette reference-icon"></i>';
                                text = `BI大屏 "${ref.dashboardName}" 的画布背景图片`;
                                break;
                            default:
                                icon = '<i class="bi bi-link-45deg reference-icon"></i>';
                                text = '未知引用';
                        }
                        
                        li.innerHTML = icon + text;
                        referencesList.appendChild(li);
                    });
                } else {
                    const li = document.createElement('li');
                    li.className = 'list-group-item reference-item text-muted';
                    li.textContent = '没有找到引用信息';
                    referencesList.appendChild(li);
                }
                
                referenceInfoModal.show();
            }
            
            // 显示删除确认对话框
            function showDeleteConfirm(file) {
                deleteImagePreview.src = file.url;
                deleteFileName.textContent = file.name;
                deleteFileSize.textContent = file.size;
                currentDeleteFile = file;
                deleteConfirmModal.show();
            }
            
            // 确认删除按钮点击事件
            confirmDeleteBtn.addEventListener('click', async () => {
                if (!currentDeleteFile) return;
                
                // 添加警告确认(如果图片在使用中)
                if (currentDeleteFile.isUsed) {
                    const doubleConfirm = confirm(`警告：此图片正在被${currentDeleteFile.references.length}个组件使用中！\n删除此图片可能会导致这些组件显示异常。\n\n您确定要继续删除吗？`);
                    if (!doubleConfirm) {
                        return;
                    }
                }
                
                confirmDeleteBtn.disabled = true;
                confirmDeleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...';
                
                try {
                    const response = await fetch(`/api/files/images/${encodeURIComponent(currentDeleteFile.name)}`, {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        // 删除成功，关闭对话框并刷新列表
                        deleteConfirmModal.hide();
                        loadImageFiles();
                    } else {
                        alert(`删除失败: ${result.error || '未知错误'}`);
                    }
                } catch (error) {
                    console.error('删除文件时出错:', error);
                    alert(`删除失败: ${error.message}`);
                } finally {
                    confirmDeleteBtn.disabled = false;
                    confirmDeleteBtn.innerHTML = '删除文件';
                }
            });
            
            // 显示图片预览
            function showImagePreview(file) {
                // 找到当前图片在数组中的索引
                currentImageIndex = allImages.findIndex(img => img.name === file.name);
                
                // 重置缩放和旋转
                resetImageTransform();
                
                // 更新图片和信息
                updateImagePreview();
                
                // 显示模态框
                imagePreviewModal.show();
                
                // 更新导航按钮状态
                updateNavigationButtons();
                
                // 添加鼠标滚轮事件监听
                setupMouseWheelZoom();
            }
            
            // 更新图片预览内容
            function updateImagePreview() {
                const file = allImages[currentImageIndex];
                
                // 设置图片源
                previewImage.src = file.url;
                
                // 更新文件信息
                previewImageName.textContent = file.name;
                previewImageSize.textContent = file.size;
                
                // 清除之前的尺寸信息
                previewImageDimensions.textContent = '';
                
                // 设置新窗口打开按钮的URL
                openInNewTabBtn.onclick = () => {
                    window.open(file.url, '_blank');
                };
                
                // 设置下载按钮
                downloadBtn.onclick = () => {
                    // 创建临时链接触发下载
                    const a = document.createElement('a');
                    a.href = file.url;
                    a.download = file.name;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                };
                
                // 设置复制URL按钮
                copyUrlBtn.onclick = () => {
                    copyToClipboard(file.url, null);
                    showZoomLevel('URL已复制!', 1000);
                };
                
                // 更新图片计数
                imageCounter.textContent = `${currentImageIndex + 1}/${allImages.length}`;
                
                // 加载完成后获取图片的原始尺寸
                previewImage.onload = function() {
                    previewImageDimensions.textContent = ` | ${this.naturalWidth} × ${this.naturalHeight}`;
                };
            }
            
            // 更新导航按钮状态
            function updateNavigationButtons() {
                // 在只有一张图片的情况下隐藏导航按钮
                const showNavigation = allImages.length > 1;
                prevImageBtn.style.display = showNavigation ? 'flex' : 'none';
                nextImageBtn.style.display = showNavigation ? 'flex' : 'none';
                imageCounter.style.display = showNavigation ? 'block' : 'none';
                
                // 循环导航，始终启用前后按钮
                prevImageBtn.disabled = false;
                nextImageBtn.disabled = false;
            }
            
            // 重置图片变换
            function resetImageTransform() {
                currentZoom = 1;
                currentRotation = 0;
                currentTranslateX = 0;
                currentTranslateY = 0;
                applyImageTransform();
            }
            
            // 应用图片变换
            function applyImageTransform() {
                previewImage.style.transform = `translate(${currentTranslateX}px, ${currentTranslateY}px) scale(${currentZoom}) rotate(${currentRotation}deg)`;
            }
            
            // 显示缩放级别
            function showZoomLevel(message, timeout = 2000) {
                zoomLevel.textContent = message || `${Math.round(currentZoom * 100)}%`;
                zoomLevel.classList.add('show');
                
                // 自动隐藏缩放指示器
                clearTimeout(window.zoomLevelTimeout);
                window.zoomLevelTimeout = setTimeout(() => {
                    zoomLevel.classList.remove('show');
                }, timeout);
            }
            
            // 前一张图片
            prevImageBtn.addEventListener('click', function() {
                currentImageIndex = (currentImageIndex - 1 + allImages.length) % allImages.length;
                resetImageTransform();
                updateImagePreview();
            });
            
            // 后一张图片
            nextImageBtn.addEventListener('click', function() {
                currentImageIndex = (currentImageIndex + 1) % allImages.length;
                resetImageTransform();
                updateImagePreview();
            });
            
            // 图片放大
            zoomInBtn.addEventListener('click', function() {
                currentZoom = Math.min(currentZoom + 0.25, 3);
                applyImageTransform();
                showZoomLevel();
            });
            
            // 图片缩小
            zoomOutBtn.addEventListener('click', function() {
                currentZoom = Math.max(currentZoom - 0.25, 0.5);
                applyImageTransform();
                showZoomLevel();
            });
            
            // 重置图片大小
            resetZoomBtn.addEventListener('click', function() {
                resetImageTransform();
                showZoomLevel();
            });
            
            // 旋转图片
            rotateBtn.addEventListener('click', function() {
                currentRotation = (currentRotation + 90) % 360;
                applyImageTransform();
            });
            
            // 添加键盘导航支持
            document.addEventListener('keydown', function(e) {
                // 只有在模态框打开时才处理键盘事件
                if (document.getElementById('imagePreviewModal').classList.contains('show')) {
                    switch(e.key) {
                        case 'ArrowLeft':
                            prevImageBtn.click();
                            break;
                        case 'ArrowRight':
                            nextImageBtn.click();
                            break;
                        case 'Escape':
                            imagePreviewModal.hide();
                            break;
                        case '+':
                        case '=':
                            zoomInBtn.click();
                            break;
                        case '-':
                            zoomOutBtn.click();
                            break;
                        case '0':
                            resetZoomBtn.click();
                            break;
                        case 'r':
                            rotateBtn.click();
                            break;
                    }
                }
            });
            
            // 处理鼠标滚轮事件
            function handleMouseWheel(e) {
                // 防止默认行为（页面滚动）
                e.preventDefault();
                
                // 使用节流机制防止过于频繁的调用
                if (wheelThrottleTimeout) return;
                
                wheelThrottleTimeout = setTimeout(() => {
                    wheelThrottleTimeout = null;
                }, 50); // 50ms的节流时间
                
                // 获取图片元素和容器
                const img = document.getElementById('previewImage');
                const container = document.querySelector('.image-preview-container');
                
                // 获取图片的当前尺寸和位置信息
                const imgRect = img.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                
                // 计算鼠标在图片上的相对位置（0-1范围）
                const mouseX = (e.clientX - imgRect.left) / imgRect.width;
                const mouseY = (e.clientY - imgRect.top) / imgRect.height;
                
                // 确定滚动方向并调整缩放级别
                // deltaY > 0 表示向下滚动（缩小），deltaY < 0 表示向上滚动（放大）
                const direction = e.deltaY < 0 ? 1 : -1;
                
                // 存储旧的缩放级别
                const oldZoom = currentZoom;
                
                // 根据滚动方向调整缩放级别，放大/缩小的步长为0.1
                const newZoom = Math.max(0.5, Math.min(3, currentZoom + direction * 0.1));
                
                // 如果缩放级别没有变化，不进行后续操作
                if (newZoom === currentZoom) return;
                
                // 以鼠标位置为中心计算新的平移
                if (mouseX >= 0 && mouseX <= 1 && mouseY >= 0 && mouseY <= 1) {
                    // 计算以鼠标位置为中心的缩放偏移
                    const imgCenterX = imgRect.width / 2;
                    const imgCenterY = imgRect.height / 2;
                    const mouseOffsetX = (mouseX - 0.5) * imgRect.width;
                    const mouseOffsetY = (mouseY - 0.5) * imgRect.height;
                    
                    // 基于缩放比例差异计算新的平移值
                    const zoomRatio = newZoom / oldZoom;
                    
                    // 调整平移以保持鼠标位置不变
                    currentTranslateX += mouseOffsetX - mouseOffsetX * zoomRatio;
                    currentTranslateY += mouseOffsetY - mouseOffsetY * zoomRatio;
                }
                
                // 更新当前缩放级别
                currentZoom = newZoom;
                
                // 应用变换
                applyImageTransform();
                
                // 显示缩放级别
                showZoomLevel();
            }
            
            // 添加图片拖动功能
            function setupImageDrag() {
                const container = document.querySelector('.image-preview-container');
                const img = document.getElementById('previewImage');
                
                let isDragging = false;
                let startX = 0;
                let startY = 0;
                
                // 触摸相关变量
                let lastTouchDistance = 0;
                
                // 鼠标按下事件
                img.addEventListener('mousedown', function(e) {
                    // 仅当图片被放大时才允许拖动
                    if (currentZoom > 1) {
                        isDragging = true;
                        startX = e.clientX - currentTranslateX;
                        startY = e.clientY - currentTranslateY;
                        img.style.cursor = 'grabbing';
                    }
                });
                
                // 鼠标移动事件
                container.addEventListener('mousemove', function(e) {
                    if (isDragging) {
                        currentTranslateX = e.clientX - startX;
                        currentTranslateY = e.clientY - startY;
                        applyImageTransform();
                    }
                });
                
                // 鼠标释放事件 - 绑定到文档以确保鼠标在容器外释放时也能捕获
                document.addEventListener('mouseup', function() {
                    if (isDragging) {
                        isDragging = false;
                        img.style.cursor = currentZoom > 1 ? 'grab' : 'default';
                    }
                });
                
                // 鼠标进入图片时设置合适的光标
                img.addEventListener('mouseenter', function() {
                    img.style.cursor = currentZoom > 1 ? 'grab' : 'default';
                });
                
                // --- 触摸设备支持 ---
                
                // 触摸开始事件
                container.addEventListener('touchstart', function(e) {
                    e.preventDefault(); // 防止默认行为
                    
                    // 单指触摸 - 拖动
                    if (e.touches.length === 1 && currentZoom > 1) {
                        isDragging = true;
                        startX = e.touches[0].clientX - currentTranslateX;
                        startY = e.touches[0].clientY - currentTranslateY;
                    }
                    // 双指触摸 - 缩放
                    else if (e.touches.length === 2) {
                        // 计算两个触摸点之间的距离
                        const touch1 = e.touches[0];
                        const touch2 = e.touches[1];
                        lastTouchDistance = Math.hypot(
                            touch2.clientX - touch1.clientX,
                            touch2.clientY - touch1.clientY
                        );
                    }
                }, { passive: false });
                
                // 触摸移动事件
                container.addEventListener('touchmove', function(e) {
                    e.preventDefault(); // 防止默认行为
                    
                    // 单指触摸 - 拖动
                    if (e.touches.length === 1 && isDragging) {
                        currentTranslateX = e.touches[0].clientX - startX;
                        currentTranslateY = e.touches[0].clientY - startY;
                        applyImageTransform();
                    }
                    // 双指触摸 - 缩放
                    else if (e.touches.length === 2) {
                        const touch1 = e.touches[0];
                        const touch2 = e.touches[1];
                        
                        // 计算当前两点之间的距离
                        const currentDistance = Math.hypot(
                            touch2.clientX - touch1.clientX,
                            touch2.clientY - touch1.clientY
                        );
                        
                        // 计算距离差异比率
                        if (lastTouchDistance > 0) {
                            const distanceRatio = currentDistance / lastTouchDistance;
                            
                            // 计算新的缩放值并限制在合理范围内
                            const newZoom = Math.max(0.5, Math.min(3, currentZoom * distanceRatio));
                            
                            // 只有当缩放值有明显变化时才应用
                            if (Math.abs(newZoom - currentZoom) > 0.01) {
                                // 计算缩放中心点
                                const centerX = (touch1.clientX + touch2.clientX) / 2;
                                const centerY = (touch1.clientY + touch2.clientY) / 2;
                                
                                // 获取图片位置
                                const imgRect = img.getBoundingClientRect();
                                
                                // 计算中心点相对于图片的位置
                                const relativeX = (centerX - imgRect.left) / imgRect.width;
                                const relativeY = (centerY - imgRect.top) / imgRect.height;
                                
                                // 以手指中心为中心进行缩放
                                if (relativeX >= 0 && relativeX <= 1 && relativeY >= 0 && relativeY <= 1) {
                                    const oldZoom = currentZoom;
                                    currentZoom = newZoom;
                                    
                                    // 计算相对于图片中心的偏移
                                    const mouseOffsetX = (relativeX - 0.5) * imgRect.width;
                                    const mouseOffsetY = (relativeY - 0.5) * imgRect.height;
                                    
                                    // 计算平移调整
                                    const zoomRatio = newZoom / oldZoom;
                                    currentTranslateX += mouseOffsetX - mouseOffsetX * zoomRatio;
                                    currentTranslateY += mouseOffsetY - mouseOffsetY * zoomRatio;
                                    
                                    // 应用变换并显示缩放级别
                                    applyImageTransform();
                                    showZoomLevel();
                                }
                            }
                        }
                        
                        // 更新上次距离
                        lastTouchDistance = currentDistance;
                    }
                }, { passive: false });
                
                // 触摸结束事件
                container.addEventListener('touchend', function(e) {
                    if (isDragging && e.touches.length === 0) {
                        isDragging = false;
                    }
                    if (e.touches.length < 2) {
                        lastTouchDistance = 0;
                    }
                });
                
                // 触摸取消事件
                container.addEventListener('touchcancel', function() {
                    isDragging = false;
                    lastTouchDistance = 0;
                });
                
                // 监听缩放变化以更新光标
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            img.style.cursor = currentZoom > 1 ? 'grab' : 'default';
                        }
                    });
                });
                
                observer.observe(img, { attributes: true });
            }
            
            // 设置鼠标滚轮缩放
            function setupMouseWheelZoom() {
                const container = document.querySelector('.image-preview-container');
                
                // 移除之前可能存在的事件监听器，避免重复
                container.removeEventListener('wheel', handleMouseWheel);
                
                // 添加鼠标滚轮事件监听
                container.addEventListener('wheel', handleMouseWheel, { passive: false });
                
                // 设置图片拖动功能
                setupImageDrag();
            }
            
            // 模态框关闭时重置图片和移除事件监听器
            document.getElementById('imagePreviewModal').addEventListener('hidden.bs.modal', function() {
                resetImageTransform();
                
                // 移除鼠标滚轮事件监听器
                const container = document.querySelector('.image-preview-container');
                container.removeEventListener('wheel', handleMouseWheel);
            });
            
            // 显示批量删除未使用图片确认对话框
            deleteUnusedBtn.addEventListener('click', function() {
                deleteUnusedConfirmModal.show();
            });
            
            // 确认批量删除未使用图片
            confirmDeleteUnusedBtn.addEventListener('click', async function() {
                confirmDeleteUnusedBtn.disabled = true;
                confirmDeleteUnusedBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...';
                
                try {
                    const response = await fetch('/api/files/images/unused', {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        // 关闭确认对话框
                        deleteUnusedConfirmModal.hide();
                        
                        // 构建删除结果内容
                        let resultHTML = '';
                        
                        if (result.success) {
                            if (result.deletedCount === 0 && (!result.failedFiles || result.failedFiles.length === 0)) {
                                // 没有找到未使用的图片
                                resultHTML += `<div class="alert alert-info">
                                    <i class="bi bi-info-circle-fill"></i> 没有找到未使用的图片，无需删除。
                                </div>`;
                            } else {
                                resultHTML += `<div class="alert alert-success">
                                    <i class="bi bi-check-circle-fill"></i> 成功删除 ${result.deletedCount} 个未使用的图片文件。
                                </div>`;
                                
                                if (result.deletedFiles && result.deletedFiles.length > 0) {
                                    resultHTML += '<h6>已删除的文件：</h6>';
                                    resultHTML += '<ul class="list-group mb-3">';
                                    result.deletedFiles.forEach(file => {
                                        resultHTML += `<li class="list-group-item"><i class="bi bi-trash"></i> ${file}</li>`;
                                    });
                                    resultHTML += '</ul>';
                                }
                                
                                if (result.failedFiles && result.failedFiles.length > 0) {
                                    resultHTML += '<h6>删除失败的文件：</h6>';
                                    resultHTML += '<ul class="list-group mb-3">';
                                    result.failedFiles.forEach(file => {
                                        resultHTML += `<li class="list-group-item"><i class="bi bi-exclamation-triangle"></i> ${file}</li>`;
                                    });
                                    resultHTML += '</ul>';
                                }
                            }
                        } else {
                            resultHTML = `<div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill"></i> 删除未使用图片失败：${result.error || '未知错误'}
                            </div>`;
                        }
                        
                        // 显示结果
                        deleteResultContent.innerHTML = resultHTML;
                        deleteResultModal.show();
                        
                        // 刷新文件列表
                        loadImageFiles();
                    } else {
                        alert(`删除失败: ${result.error || '未知错误'}`);
                    }
                } catch (error) {
                    console.error('批量删除未使用图片时出错:', error);
                    alert(`删除失败: ${error.message}`);
                } finally {
                    confirmDeleteUnusedBtn.disabled = false;
                    confirmDeleteUnusedBtn.innerHTML = '删除所有未使用图片';
                }
            });
            
            // 初始加载图片列表
            loadImageFiles();

            // 刷新按钮点击事件
            refreshBtn.addEventListener('click', loadImageFiles);

            // 初始化视频管理
            setupVideoManagement();

            // 初始化素材管理
            setupMaterialManagement();

            // 初始化HTML代码管理
            setupHtmlCodeManagement();
        });

        // 视频管理功能
        function setupVideoManagement() {
            const videosTab = document.getElementById('videos-tab');
            const videoFile = document.getElementById('videoFile');

            // 视频标签页点击事件
            videosTab.addEventListener('click', function() {
                loadVideoFiles();
            });

            // 视频文件选择事件
            videoFile.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    uploadVideoFiles(files);
                }
                // 清空input值，允许重复选择相同文件
                e.target.value = '';
            });
        }

        // 加载视频文件列表
        async function loadVideoFiles() {
            const videosLoadingSpinner = document.getElementById('videosLoadingSpinner');
            const videosTable = document.getElementById('videosTable');
            const videosEmptyMessage = document.getElementById('videosEmptyMessage');
            const videosTableBody = document.getElementById('videosTableBody');

            // 显示加载中
            videosLoadingSpinner.style.display = 'flex';
            videosTable.style.display = 'none';
            videosEmptyMessage.style.display = 'none';

            try {
                const response = await fetch('/api/files/videos');
                if (!response.ok) {
                    throw new Error('获取视频列表失败');
                }

                const files = await response.json();

                // 隐藏加载中
                videosLoadingSpinner.style.display = 'none';

                if (files.length === 0) {
                    videosEmptyMessage.style.display = 'block';
                } else {
                    videosTable.style.display = 'table';

                    // 清空表格
                    videosTableBody.innerHTML = '';

                    // 填充表格
                    files.forEach(file => {
                        const row = document.createElement('tr');

                        // 预览
                        let previewCell = document.createElement('td');
                        let videoPreview = document.createElement('div');
                        videoPreview.className = 'video-thumbnail';
                        videoPreview.innerHTML = `
                            <i class="bi bi-play-circle-fill" style="font-size: 3rem; color: #007bff;"></i>
                            <div style="font-size: 0.8rem; margin-top: 5px;">视频</div>
                        `;
                        videoPreview.onclick = () => showVideoPreview(file);
                        previewCell.appendChild(videoPreview);
                        row.appendChild(previewCell);

                        // 文件名
                        let nameCell = document.createElement('td');
                        nameCell.textContent = file.name;
                        row.appendChild(nameCell);

                        // 文件URL
                        let urlCell = document.createElement('td');
                        urlCell.className = 'file-address';
                        urlCell.textContent = file.url;
                        row.appendChild(urlCell);

                        // 文件大小
                        let sizeCell = document.createElement('td');
                        sizeCell.textContent = file.size;
                        row.appendChild(sizeCell);

                        // 使用状态
                        let usageCell = document.createElement('td');

                        // 创建使用状态徽章
                        const badgeSpan = document.createElement('span');
                        if (file.isUsed) {
                            badgeSpan.className = 'usage-badge in-use';
                            badgeSpan.textContent = '使用中';

                            // 添加引用信息按钮
                            if (file.references && file.references.length > 0) {
                                const infoButton = document.createElement('i');
                                infoButton.className = 'bi bi-info-circle reference-info-btn';
                                infoButton.title = '查看引用详情';
                                infoButton.addEventListener('click', () => showVideoReferenceInfo(file));
                                usageCell.appendChild(badgeSpan);
                                usageCell.appendChild(infoButton);
                            } else {
                                usageCell.appendChild(badgeSpan);
                            }
                        } else {
                            badgeSpan.className = 'usage-badge not-used';
                            badgeSpan.textContent = '未使用';
                            usageCell.appendChild(badgeSpan);
                        }

                        row.appendChild(usageCell);

                        // 修改时间
                        let timeCell = document.createElement('td');
                        const date = new Date(file.lastModified);
                        timeCell.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                        row.appendChild(timeCell);

                        // 操作按钮
                        let actionCell = document.createElement('td');
                        actionCell.className = 'action-buttons';

                        // 复制成功提示元素
                        let copySuccess = document.createElement('span');
                        copySuccess.className = 'copy-success';
                        copySuccess.innerHTML = '<i class="bi bi-check-circle"></i> 已复制';
                        copySuccess.id = `video-copy-success-${file.name.replace(/[^a-zA-Z0-9]/g, '_')}`;
                        copySuccess.style.display = 'none';

                        let copyBtn = document.createElement('button');
                        copyBtn.className = 'btn btn-sm btn-outline-primary';
                        copyBtn.innerHTML = '<i class="bi bi-clipboard"></i> 复制地址';
                        copyBtn.onclick = () => copyToClipboard(file.url, copySuccess.id);

                        let deleteBtn = document.createElement('button');
                        deleteBtn.className = 'btn btn-sm btn-outline-danger';
                        deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 删除';
                        deleteBtn.onclick = () => deleteVideoFile(file.name);

                        // 如果视频正在使用中，添加警告提示
                        if (file.isUsed) {
                            deleteBtn.title = '警告：该视频正在使用中，删除可能会导致引用它的组件显示异常';
                            deleteBtn.classList.add('position-relative');

                            // 添加警告图标
                            const warningBadge = document.createElement('span');
                            warningBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark';
                            warningBadge.innerHTML = '!';
                            warningBadge.style.fontSize = '0.6rem';
                            deleteBtn.appendChild(warningBadge);
                        }

                        actionCell.appendChild(copySuccess);
                        actionCell.appendChild(copyBtn);
                        actionCell.appendChild(deleteBtn);
                        row.appendChild(actionCell);

                        videosTableBody.appendChild(row);
                    });
                }
            } catch (error) {
                console.error('加载视频文件失败:', error);
                videosLoadingSpinner.style.display = 'none';
                videosEmptyMessage.style.display = 'block';
                videosEmptyMessage.innerHTML = `
                    <i class="bi bi-exclamation-triangle fs-1 text-danger"></i>
                    <p>加载视频文件失败: ${error.message}</p>
                `;
            }
        }

        // 上传视频文件
        async function uploadVideoFiles(files) {
            for (const file of files) {
                await uploadSingleVideoFile(file);
            }
            // 上传完成后刷新列表
            loadVideoFiles();
        }

        // 上传单个视频文件
        async function uploadSingleVideoFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload/video', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    console.log('视频上传成功:', result.url);
                } else {
                    alert(`视频上传失败: ${result.error}`);
                }
            } catch (error) {
                console.error('视频上传失败:', error);
                alert(`视频上传失败: ${error.message}`);
            }
        }

        // 删除视频文件
        async function deleteVideoFile(filename) {
            if (!confirm(`确定要删除视频文件 "${filename}" 吗？`)) {
                return;
            }

            try {
                const response = await fetch(`/api/files/videos/${encodeURIComponent(filename)}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    loadVideoFiles(); // 刷新列表
                } else {
                    alert(`删除失败: ${result.error}`);
                }
            } catch (error) {
                console.error('删除视频文件失败:', error);
                alert(`删除失败: ${error.message}`);
            }
        }

        // 显示视频引用信息
        function showVideoReferenceInfo(file) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">视频引用详情 - ${file.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="list-group">
                                ${file.references.map(ref => {
                                    let description = ref.description || '未知引用';
                                    return `<div class="list-group-item reference-item">${description}</div>`;
                                }).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // 显示视频预览
        function showVideoPreview(file) {
            // 创建视频预览模态框
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">视频预览 - ${file.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <video controls style="max-width: 100%; max-height: 400px;">
                                <source src="${file.url}" type="video/mp4">
                                您的浏览器不支持视频播放
                            </video>
                            <div class="mt-3">
                                <p><strong>文件名：</strong>${file.name}</p>
                                <p><strong>文件大小：</strong>${file.size}</p>
                                <p><strong>文件URL：</strong></p>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="${file.url}" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('${file.url}', null); alert('URL已复制!');">
                                        <i class="bi bi-clipboard"></i> 复制
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // 素材管理功能
        let currentSelectedCategory = null;
        let materialCategories = [];

        function setupMaterialManagement() {
            // 加载分类列表
            loadMaterialCategories();

            // 添加分类按钮事件
            document.getElementById('addCategoryBtn').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('addCategoryModal'));
                modal.show();
            });

            // 保存分类按钮事件
            document.getElementById('saveCategoryBtn').addEventListener('click', saveMaterialCategory);

            // 上传素材按钮事件
            document.getElementById('uploadMaterialBtn').addEventListener('click', function() {
                loadCategoriesForUpload();
                const modal = new bootstrap.Modal(document.getElementById('uploadMaterialModal'));
                modal.show();
            });

            // 初始化上传功能
            setupUploadEvents();

            // 上传素材提交按钮事件
            document.getElementById('uploadMaterialSubmitBtn').addEventListener('click', uploadMaterialFile);

            // 更新分类提交按钮事件
            document.getElementById('updateCategorySubmitBtn').addEventListener('click', function() {
                const categoryId = document.getElementById('editCategoryId').value;
                const categoryName = document.getElementById('editCategoryName').value.trim();
                const categoryDescription = document.getElementById('editCategoryDescription').value.trim();

                if (!categoryName) {
                    alert('请输入分类名称');
                    return;
                }

                const formData = new FormData();
                formData.append('name', categoryName);
                if (categoryDescription) {
                    formData.append('description', categoryDescription);
                }

                fetch(`/api/materials/categories/${categoryId}`, {
                    method: 'PUT',
                    body: formData
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error('更新失败');
                    }
                })
                .then(result => {
                    if (result.success) {
                        alert('分类更新成功');
                        // 关闭模态框
                        bootstrap.Modal.getInstance(document.getElementById('editCategoryModal')).hide();
                        // 重新加载分类列表
                        loadMaterialCategories();
                    } else {
                        alert('更新分类失败: ' + result.error);
                    }
                })
                .catch(error => {
                    console.error('更新分类失败:', error);
                    alert('更新分类失败: ' + error.message);
                });
            });

            // 更新素材提交按钮事件
            document.getElementById('updateMaterialSubmitBtn').addEventListener('click', function() {
                const materialId = document.getElementById('editMaterialId').value;
                const materialName = document.getElementById('editMaterialName').value.trim();
                const categoryId = document.getElementById('editMaterialCategory').value;
                const isDefault = document.getElementById('editIsDefaultMaterial').checked;

                if (!materialName) {
                    alert('请输入文件名称');
                    return;
                }

                const formData = new FormData();
                formData.append('originalName', materialName);
                if (categoryId) {
                    formData.append('categoryId', categoryId);
                }
                formData.append('isDefault', isDefault);

                fetch(`/api/materials/files/${materialId}`, {
                    method: 'PUT',
                    body: formData
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error('更新失败');
                    }
                })
                .then(result => {
                    alert('素材更新成功');
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('editMaterialModal')).hide();
                    // 刷新素材列表
                    loadMaterialFiles();
                })
                .catch(error => {
                    console.error('更新素材失败:', error);
                    alert('更新素材失败: ' + error.message);
                });
            });
        }

        // 加载素材分类
        async function loadMaterialCategories() {
            try {
                const response = await fetch('/api/materials/categories');
                if (!response.ok) {
                    throw new Error('获取分类列表失败');
                }

                materialCategories = await response.json();
                renderCategoryList();

                // 自动选择"全部素材"并加载素材文件
                autoSelectAllMaterials();
            } catch (error) {
                console.error('加载素材分类失败:', error);
                alert('加载分类列表失败: ' + error.message);
            }
        }

        // 自动选择全部素材
        function autoSelectAllMaterials() {
            // 设置当前选中分类为null（全部素材）
            currentSelectedCategory = null;

            // 设置"全部素材"项为选中状态
            const allMaterialsItem = document.querySelector('.category-item[data-category="all"]');
            if (allMaterialsItem) {
                // 清除其他选中状态
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });
                // 设置全部素材为选中
                allMaterialsItem.classList.add('active');
            }

            // 更新标题
            const title = document.getElementById('materialsPanelTitle');
            title.innerHTML = `<i class="bi bi-images"></i> 全部素材文件`;

            // 加载全部素材文件
            loadMaterialFiles();
        }

        // 渲染分类列表
        function renderCategoryList() {
            const categoryList = document.getElementById('categoryList');
            categoryList.innerHTML = '';

            // 添加"全部"选项
            const allItem = document.createElement('div');
            allItem.className = 'list-group-item category-item d-flex justify-content-between align-items-center';
            allItem.setAttribute('data-category', 'all');
            allItem.innerHTML = `
                <span><i class="bi bi-collection"></i> 全部素材</span>
                <span class="category-count badge bg-secondary">-</span>
            `;
            allItem.addEventListener('click', () => selectCategory(null));
            categoryList.appendChild(allItem);

            // 按类型分组显示分类
            const decorationCategories = materialCategories.filter(cat => cat.type === 'decoration');
            const borderCategories = materialCategories.filter(cat => cat.type === 'border');

            if (decorationCategories.length > 0) {
                // 装饰分类标题
                const decorationHeader = document.createElement('div');
                decorationHeader.className = 'list-group-item bg-light';
                decorationHeader.innerHTML = '<small class="text-muted fw-bold">装饰素材</small>';
                categoryList.appendChild(decorationHeader);

                decorationCategories.forEach(category => {
                    const item = createCategoryItem(category);
                    categoryList.appendChild(item);
                });
            }

            if (borderCategories.length > 0) {
                // 边框分类标题
                const borderHeader = document.createElement('div');
                borderHeader.className = 'list-group-item bg-light';
                borderHeader.innerHTML = '<small class="text-muted fw-bold">边框素材</small>';
                categoryList.appendChild(borderHeader);

                borderCategories.forEach(category => {
                    const item = createCategoryItem(category);
                    categoryList.appendChild(item);
                });
            }
        }

        // 创建分类项
        function createCategoryItem(category) {
            const item = document.createElement('div');
            item.className = 'list-group-item category-item d-flex justify-content-between align-items-center';
            item.innerHTML = `
                <span class="category-name-area" style="flex: 1; cursor: pointer;">
                    <i class="bi bi-folder"></i> ${category.name}
                </span>
                <div class="d-flex align-items-center gap-2">
                    <span class="category-count badge bg-secondary">0</span>
                    <div class="category-actions" style="opacity: 0; transition: opacity 0.3s;">
                        <button class="btn btn-sm btn-outline-primary edit-category-btn" title="编辑分类">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-category-btn" title="删除分类">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            // 添加悬停效果
            item.addEventListener('mouseenter', () => {
                const actions = item.querySelector('.category-actions');
                if (actions) actions.style.opacity = '1';
            });

            item.addEventListener('mouseleave', () => {
                const actions = item.querySelector('.category-actions');
                if (actions) actions.style.opacity = '0';
            });

            // 分类名称点击事件
            item.querySelector('.category-name-area').addEventListener('click', () => selectCategory(category));

            // 编辑按钮事件
            item.querySelector('.edit-category-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                showEditCategoryModal(category);
            });

            // 删除按钮事件
            item.querySelector('.delete-category-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                deleteMaterialCategory(category);
            });

            return item;
        }

        // 选择分类
        function selectCategory(category) {
            currentSelectedCategory = category;

            // 更新选中状态
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.category-item').classList.add('active');

            // 更新标题
            const title = document.getElementById('materialsPanelTitle');
            if (category) {
                title.innerHTML = `<i class="bi bi-images"></i> ${category.name} - 素材文件`;
            } else {
                title.innerHTML = `<i class="bi bi-images"></i> 全部素材文件`;
            }

            // 加载素材文件
            loadMaterialFiles();
        }

        // 加载素材文件
        async function loadMaterialFiles() {
            const materialsGrid = document.getElementById('materialsGrid');
            const loadingSpinner = document.getElementById('materialsLoadingSpinner');
            const emptyMessage = document.getElementById('materialsEmptyMessage');

            // 显示加载状态
            loadingSpinner.style.display = 'flex';
            emptyMessage.style.display = 'none';
            materialsGrid.innerHTML = '';

            try {
                let url = '/api/materials/files';
                if (currentSelectedCategory) {
                    url = `/api/materials/files/category/${currentSelectedCategory.id}`;
                }

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error('获取素材文件失败');
                }

                const materials = await response.json();

                loadingSpinner.style.display = 'none';

                if (materials.length === 0) {
                    emptyMessage.style.display = 'block';
                    return;
                }

                // 渲染素材网格
                materials.forEach(material => {
                    const materialCard = createMaterialCard(material);
                    materialsGrid.appendChild(materialCard);
                });

            } catch (error) {
                console.error('加载素材文件失败:', error);
                loadingSpinner.style.display = 'none';
                emptyMessage.style.display = 'block';
                alert('加载素材文件失败: ' + error.message);
            }
        }

        // 创建素材卡片
        function createMaterialCard(material) {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-4 col-6';

            const typeClass = material.category ?
                `material-type-${material.category.type}` : 'material-type-decoration';
            const typeName = material.category ?
                material.category.type === 'decoration' ? '装饰' : '边框' : '装饰';

            col.innerHTML = `
                <div class="material-card" data-material-id="${material.id}">
                    <div class="material-card-header">
                        <div class="material-actions">
                            <button class="btn btn-sm btn-outline-primary edit-material-btn" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-material-btn" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                    <img src="${material.fileUrl}" alt="${material.originalName}" class="material-preview">
                    <div class="material-info">
                        <div class="material-name" title="${material.originalName}">${material.originalName}</div>
                        <div class="material-meta">
                            <span class="material-type-badge ${typeClass}">${typeName}</span>
                            <span>${material.fileSizeDisplay || '0 B'}</span>
                        </div>
                    </div>
                </div>
            `;

            // 添加点击事件
            col.querySelector('.material-preview').addEventListener('click', () => {
                showMaterialPreview(material);
            });

            // 添加编辑按钮事件
            col.querySelector('.edit-material-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                showEditMaterialModal(material);
            });

            // 添加删除按钮事件
            col.querySelector('.delete-material-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                deleteMaterialFile(material);
            });

            return col;
        }

        // 显示素材预览
        function showMaterialPreview(material) {
            // 可以复用现有的图片预览模态框
            const modal = document.getElementById('imagePreviewModal');
            const img = document.getElementById('previewImage');
            const title = document.getElementById('previewImageName');

            img.src = material.fileUrl;
            title.textContent = material.originalName;

            const previewModal = new bootstrap.Modal(modal);
            previewModal.show();
        }

        // 显示编辑素材模态框
        function showEditMaterialModal(material) {
            // 填充表单数据
            document.getElementById('editMaterialId').value = material.id;
            document.getElementById('editMaterialName').value = material.originalName;
            document.getElementById('editIsDefaultMaterial').checked = material.isDefault;

            // 加载分类选项并预选当前分类
            loadCategoriesForEdit(material.category ? material.category.id : null);

            // 显示模态框
            const editModal = new bootstrap.Modal(document.getElementById('editMaterialModal'));
            editModal.show();
        }

        // 为编辑模态框加载分类选项
        function loadCategoriesForEdit(selectedCategoryId) {
            const select = document.getElementById('editMaterialCategory');
            select.innerHTML = '<option value="">无分类</option>';

            // 确保materialCategories已加载
            if (!materialCategories || materialCategories.length === 0) {
                // 如果分类还未加载，先加载分类
                fetch('/api/materials/categories')
                    .then(response => response.json())
                    .then(categories => {
                        materialCategories = categories;
                        populateEditCategoryOptions(selectedCategoryId);
                    })
                    .catch(error => {
                        console.error('加载分类失败:', error);
                    });
            } else {
                populateEditCategoryOptions(selectedCategoryId);
            }
        }

        // 填充编辑分类选项
        function populateEditCategoryOptions(selectedCategoryId) {
            const select = document.getElementById('editMaterialCategory');

            materialCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = `${category.name} (${category.type === 'decoration' ? '装饰' : '边框'})`;
                if (category.id === selectedCategoryId) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }

        // 删除素材文件
        function deleteMaterialFile(material) {
            if (confirm(`确定要删除素材文件 "${material.originalName}" 吗？此操作无法撤销。`)) {
                fetch(`/api/materials/files/${material.id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (response.ok) {
                        alert('素材删除成功');
                        // 刷新素材列表
                        loadMaterialFiles();
                    } else {
                        throw new Error('删除失败');
                    }
                })
                .catch(error => {
                    console.error('删除素材失败:', error);
                    alert('删除素材失败: ' + error.message);
                });
            }
        }

        // 显示编辑分类模态框
        function showEditCategoryModal(category) {
            // 填充表单数据
            document.getElementById('editCategoryId').value = category.id;
            document.getElementById('editCategoryName').value = category.name;
            document.getElementById('editCategoryType').value = category.type;
            document.getElementById('editCategoryDescription').value = category.description || '';

            // 显示模态框
            const editModal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
            editModal.show();
        }

        // 删除分类
        function deleteMaterialCategory(category) {
            if (confirm(`确定要删除分类 "${category.name}" 吗？此操作将同时删除该分类下的所有素材文件，且无法撤销。`)) {
                fetch(`/api/materials/categories/${category.id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (response.ok) {
                        alert('分类删除成功');
                        // 重新加载分类列表
                        loadMaterialCategories();
                        // 如果当前选中的是被删除的分类，切换到全部素材
                        if (currentSelectedCategory && currentSelectedCategory.id === category.id) {
                            currentSelectedCategory = null;
                            loadMaterialFiles();
                        }
                    } else {
                        throw new Error('删除失败');
                    }
                })
                .catch(error => {
                    console.error('删除分类失败:', error);
                    alert('删除分类失败: ' + error.message);
                });
            }
        }

        // 保存素材分类
        async function saveMaterialCategory() {
            const name = document.getElementById('categoryName').value.trim();
            const type = document.getElementById('categoryType').value;
            const description = document.getElementById('categoryDescription').value.trim();

            if (!name || !type) {
                alert('请填写分类名称和类型');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('name', name);
                formData.append('type', type);
                if (description) {
                    formData.append('description', description);
                }

                const response = await fetch('/api/materials/categories', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
                    modal.hide();

                    // 清空表单
                    document.getElementById('addCategoryForm').reset();

                    // 重新加载分类列表
                    loadMaterialCategories();

                    alert('分类创建成功');
                } else {
                    alert('创建分类失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存分类失败:', error);
                alert('保存分类失败: ' + error.message);
            }
        }

        // 为上传加载分类选项
        async function loadCategoriesForUpload() {
            const select = document.getElementById('materialCategory');
            select.innerHTML = '<option value="">无分类</option>';

            materialCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = `${category.name} (${category.type === 'decoration' ? '装饰' : '边框'})`;
                select.appendChild(option);
            });
        }

        // 全局变量存储选中的文件
        let selectedFiles = [];

        // 设置拖拽上传和文件选择事件
        function setupUploadEvents() {
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('materialFile');
            const selectFilesBtn = document.getElementById('selectFilesBtn');
            const addMoreFilesBtn = document.getElementById('addMoreFilesBtn');

            // 点击选择文件按钮
            selectFilesBtn.addEventListener('click', () => {
                fileInput.click();
            });

            // 继续添加文件按钮
            addMoreFilesBtn.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择事件
            fileInput.addEventListener('change', (e) => {
                handleFileSelection(e.target.files, false); // false表示累积添加
                // 清空input的值，允许重复选择相同文件
                e.target.value = '';
            });

            // 拖拽事件
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#007bff';
                dropZone.style.backgroundColor = '#e3f2fd';
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = '#f8f9fa';
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = '#f8f9fa';
                handleFileSelection(e.dataTransfer.files, false); // false表示累积添加
            });
        }

        // 处理文件选择
        function handleFileSelection(files, replace = true) {
            const validFiles = [];
            const invalidFiles = [];
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/apng'];

            for (let file of files) {
                if (allowedTypes.includes(file.type)) {
                    // 检查是否已经存在相同的文件
                    const isDuplicate = selectedFiles.some(existingFile =>
                        existingFile.name === file.name && existingFile.size === file.size
                    );

                    if (!isDuplicate) {
                        validFiles.push(file);
                    }
                } else {
                    invalidFiles.push(file.name);
                }
            }

            // 显示无效文件提示
            if (invalidFiles.length > 0) {
                alert(`以下文件格式不支持，已跳过：\n${invalidFiles.join('\n')}\n\n请选择图片文件（JPG、PNG、GIF、WEBP、APNG）`);
            }

            if (validFiles.length > 0) {
                if (replace) {
                    // 替换模式：清空现有文件
                    selectedFiles = validFiles;
                } else {
                    // 累积模式：添加到现有文件
                    selectedFiles = selectedFiles.concat(validFiles);
                }

                displayFileList();
                document.getElementById('uploadMaterialSubmitBtn').disabled = false;

                // 显示添加成功提示
                if (!replace && validFiles.length > 0) {
                    const message = validFiles.length === 1 ?
                        `已添加 1 个文件` :
                        `已添加 ${validFiles.length} 个文件`;
                    showTemporaryMessage(message);
                }
            }
        }

        // 显示临时消息
        function showTemporaryMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            messageDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            messageDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(messageDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        // 显示文件列表
        function displayFileList() {
            const fileListContainer = document.getElementById('fileListContainer');
            const fileList = document.getElementById('fileList');

            if (selectedFiles.length === 0) {
                fileListContainer.style.display = 'none';
                return;
            }

            fileListContainer.style.display = 'block';
            fileList.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'd-flex justify-content-between align-items-center p-2 border-bottom';
                fileItem.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-image me-2"></i>
                        <span>${file.name}</span>
                        <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                        <i class="bi bi-x"></i>
                    </button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        // 移除文件
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displayFileList();

            if (selectedFiles.length === 0) {
                document.getElementById('uploadMaterialSubmitBtn').disabled = true;
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 批量上传素材文件
        async function uploadMaterialFile() {
            if (selectedFiles.length === 0) {
                alert('请选择要上传的文件');
                return;
            }

            const categorySelect = document.getElementById('materialCategory');
            const isDefaultCheckbox = document.getElementById('isDefaultMaterial');
            const categoryId = categorySelect.value || null;
            const isDefault = isDefaultCheckbox.checked;

            // 显示上传进度
            const progressContainer = document.getElementById('uploadProgress');
            const progressBar = progressContainer.querySelector('.progress-bar');
            const uploadStatus = document.getElementById('uploadStatus');

            progressContainer.style.display = 'block';
            document.getElementById('uploadMaterialSubmitBtn').disabled = true;

            let successCount = 0;
            let failCount = 0;
            const failedFiles = [];

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progress = ((i + 1) / selectedFiles.length) * 100;

                progressBar.style.width = progress + '%';
                uploadStatus.textContent = `正在上传 ${i + 1}/${selectedFiles.length}: ${file.name}`;

                try {
                    const formData = new FormData();
                    formData.append('file', file);
                    if (categoryId) {
                        formData.append('categoryId', categoryId);
                    }
                    formData.append('isDefault', isDefault);

                    const response = await fetch('/api/materials/files/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                        failedFiles.push(`${file.name}: ${result.error}`);
                    }
                } catch (error) {
                    failCount++;
                    failedFiles.push(`${file.name}: ${error.message}`);
                }
            }

            // 上传完成
            progressBar.style.width = '100%';
            uploadStatus.textContent = `上传完成：成功 ${successCount} 个，失败 ${failCount} 个`;

            // 显示结果
            let message = `上传完成！\n成功：${successCount} 个文件\n失败：${failCount} 个文件`;
            if (failedFiles.length > 0) {
                message += '\n\n失败详情：\n' + failedFiles.join('\n');
            }
            alert(message);

            // 重新加载素材文件
            if (successCount > 0) {
                loadMaterialFiles();
            }

            // 重置界面
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('uploadMaterialModal'));
                modal.hide();
                resetUploadForm();
            }, 2000);
        }

        // 重置上传表单
        function resetUploadForm() {
            selectedFiles = [];
            document.getElementById('uploadMaterialForm').reset();
            document.getElementById('fileListContainer').style.display = 'none';
            document.getElementById('uploadProgress').style.display = 'none';
            document.getElementById('uploadMaterialSubmitBtn').disabled = true;
        }

        // ==================== HTML代码管理功能 ====================

        // HTML代码管理相关变量
        let htmlCategories = [];
        let htmlCodeSnippets = []; // 当前显示的代码片段
        let allHtmlCodeSnippets = []; // 所有代码片段，用于计算分类数量
        let currentSelectedHtmlCategory = null;
        let currentEditingHtmlCode = null;

        // 初始化HTML代码管理
        function setupHtmlCodeManagement() {
            // 加载HTML代码分类
            loadHtmlCodeCategories();

            // 添加分类按钮事件
            document.getElementById('addHtmlCategoryBtn').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('addHtmlCategoryModal'));
                modal.show();
            });

            // 保存分类按钮事件
            document.getElementById('saveHtmlCategoryBtn').addEventListener('click', saveHtmlCodeCategory);

            // 添加HTML代码按钮事件
            document.getElementById('addHtmlCodeBtn').addEventListener('click', function() {
                showHtmlCodeModal();
            });

            // 搜索按钮事件
            document.getElementById('searchHtmlCodesBtn').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('searchHtmlCodesModal'));
                modal.show();
            });

            // 保存HTML代码按钮事件
            document.getElementById('saveHtmlCodeBtn').addEventListener('click', saveHtmlCodeSnippet);

            // HTML代码内容实时预览
            document.getElementById('htmlCodeContent').addEventListener('input', function() {
                updateHtmlCodePreview();
            });

            // 搜索功能
            document.getElementById('performSearchBtn').addEventListener('click', performHtmlCodeSearch);
            document.getElementById('clearSearchBtn').addEventListener('click', clearHtmlCodeSearch);

            // 复制HTML代码按钮事件
            document.getElementById('copyHtmlCodeBtn').addEventListener('click', copyHtmlCodeToClipboard);

            // 新窗口打开按钮事件
            document.getElementById('openInNewWindowBtn').addEventListener('click', openHtmlCodeInNewWindow);
        }

        // 加载HTML代码分类
        async function loadHtmlCodeCategories() {
            try {
                const response = await fetch('/api/html-codes/categories');
                if (!response.ok) {
                    throw new Error('获取HTML代码分类失败');
                }

                htmlCategories = await response.json();

                // 先加载所有代码片段，这样allHtmlCodeSnippets会被正确设置
                await loadHtmlCodeSnippets();

                // 然后渲染分类列表，此时分类数量会正确显示
                renderHtmlCodeCategories();
            } catch (error) {
                console.error('加载HTML代码分类失败:', error);
                alert('加载HTML代码分类失败: ' + error.message);
            }
        }

        // 渲染HTML代码分类列表
        function renderHtmlCodeCategories() {
            const categoryList = document.getElementById('htmlCategoryList');
            categoryList.innerHTML = '';

            // 添加"全部"选项
            const allItem = document.createElement('div');
            allItem.className = 'list-group-item category-item d-flex justify-content-between align-items-center';
            allItem.innerHTML = `
                <span><i class="bi bi-collection me-2"></i>全部代码</span>
                <span class="category-count">${allHtmlCodeSnippets.length}</span>
            `;
            allItem.addEventListener('click', function() {
                selectHtmlCodeCategory(null);
            });
            categoryList.appendChild(allItem);

            // 添加分类项
            htmlCategories.forEach(category => {
                const categoryItem = document.createElement('div');
                categoryItem.className = 'list-group-item category-item d-flex justify-content-between align-items-center';

                // 使用allHtmlCodeSnippets来计算分类数量，确保数量正确
                const snippetCount = allHtmlCodeSnippets.filter(snippet =>
                    snippet.category && snippet.category.id === category.id
                ).length;

                categoryItem.innerHTML = `
                    <span><i class="bi bi-folder me-2"></i>${category.name}</span>
                    <div>
                        <span class="category-count me-2">${snippetCount}</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-sm edit-category-btn" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm delete-category-btn" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                categoryItem.addEventListener('click', function(e) {
                    if (!e.target.closest('.btn-group')) {
                        selectHtmlCodeCategory(category);
                    }
                });

                // 编辑分类按钮事件
                categoryItem.querySelector('.edit-category-btn').addEventListener('click', function(e) {
                    e.stopPropagation();
                    editHtmlCodeCategory(category);
                });

                // 删除分类按钮事件
                categoryItem.querySelector('.delete-category-btn').addEventListener('click', function(e) {
                    e.stopPropagation();
                    deleteHtmlCodeCategory(category);
                });

                categoryList.appendChild(categoryItem);
            });
        }

        // 选择HTML代码分类
        function selectHtmlCodeCategory(category) {
            // 更新选中状态
            document.querySelectorAll('#htmlCategoryList .category-item').forEach(item => {
                item.classList.remove('active');
            });

            if (category) {
                event.currentTarget.classList.add('active');
                currentSelectedHtmlCategory = category;
                document.getElementById('htmlCodesPanelTitle').innerHTML =
                    `<i class="bi bi-code-slash"></i> ${category.name} - HTML代码片段`;
            } else {
                document.querySelector('#htmlCategoryList .category-item').classList.add('active');
                currentSelectedHtmlCategory = null;
                document.getElementById('htmlCodesPanelTitle').innerHTML =
                    `<i class="bi bi-code-slash"></i> HTML代码片段`;
            }

            // 重新加载代码片段
            loadHtmlCodeSnippets();
        }

        // 加载HTML代码片段
        async function loadHtmlCodeSnippets() {
            const loadingSpinner = document.getElementById('htmlCodesLoadingSpinner');
            const emptyMessage = document.getElementById('htmlCodesEmptyMessage');
            const htmlCodesGrid = document.getElementById('htmlCodesGrid');

            loadingSpinner.style.display = 'flex';
            emptyMessage.style.display = 'none';
            htmlCodesGrid.innerHTML = '';

            try {
                let url = '/api/html-codes/snippets';
                if (currentSelectedHtmlCategory) {
                    url = `/api/html-codes/snippets/category/${currentSelectedHtmlCategory.id}`;
                }

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error('获取HTML代码片段失败');
                }

                htmlCodeSnippets = await response.json();

                // 如果是加载全部代码片段，同时更新allHtmlCodeSnippets
                if (!currentSelectedHtmlCategory) {
                    allHtmlCodeSnippets = [...htmlCodeSnippets]; // 创建副本
                    console.log('更新全部代码片段数据，共', allHtmlCodeSnippets.length, '个');
                }

                loadingSpinner.style.display = 'none';

                if (htmlCodeSnippets.length === 0) {
                    emptyMessage.style.display = 'block';
                    return;
                }

                // 渲染代码片段网格
                htmlCodeSnippets.forEach(snippet => {
                    const snippetCard = createHtmlCodeCard(snippet);
                    htmlCodesGrid.appendChild(snippetCard);
                });

                // 重新渲染分类列表以更新数量
                renderHtmlCodeCategories();

            } catch (error) {
                console.error('加载HTML代码片段失败:', error);
                loadingSpinner.style.display = 'none';
                emptyMessage.style.display = 'block';
                alert('加载HTML代码片段失败: ' + error.message);
            }
        }

        // 创建HTML代码卡片
        function createHtmlCodeCard(snippet) {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4 mb-3';

            const categoryName = snippet.category ? snippet.category.name : '无分类';
            const tags = snippet.tags ? snippet.tags.split(',').map(tag =>
                `<span class="html-code-tag">${tag.trim()}</span>`
            ).join('') : '';



            col.innerHTML = `
                <div class="html-code-card" data-snippet-id="${snippet.id}">
                    <div class="html-code-card-header">
                        <div class="html-code-actions">
                            <button class="btn btn-sm btn-outline-primary preview-html-btn" title="预览">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success edit-html-btn" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>

                            <button class="btn btn-sm btn-outline-danger delete-html-btn" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="html-code-preview" id="preview-${snippet.id}">
                        <!-- 预览内容将通过iframe显示 -->
                    </div>
                    <div class="html-code-info">
                        <div class="html-code-title">${snippet.title}</div>
                        <div class="html-code-description">${snippet.description || ''}</div>
                        <div class="html-code-meta">
                            <div>
                                <small class="text-muted">${categoryName}</small>
                            </div>
                            <div class="html-code-tags">
                                ${tags}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 创建预览iframe - 使用更安全的方式
            const previewContainer = col.querySelector(`#preview-${snippet.id}`);

            try {
                const iframe = document.createElement('iframe');
                iframe.style.width = '125%'; // 补偿scale缩放
                iframe.style.height = '125%'; // 补偿scale缩放
                iframe.style.border = 'none';
                iframe.style.transform = 'scale(0.8)';
                iframe.style.transformOrigin = 'top left';
                iframe.style.overflow = 'hidden';

                // 使用更安全的方式设置iframe内容
                iframe.onload = function() {
                    try {
                        // iframe加载完成后再设置内容
                        if (iframe.contentDocument) {
                            iframe.contentDocument.open();
                            iframe.contentDocument.write(snippet.htmlContent);
                            iframe.contentDocument.close();
                        }
                    } catch (e) {
                        console.warn('iframe内容设置失败，使用fallback方案:', e);
                        // fallback: 使用srcdoc
                        iframe.srcdoc = snippet.htmlContent;
                    }
                };

                // 先添加到DOM，然后设置src为空以触发onload
                previewContainer.appendChild(iframe);
                iframe.src = 'about:blank';

                // 如果onload没有触发，使用srcdoc作为备用方案
                setTimeout(() => {
                    if (!iframe.contentDocument || iframe.contentDocument.body.innerHTML === '') {
                        iframe.srcdoc = snippet.htmlContent;
                    }
                }, 100);

            } catch (error) {
                console.error('创建iframe预览失败:', error);
                // 完全失败时显示错误信息
                previewContainer.innerHTML = '<div class="text-center text-muted p-3"><small>预览加载失败</small></div>';
            }

            // 添加事件监听器
            col.querySelector('.preview-html-btn').addEventListener('click', function() {
                showHtmlCodePreview(snippet);
            });

            col.querySelector('.edit-html-btn').addEventListener('click', function() {
                showHtmlCodeModal(snippet);
            });



            col.querySelector('.delete-html-btn').addEventListener('click', function() {
                deleteHtmlCodeSnippet(snippet);
            });

            return col;
        }

        // 重新加载所有HTML代码数据（用于更新分类计数）
        async function reloadAllHtmlCodeData() {
            try {
                // 临时保存当前选中的分类
                const tempSelectedCategory = currentSelectedHtmlCategory;

                // 重置为全部分类以加载所有数据
                currentSelectedHtmlCategory = null;

                // 加载所有代码片段数据
                await loadHtmlCodeSnippets();

                // 恢复之前选中的分类
                currentSelectedHtmlCategory = tempSelectedCategory;

                // 如果之前有选中分类，重新加载该分类的数据
                if (tempSelectedCategory) {
                    await loadHtmlCodeSnippets();
                }

                // 重新渲染分类列表以更新数量
                renderHtmlCodeCategories();

                console.log('重新加载HTML代码数据完成');
            } catch (error) {
                console.error('重新加载HTML代码数据失败:', error);
            }
        }

        // 显示HTML代码预览
        function showHtmlCodePreview(snippet) {
            document.getElementById('previewModalTitle').textContent = snippet.title;
            document.getElementById('htmlCodeDisplay').textContent = snippet.htmlContent;

            // 使用iframe显示完整预览
            const fullPreviewContainer = document.getElementById('fullPreviewContainer');
            if (!fullPreviewContainer) {
                console.warn('预览容器未找到');
                return;
            }

            fullPreviewContainer.innerHTML = '';

            try {
                const iframe = document.createElement('iframe');
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = 'none';
                iframe.style.borderRadius = '4px';

                // 使用更安全的方式设置iframe内容
                iframe.onload = function() {
                    try {
                        if (iframe.contentDocument) {
                            iframe.contentDocument.open();
                            iframe.contentDocument.write(snippet.htmlContent);
                            iframe.contentDocument.close();
                        }
                    } catch (e) {
                        console.warn('iframe内容设置失败，使用srcdoc:', e);
                        iframe.srcdoc = snippet.htmlContent;
                    }
                };

                fullPreviewContainer.appendChild(iframe);
                iframe.src = 'about:blank';

                // 备用方案
                setTimeout(() => {
                    if (!iframe.contentDocument || iframe.contentDocument.body.innerHTML === '') {
                        iframe.srcdoc = snippet.htmlContent;
                    }
                }, 100);

            } catch (error) {
                console.error('创建完整预览iframe失败:', error);
                fullPreviewContainer.innerHTML = '<div class="text-center text-muted p-3"><small>预览加载失败</small></div>';
            }

            const modal = new bootstrap.Modal(document.getElementById('htmlCodePreviewModal'));
            modal.show();

            // 设置当前预览的代码片段
            currentPreviewSnippet = snippet;
        }

        // 显示HTML代码模态框（添加或编辑）
        async function showHtmlCodeModal(snippet = null) {
            const modal = new bootstrap.Modal(document.getElementById('htmlCodeModal'));
            const modalTitle = document.getElementById('htmlCodeModalTitle');
            const form = document.getElementById('htmlCodeForm');

            // 先加载分类选项，等待完成
            await loadHtmlCategoriesForForm();

            if (snippet) {
                // 编辑模式
                modalTitle.textContent = '编辑HTML代码片段';
                document.getElementById('htmlCodeId').value = snippet.id;
                document.getElementById('htmlCodeTitle').value = snippet.title;
                document.getElementById('htmlCodeDescription').value = snippet.description || '';
                document.getElementById('htmlCodeContent').value = snippet.htmlContent;
                document.getElementById('htmlCodeTags').value = snippet.tags || '';


                // 调试：打印snippet对象查看category信息
                console.log('编辑的snippet对象:', snippet);
                console.log('snippet.category:', snippet.category);

                // 在分类选项加载完成后设置分类值
                if (snippet.category && snippet.category.id) {
                    const categorySelect = document.getElementById('htmlCodeCategory');
                    categorySelect.value = snippet.category.id;
                    console.log('设置分类ID:', snippet.category.id, '当前值:', categorySelect.value);
                } else {
                    console.log('snippet没有category信息或category.id为空');
                }

                currentEditingHtmlCode = snippet;
            } else {
                // 添加模式
                modalTitle.textContent = '添加HTML代码片段';
                form.reset();
                document.getElementById('htmlCodeId').value = '';
                currentEditingHtmlCode = null;
            }

            // 更新预览
            updateHtmlCodePreview();
            modal.show();
        }

        // 加载分类选项到表单
        async function loadHtmlCategoriesForForm() {
            const categorySelect = document.getElementById('htmlCodeCategory');
            categorySelect.innerHTML = '<option value="">无分类</option>';

            // 如果分类还没有加载，先加载分类
            if (!htmlCategories || htmlCategories.length === 0) {
                try {
                    const response = await fetch('/api/html-codes/categories');
                    if (response.ok) {
                        htmlCategories = await response.json();
                        console.log('重新加载HTML分类:', htmlCategories.length); // 调试日志
                    }
                } catch (error) {
                    console.error('加载HTML分类失败:', error);
                }
            }

            // 填充分类选项
            htmlCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });

            console.log('分类选项加载完成，共', htmlCategories.length, '个分类'); // 调试日志
        }

        // 更新HTML代码预览
        function updateHtmlCodePreview() {
            const htmlContentElement = document.getElementById('htmlCodeContent');
            const previewContainer = document.getElementById('htmlCodePreview');

            if (!htmlContentElement || !previewContainer) {
                console.warn('预览元素未找到');
                return;
            }

            const htmlContent = htmlContentElement.value;

            // 清空容器
            previewContainer.innerHTML = '';

            if (htmlContent.trim()) {
                try {
                    // 创建iframe来隔离HTML代码
                    const iframe = document.createElement('iframe');
                    iframe.style.width = '100%';
                    iframe.style.height = '100%';
                    iframe.style.border = 'none';
                    iframe.style.borderRadius = '4px';

                    // 使用更安全的方式设置iframe内容
                    iframe.onload = function() {
                        try {
                            if (iframe.contentDocument) {
                                iframe.contentDocument.open();
                                iframe.contentDocument.write(htmlContent);
                                iframe.contentDocument.close();
                            }
                        } catch (e) {
                            console.warn('iframe内容设置失败，使用srcdoc:', e);
                            iframe.srcdoc = htmlContent;
                        }
                    };

                    previewContainer.appendChild(iframe);
                    iframe.src = 'about:blank';

                    // 备用方案
                    setTimeout(() => {
                        if (!iframe.contentDocument || iframe.contentDocument.body.innerHTML === '') {
                            iframe.srcdoc = htmlContent;
                        }
                    }, 100);

                } catch (error) {
                    console.error('创建预览iframe失败:', error);
                    previewContainer.innerHTML = '<div class="text-center text-muted p-3"><small>预览加载失败</small></div>';
                }
            } else {
                previewContainer.innerHTML = '<p class="text-muted text-center">在上方输入HTML代码查看预览效果</p>';
            }
        }

        // 保存HTML代码分类
        async function saveHtmlCodeCategory() {
            const name = document.getElementById('htmlCategoryName').value.trim();
            const description = document.getElementById('htmlCategoryDescription').value.trim();

            if (!name) {
                alert('请输入分类名称');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('name', name);
                if (description) {
                    formData.append('description', description);
                }

                const response = await fetch('/api/html-codes/categories', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addHtmlCategoryModal'));
                    modal.hide();

                    // 重新加载分类
                    loadHtmlCodeCategories();

                    // 重置表单
                    document.getElementById('addHtmlCategoryForm').reset();

                    alert('分类创建成功');
                } else {
                    alert('创建分类失败: ' + result.error);
                }
            } catch (error) {
                console.error('创建分类失败:', error);
                alert('创建分类失败: ' + error.message);
            }
        }

        // 保存HTML代码片段
        async function saveHtmlCodeSnippet() {
            const title = document.getElementById('htmlCodeTitle').value.trim();
            const description = document.getElementById('htmlCodeDescription').value.trim();
            const htmlContent = document.getElementById('htmlCodeContent').value.trim();
            const categoryId = document.getElementById('htmlCodeCategory').value || null;
            const tags = document.getElementById('htmlCodeTags').value.trim();

            const snippetId = document.getElementById('htmlCodeId').value;

            if (!title) {
                alert('请输入标题');
                return;
            }

            if (!htmlContent) {
                alert('请输入HTML代码');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('title', title);
                if (description) formData.append('description', description);
                formData.append('htmlContent', htmlContent);
                if (categoryId) formData.append('categoryId', categoryId);
                if (tags) formData.append('tags', tags);


                let url = '/api/html-codes/snippets';
                let method = 'POST';

                if (snippetId) {
                    // 编辑模式
                    url = `/api/html-codes/snippets/${snippetId}`;
                    method = 'PUT';
                }

                const response = await fetch(url, {
                    method: method,
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('htmlCodeModal'));
                    modal.hide();

                    // 重新加载全部数据以更新分类计数
                    await reloadAllHtmlCodeData();

                    // 重置表单
                    document.getElementById('htmlCodeForm').reset();

                    alert(snippetId ? '代码片段更新成功' : '代码片段创建成功');
                } else {
                    alert((snippetId ? '更新' : '创建') + '代码片段失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存代码片段失败:', error);
                alert('保存代码片段失败: ' + error.message);
            }
        }



        // 删除HTML代码片段
        async function deleteHtmlCodeSnippet(snippet) {
            if (!confirm(`确定要删除代码片段"${snippet.title}"吗？此操作无法撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/html-codes/snippets/${snippet.id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    // 重新加载全部数据以更新分类计数
                    await reloadAllHtmlCodeData();

                    alert('代码片段删除成功');
                } else {
                    alert('删除代码片段失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除代码片段失败:', error);
                alert('删除代码片段失败: ' + error.message);
            }
        }

        // 删除HTML代码分类
        async function deleteHtmlCodeCategory(category) {
            if (!confirm(`确定要删除分类"${category.name}"吗？此操作无法撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/html-codes/categories/${category.id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    // 重新加载分类
                    loadHtmlCodeCategories();
                    alert('分类删除成功');
                } else {
                    alert('删除分类失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除分类失败:', error);
                alert('删除分类失败: ' + error.message);
            }
        }

        // 搜索HTML代码
        async function performHtmlCodeSearch() {
            const keyword = document.getElementById('searchKeyword').value.trim();

            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            try {
                const response = await fetch(`/api/html-codes/snippets/search?keyword=${encodeURIComponent(keyword)}`);
                if (!response.ok) {
                    throw new Error('搜索失败');
                }

                htmlCodeSnippets = await response.json();

                // 关闭搜索模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('searchHtmlCodesModal'));
                modal.hide();

                // 更新界面
                currentSelectedHtmlCategory = null;
                document.getElementById('htmlCodesPanelTitle').innerHTML =
                    `<i class="bi bi-search"></i> 搜索结果: "${keyword}"`;

                // 清除分类选中状态
                document.querySelectorAll('#htmlCategoryList .category-item').forEach(item => {
                    item.classList.remove('active');
                });

                // 渲染搜索结果
                renderHtmlCodeSearchResults();

            } catch (error) {
                console.error('搜索失败:', error);
                alert('搜索失败: ' + error.message);
            }
        }

        // 渲染HTML代码搜索结果
        function renderHtmlCodeSearchResults() {
            const loadingSpinner = document.getElementById('htmlCodesLoadingSpinner');
            const emptyMessage = document.getElementById('htmlCodesEmptyMessage');
            const htmlCodesGrid = document.getElementById('htmlCodesGrid');

            loadingSpinner.style.display = 'none';
            htmlCodesGrid.innerHTML = '';

            if (htmlCodeSnippets.length === 0) {
                emptyMessage.style.display = 'block';
                emptyMessage.innerHTML = '<i class="bi bi-search fs-1"></i><p class="mt-3">没有找到匹配的HTML代码片段</p>';
                return;
            }

            emptyMessage.style.display = 'none';

            // 渲染代码片段网格
            htmlCodeSnippets.forEach(snippet => {
                const snippetCard = createHtmlCodeCard(snippet);
                htmlCodesGrid.appendChild(snippetCard);
            });
        }

        // 清除搜索
        function clearHtmlCodeSearch() {
            document.getElementById('searchKeyword').value = '';

            // 关闭搜索模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('searchHtmlCodesModal'));
            modal.hide();

            // 重新加载所有代码片段
            currentSelectedHtmlCategory = null;
            document.getElementById('htmlCodesPanelTitle').innerHTML =
                `<i class="bi bi-code-slash"></i> HTML代码片段`;

            // 选中"全部"分类
            document.querySelector('#htmlCategoryList .category-item').classList.add('active');

            loadHtmlCodeSnippets();
        }

        // 复制HTML代码到剪贴板
        function copyHtmlCodeToClipboard() {
            if (!currentPreviewSnippet) return;

            navigator.clipboard.writeText(currentPreviewSnippet.htmlContent).then(() => {
                alert('HTML代码已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 在新窗口中打开HTML代码
        function openHtmlCodeInNewWindow() {
            if (!currentPreviewSnippet) return;

            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${currentPreviewSnippet.title}</title>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                </head>
                <body>
                    ${currentPreviewSnippet.htmlContent}
                </body>
                </html>
            `);
            newWindow.document.close();
        }

        // 当前预览的代码片段
        let currentPreviewSnippet = null;
    </script>

    <!-- 隐藏的视频上传input -->
    <input type="file" class="d-none" id="videoFile" accept="video/*" multiple>

</body>
</html>