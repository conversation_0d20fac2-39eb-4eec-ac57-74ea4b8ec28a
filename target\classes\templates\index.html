<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <script type="text/javascript" th:src="@{/js/chart.min.js}"></script>
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --card-shadow: 0 2px 8px rgba(0,0,0,0.1);
            --hover-shadow: 0 4px 12px rgba(0,0,0,0.15);
            --border-radius: 0.375rem;
            --transition: all 0.3s ease;
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
        }

        .container { margin-top: 30px; }

        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            overflow: hidden;
            background: white;
        }

        .card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            border-bottom: none;
            padding: 1.5rem;
            font-weight: 600;
        }

        .card-header h2 {
            margin: 0;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-body {
            padding: 2rem;
        }

        .connection-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            margin-left: 10px;
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition);
        }

        .connected {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .disconnected {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
            font-style: italic;
        }

        .alert {
            margin-bottom: 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            backdrop-filter: blur(4px);
        }

        .alert-info {
            background: rgba(13, 202, 240, 0.1);
            border: 1px solid rgba(13, 202, 240, 0.2);
            color: #0dcaf0;
        }

        .real-time-table {
            width: 100%;
            margin-top: 1rem;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .real-time-table th {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 0.75rem;
            vertical-align: middle;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.875rem;
            border: none;
        }

        .real-time-table td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
            border: none;
            border-bottom: 1px solid #f1f3f4;
            background: white;
        }

        .real-time-table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
            transition: var(--transition);
        }

        .chart-container {
            width: 100%;
            height: 60px;
            position: relative;
        }

        .value-cell {
            font-size: 1.2em;
            font-weight: bold;
            min-width: 100px;
            text-align: center;
            color: #667eea;
            font-family: 'Courier New', monospace;
        }

        .btn-group-sm > .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .modal-backdrop {
            z-index: 1040;
        }

        .modal {
            z-index: 1050;
        }

        .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: var(--hover-shadow);
        }

        .modal-header {
            background: var(--primary-gradient);
            color: white;
            border-bottom: none;
            border-radius: 16px 16px 0 0;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        .chart-td {
            padding: 0.5rem !important;
            width: 45%;
        }

        .device-list {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            padding: 1.5rem;
            border-radius: 16px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
        }

        .device-list h3 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .logo-container {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 120px;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
        }

        .logo-container:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }

        .logo-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .device-item {
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.3);
            min-height: 70px;
        }

        .device-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .device-item.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .device-item.active .text-muted,
        .device-item.active .device-address {
            color: rgba(255, 255, 255, 0.85) !important;
        }

        .device-item.active .device-name {
            color: white;
        }

        .device-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            border: 2px solid white;
            position: relative;
        }

        .device-status.connected {
            background-color: #28a745;
            box-shadow: 0 0 12px rgba(40, 167, 69, 0.6);
            animation: pulse-green 2s infinite;
        }

        .device-status.disconnected {
            background-color: #dc3545;
            box-shadow: 0 0 12px rgba(220, 53, 69, 0.6);
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 12px rgba(40, 167, 69, 0.6); }
            50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.9); }
        }

        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 0 12px rgba(220, 53, 69, 0.6); }
            50% { box-shadow: 0 0 20px rgba(220, 53, 69, 0.9); }
        }

        /* 设备列表按钮优化 */
        .device-item .btn-group {
            display: flex;
            gap: 2px;
            flex-shrink: 0;
            align-items: center;
        }

        .device-item .btn-group .btn {
            padding: 0.5rem 0.25rem;
            font-size: 0.9rem;
            font-weight: 600;
            border-radius: 6px;
            min-width: 2rem;
            height: 3rem;
            writing-mode: vertical-rl;
            text-orientation: upright;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1.2;
            letter-spacing: 0.1em;
            white-space: nowrap;
            overflow: hidden;
        }

        /* 为了更好的兼容性，添加备用样式 */
        @supports not (writing-mode: vertical-rl) {
            .device-item .btn-group .btn {
                writing-mode: tb-rl;
                text-orientation: upright;
            }
        }

        .device-item .btn-group .btn-sm {
            padding: 0.5rem 0.25rem;
            font-size: 0.9rem;
            height: 3rem;
            writing-mode: vertical-rl;
            text-orientation: upright;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1.2;
            letter-spacing: 0.1em;
            white-space: nowrap;
            overflow: hidden;
            min-width: 2rem;
            border-radius: 6px;
        }

        /* 设备信息区域 */
        .device-info-area {
            flex: 1;
            min-width: 0;
            margin-right: 8px;
        }

        .device-info-area .device-name {
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .device-info-area .device-address {
            font-size: 0.75rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 按钮样式美化 */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
        }

        .btn-outline-warning {
            color: #fd7e14;
            border: 2px solid #fd7e14;
            background: transparent;
        }

        .btn-outline-warning:hover {
            background: var(--warning-gradient);
            color: white;
            border-color: transparent;
        }

        .btn-group-sm .btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin: 0 2px;
        }

        .btn-group-sm .btn:hover {
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            color: #667eea;
            border: 2px solid #667eea;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
        }

        .btn-outline-danger {
            color: #dc3545;
            border: 2px solid #dc3545;
            background: transparent;
        }

        .btn-outline-danger:hover {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            border-color: transparent;
        }

        /* 表单控件美化 */
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem;
            transition: var(--transition);
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        /* 开关按钮美化 */
        .form-check-input {
            width: 2.5em;
            height: 1.25em;
            margin-top: 0.25em;
            background-color: #e9ecef;
            border-color: #ced4da;
            cursor: pointer;
            transition: var(--transition);
        }

        .form-check-input:checked {
            background: var(--primary-gradient);
            border-color: #667eea;
        }

        .form-check-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* 按钮图标样式 */
        .btn i {
            font-size: 1rem;
            vertical-align: middle;
            margin-right: 0.5rem;
        }

        /* 操作列样式 */
        td .btn-group {
            display: flex;
            gap: 0.5rem;
        }

        /* 历史记录开关列样式 */
        td .form-check {
            display: flex;
            justify-content: center;
            margin: 0;
        }

        /* 历史记录状态列样式优化 */
        .history-status-column {
            min-width: 120px;
        }

        /* 统计功能状态列样式优化 */
        .stats-status-column {
            min-width: 120px;
        }

        .history-status-column .btn-group {
            gap: 2px;
        }

        .history-status-column .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        /* 历史记录切换按钮样式 */
        .history-toggle-btn {
            min-width: 80px;
            font-weight: 500;
            border-radius: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .history-toggle-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .history-toggle-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .history-toggle-btn.btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-color: #28a745;
            color: white;
        }

        .history-toggle-btn.btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
            border-color: #1e7e34;
        }

        .history-toggle-btn.btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
            border-color: #6c757d;
            color: white;
        }

        .history-toggle-btn.btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #727b84 100%);
            border-color: #545b62;
        }
        .user-info {
            position: absolute;
            top: 10px;
            right: 20px;
            z-index: 1000;
        }
        .user-info .username {
            margin-right: 10px;
            color: #6c757d;
        }

        /* 导航栏样式 */
        .navbar {
            background: var(--primary-gradient) !important;
            padding: 0.5rem 1rem;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.9);
        }
        .navbar .user-info {
            position: static;
            display: flex;
            align-items: center;
        }
        .navbar .username {
            color: white;
            margin-right: 15px;
            font-weight: 500;
        }
        .navbar .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: var(--transition);
        }
        .navbar .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-1px);
        }
        .navbar .btn-outline-light.active {
            background-color: rgba(255, 255, 255, 0.3);
            border-color: white;
            color: #495057;
        }

        /* 主容器样式 */
        .main-container {
            padding-top: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }

            .device-list {
                padding: 1rem;
            }

            .real-time-table th,
            .real-time-table td {
                padding: 0.5rem;
                font-size: 0.875rem;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            /* 移动设备上的设备按钮调整 */
            .device-item .btn-group .btn,
            .device-item .btn-group .btn-sm {
                height: 2.5rem;
                font-size: 0.8rem;
                min-width: 1.8rem;
                padding: 0.4rem 0.2rem;
            }

            .device-item {
                min-height: 65px;
            }
        }

        /* 加载动画 */
        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 预加载结果样式 */
        pre {
            background: rgba(248, 249, 250, 0.8);
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            color: #495057;
        }

        /* 表格响应式 */
        .table-responsive {
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
        }
    </style>
    </style>
</head>
<body>
    <!-- 引入导航条片段 -->
    <nav th:replace="fragments/navbar :: navbar('胜大科技智联管理系统', 'home', true, true, true, null)"></nav>
    
    <div class="container-fluid main-container">
        <div class="row">
            <!-- Device List Sidebar -->
            <div class="col-md-2">
                <div class="logo-container">
                    <img th:src="@{/system-images/logo.png}" alt="胜大科技智联管理系统" />
                </div>
                <div class="device-list">
                    <h3>设备列表</h3>
                    <div class="mb-3">
                        <button class="btn btn-primary btn-sm w-100" onclick="showAddDeviceModal()">
                            <i class="bi bi-plus-circle"></i> 添加设备
                        </button>
                    </div>
                    <div id="deviceListContainer">
                        <!-- 设备列表将动态添加到这里 -->
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <!-- Device Function Section -->
                <div id="deviceFunctionSection" style="display: none;">
                    <h2 class="mb-4">设备功能</h2>
                    <!-- Modbus Section -->
                    <div class="card">
                        <div class="card-header">
                            <h2>Modbus TCP 控制</h2>
                        </div>
                        <div class="card-body">
                            <!-- PLC Connection -->
                            <div class="mb-4">
                                <h3>设备状态
                                    <span id="connectionStatus" class="connection-status disconnected">未选择设备</span>
                                </h3>
                            </div>

                            <!-- Read Register -->
                            <div class="mb-4">
                                <h3>读取寄存器</h3>
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" id="readAddress" class="form-control" placeholder="寄存器地址">
                                        <div class="help-text">支持格式：30001+, 40001+, 直接偏移地址, Modbus Slave格式(如410001=10000)</div>
                                    </div>
                                    <div class="col-md-3">
                                        <button onclick="readRegister()" class="btn btn-primary" disabled>读取</button>
                                        <button onclick="showAddRealTimeModal()" class="btn btn-success" disabled>添加实时读取</button>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <pre id="readResult"></pre>
                                </div>
                            </div>

                            <!-- Batch Read Registers -->
                            <div class="mb-4">
                                <h3>批量读取寄存器</h3>
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" id="batchReadStartAddress" class="form-control" placeholder="起始地址">
                                        <div class="help-text">支持格式：30001+, 40001+, 直接偏移地址, Modbus Slave格式(如410001=10000)</div>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" id="batchReadCount" class="form-control" placeholder="读取数量" min="1" max="100" value="10">
                                        <div class="help-text">最多一次读取100个地址</div>
                                    </div>
                                    <div class="col-md-3">
                                        <button onclick="batchReadRegisters()" class="btn btn-primary" disabled>批量读取</button>
                                        <button onclick="exportBatchReadResult()" class="btn btn-secondary" disabled>导出数据</button>
                                    </div>
                                </div>
                                <div id="batchReadTableContainer" class="mt-3">
                                    <!-- 设备特定的批量读取结果将动态添加到这里 -->
                                </div>
                            </div>

                            <!-- Real-time Monitoring -->
                            <div class="mb-4">
                                <h3>
                                    实时监控
                                    <button id="cleanHistoryBtn" class="btn btn-outline-warning btn-sm ms-2" onclick="cleanInvalidHistoryData()" title="清理无效历史数据">
                                        <i class="bi bi-trash"></i> 清理无效历史数据
                                    </button>
                                </h3>
                                <div class="table-responsive">
                                    <table class="table table-bordered real-time-table" id="realTimeTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 14%" class="text-center">监控名称</th>
                                                <th style="width: 8%" class="text-center">当前值</th>
                                                <th style="width: 7%" class="text-center">更新间隔</th>
                                                <th style="width: 30%" class="chart-td text-center">数据趋势</th>
                                                <th style="width: 8%" class="text-center">历史记录</th>
                                                <th style="width: 8%" class="text-center">统计功能</th>
                                                <th style="width: 7%" class="text-center">排序</th>
                                                <th style="width: 18%" class="text-center">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="realTimeContainer">
                                            <!-- 实时数据项会动态添加到这里 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Write Registers -->
                            <div>
                                <h3>写入寄存器</h3>
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" id="writeAddress" class="form-control" placeholder="起始地址">
                                        <div class="help-text">支持格式：40001+, 直接偏移地址, Modbus Slave格式(如410001=10000)</div>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" id="writeValues" class="form-control" placeholder="值（用逗号分隔）">
                                    </div>
                                    <div class="col-md-6">
                                        <button onclick="writeRegisters()" class="btn btn-primary" disabled>写入</button>
                                        <button onclick="showImportModal()" class="btn btn-secondary" disabled>导入写入</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- MQTT Section -->
                    <div class="card">
                        <div class="card-header">
                            <h2>MQTT 控制</h2>
                        </div>
                        <div class="card-body">
                            <!-- Local Broker Info -->
                            <div class="alert alert-info">
                                <strong>本地MQTT Broker已启动</strong>
                                <p class="mb-0">地址：tcp://localhost:1883</p>
                                <p class="mb-0">WebSocket地址：ws://localhost:8083</p>
                            </div>

                            <!-- Connect -->
                            <div class="mb-4">
                                <h3>连接设置</h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="text" id="brokerUrl" class="form-control" placeholder="Broker 地址" value="tcp://localhost:1883">
                                        <div class="help-text">可以使用本地Broker或其他外部Broker</div>
                                    </div>
                                    <div class="col-md-3">
                                        <button onclick="connectMqtt()" class="btn btn-primary">连接</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Publish Message -->
                            <div>
                                <h3>发布消息</h3>
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" id="topic" class="form-control" placeholder="主题">
                                        <div class="help-text">Modbus数据会发布到 modbus/register/# 主题</div>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" id="message" class="form-control" placeholder="消息内容">
                                    </div>
                                    <div class="col-md-3">
                                        <button onclick="publishMessage()" class="btn btn-primary">发布</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Device Status Section -->
                <div id="deviceStatusSection">
                    <iframe src="/device/status" style="width: 100%; height: 100vh; border: none;"></iframe>
                </div>

                <!-- Device Config Section -->
                <div id="deviceConfigSection" style="display: none;">
                    <iframe id="deviceConfigFrame" style="width: 100%; height: 100vh; border: none;"></iframe>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Real-time Monitor Modal -->
    <div class="modal fade" id="addRealTimeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加实时监控</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="monitorName" class="form-label">监控名称</label>
                        <input type="text" class="form-control" id="monitorName" placeholder="请输入监控名称">
                    </div>
                    <div class="mb-3">
                        <label for="updateInterval" class="form-label">更新间隔（毫秒）</label>
                        <input type="number" class="form-control" id="updateInterval" value="1000" min="100">
                        <div class="form-text">建议最小间隔100毫秒</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addRealTimeMonitor()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Device Modal -->
    <div class="modal fade" id="addDeviceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加设备</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="deviceName" class="form-label">设备名称</label>
                        <input type="text" class="form-control" id="deviceName" placeholder="请输入设备名称">
                    </div>
                    <div class="mb-3">
                        <label for="deviceIp" class="form-label">IP地址</label>
                        <input type="text" class="form-control" id="deviceIp" placeholder="请输入IP地址">
                    </div>
                    <div class="mb-3">
                        <label for="devicePort" class="form-label">端口号</label>
                        <input type="number" class="form-control" id="devicePort" value="502">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addDevice()">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Data Modal -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入数据并写入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择导入文件</label>
                        <input type="file" class="form-control" id="importFile" accept=".txt">
                        <div class="form-text">请选择之前导出的文本文件（.txt格式）</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importAndWrite()">导入并写入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加导出历史记录模态框 -->
    <div class="modal fade" id="exportHistoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导出历史记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="exportMonitorId">
                    <div class="mb-3">
                        <label class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="exportStartTime">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="exportEndTime">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="exportHistory()">导出</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Device Modal -->
    <div class="modal fade" id="editDeviceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">修改设备信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editDeviceForm">
                        <div class="mb-3">
                            <label for="editDeviceName" class="form-label">设备名称</label>
                            <input type="text" class="form-control" id="editDeviceName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDeviceIp" class="form-label">IP地址</label>
                            <input type="text" class="form-control" id="editDeviceIp"
                                   pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的IP地址" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDevicePort" class="form-label">端口号</label>
                            <input type="number" class="form-control" id="editDevicePort"
                                   min="1" max="65535" value="502" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDeviceChanges()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录保留时间配置模态框 -->
    <div class="modal fade" id="retentionConfigModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">历史记录保留时间配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="retentionMonitorId">
                    <div class="mb-3">
                        <label class="form-label">保留时间单位</label>
                        <select class="form-select" id="retentionUnit" onchange="updateRetentionValueField()">
                            <option value="hours">小时</option>
                            <option value="days">天</option>
                            <option value="permanent">永久保存</option>
                        </select>
                    </div>
                    <div class="mb-3" id="retentionValueContainer">
                        <label class="form-label">保留时间值</label>
                        <input type="number" class="form-control" id="retentionValue" min="1" value="24">
                        <div class="form-text">请输入正整数</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveRetentionConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 小时统计数据模态框 -->
    <div class="modal fade" id="hourlyStatsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">小时统计数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="statsMonitorId">
                    <div class="mb-3">
                        <label class="form-label">查看时间范围</label>
                        <select class="form-select" id="statsTimeRange" onchange="loadHourlyStats()">
                            <option value="3">最近3小时</option>
                            <option value="6">最近6小时</option>
                            <option value="24" selected>最近24小时</option>
                            <option value="48">最近48小时</option>
                            <option value="72">最近72小时</option>
                            <option value="168">最近7天</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary btn-sm" onclick="loadHourlyStats()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="manualRecalculateStats()">
                            <i class="bi bi-calculator"></i> 手动重新计算
                        </button>
                    </div>
                    <div id="hourlyStatsContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script>
        let realTimeMonitors = new Map(); // 存储实时监控项
        let addModal = null;
        const MAX_DATA_POINTS = 10; // 最大数据点数量
        let editingMonitorId = null; // 当前正在编辑的监控ID
        let devices = new Map(); // 存储设备列表
        let currentDeviceId = null; // 当前选中的设备ID
        let addDeviceModal = null;
        let editDeviceModal = null;
        let deviceMonitors = new Map(); // 存储每个设备的监控配置
        let retentionModal = null;
        let hourlyStatsModal = null;

        // 生成UUID的函数
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        document.addEventListener('DOMContentLoaded', async function() {
            addModal = new bootstrap.Modal(document.getElementById('addRealTimeModal'));
            addDeviceModal = new bootstrap.Modal(document.getElementById('addDeviceModal'));
            editDeviceModal = new bootstrap.Modal(document.getElementById('editDeviceModal'));
            importModal = new bootstrap.Modal(document.getElementById('importModal'));
            exportModal = new bootstrap.Modal(document.getElementById('exportHistoryModal'));
            retentionModal = new bootstrap.Modal(document.getElementById('retentionConfigModal'));
            hourlyStatsModal = new bootstrap.Modal(document.getElementById('hourlyStatsModal'));
            
            // 清空表单
            document.getElementById('addRealTimeModal').addEventListener('hidden.bs.modal', function () {
                document.getElementById('monitorName').value = '';
                document.getElementById('updateInterval').value = '1000';
                editingMonitorId = null; // 重置编辑状态
            });

            // 修改确定按钮的点击事件
            document.querySelector('#addRealTimeModal .btn-primary').onclick = function() {
                if (editingMonitorId) {
                    updateMonitor();
                } else {
                    addRealTimeMonitor();
                }
            };

            // 加载设备列表和监控项
            await loadDevicesAndMonitors();
            
            // 默认显示设备状态概览
            showHome();
        });

        // 加载设备列表和监控项
        async function loadDevicesAndMonitors() {
            try {
                console.log('开始加载设备列表和监控项');
                // 加载设备列表
                const response = await fetch('/api/devices');
                if (!response.ok) {
                    throw new Error('获取设备列表失败');
                }
                const deviceList = await response.json();
                console.log('获取到设备列表:', deviceList);
                
                devices.clear();
                deviceMonitors.clear();

                // 加载每个设备的监控项
                for (const device of deviceList) {
                    console.log('处理设备:', device);
                    devices.set(device.id, device);
                    try {
                        const monitorResponse = await fetch(`/api/device/${device.id}/data-items`);
                        if (!monitorResponse.ok) {
                            console.error(`加载设备 ${device.id} 的监控项失败:`, await monitorResponse.text());
                            continue;
                        }
                        const monitors = await monitorResponse.json();
                        console.log(`设备 ${device.id} 的监控项:`, monitors);
                        
                        const monitorMap = new Map();
                        if (Array.isArray(monitors)) {
                            monitors.forEach(monitor => {
                                // 不再检查deviceId，因为它是后端保证的
                                monitorMap.set(monitor.id, {
                                    id: monitor.id,
                                    name: monitor.name,
                                    address: monitor.address,
                                    interval: monitor.refreshInterval,
                                    deviceId: device.id,  // 使用设备ID而不是监控项中的deviceId
                                    historyEnabled: monitor.historyEnabled !== undefined ? monitor.historyEnabled : true,  // 添加历史记录开关状态，默认为true
                                    historyRetentionHours: monitor.historyRetentionHours !== undefined ? monitor.historyRetentionHours : 720,  // 添加历史记录保留时间，默认为720小时（30天）
                                    statsEnabled: monitor.statsEnabled !== undefined ? monitor.statsEnabled : false  // 添加统计功能开关状态，默认为false
                                });
                            });
                        } else {
                            console.warn(`设备 ${device.id} 的监控项数据不是数组:`, monitors);
                        }
                        deviceMonitors.set(device.id, monitorMap);
                    } catch (error) {
                        console.error(`加载设备 ${device.id} 的监控项时出错:`, error);
                    }
                }

                console.log('设备列表加载完成，开始更新界面');
                updateDeviceList();
                console.log('界面更新完成');
            } catch (error) {
                console.error('加载设备和监控项失败:', error);
                alert('加载设备和监控项失败: ' + error.message);
            }
        }

        // PLC Connection Functions
        async function connectPLC() {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }
            await connectDevice(currentDeviceId);
        }

        async function disconnectPLC() {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }
            await disconnectDevice(currentDeviceId);
        }

        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const readButton = document.querySelector('button[onclick="readRegister()"]');
            const writeButton = document.querySelector('button[onclick="writeRegisters()"]');
            const addRealTimeButton = document.querySelector('button[onclick="showAddRealTimeModal()"]');
            const batchReadButton = document.querySelector('button[onclick="batchReadRegisters()"]');
            const exportButton = document.querySelector('button[onclick="exportBatchReadResult()"]');
            const importButton = document.querySelector('button[onclick="showImportModal()"]');

            if (!currentDeviceId) {
                statusElement.textContent = '未选择设备';
                statusElement.className = 'connection-status disconnected';
                readButton.disabled = true;
                writeButton.disabled = true;
                addRealTimeButton.disabled = true;
                batchReadButton.disabled = true;
                exportButton.disabled = true;
                importButton.disabled = true;
                return;
            }

            const device = devices.get(currentDeviceId);
            if (!device) {
                statusElement.textContent = '设备不存在';
                statusElement.className = 'connection-status disconnected';
                readButton.disabled = true;
                writeButton.disabled = true;
                addRealTimeButton.disabled = true;
                batchReadButton.disabled = true;
                exportButton.disabled = true;
                importButton.disabled = true;
            }

            if (device.connected) {
                statusElement.textContent = `已连接 - ${device.name}`;
                statusElement.className = 'connection-status connected';
                readButton.disabled = false;
                writeButton.disabled = false;
                addRealTimeButton.disabled = false;
                batchReadButton.disabled = false;
                exportButton.disabled = false;
                importButton.disabled = false;
            } else {
                statusElement.textContent = `未连接 - ${device.name}`;
                statusElement.className = 'connection-status disconnected';
                readButton.disabled = true;
                writeButton.disabled = true;
                addRealTimeButton.disabled = true;
                batchReadButton.disabled = true;
                exportButton.disabled = true;
                importButton.disabled = true;
            }
        }

        // Modbus Functions
        async function readRegister() {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }
            
            const device = devices.get(currentDeviceId);
            if (!device || !device.connected) {
                alert('请先连接设备');
                return;
            }

            const address = document.getElementById('readAddress').value;
            try {
                const response = await fetch(`/api/modbus/read?deviceId=${currentDeviceId}&address=${encodeURIComponent(address)}`);
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '读取失败');
                }
                const data = await response.json();
                document.getElementById('readResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                alert('读取寄存器失败：' + error.message);
            }
        }

        async function writeRegisters(silent = false) {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }
            
            const device = devices.get(currentDeviceId);
            if (!device || !device.connected) {
                alert('请先连接设备');
                return;
            }

            const address = document.getElementById('writeAddress').value;
            const values = document.getElementById('writeValues').value.split(',').map(Number);
            
            try {
                const response = await fetch(`/api/modbus/write?deviceId=${currentDeviceId}&address=${address}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(values)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '写入失败');
                }
                
                if (!silent) {
                    alert('写入成功');
                }
                return true;
            } catch (error) {
                alert('写入寄存器失败：' + error.message);
                throw error;
            }
        }

        // 添加实时监控项（带配置）
        async function addRealTimeMonitorWithConfig(address, name, interval, monitorId = generateUUID(), shouldSave = true) {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }

            const device = devices.get(currentDeviceId);
            if (!device || !device.connected) {
                alert('请先连接设备');
                return;
            }

            const monitor = {
                id: monitorId,
                address: address,
                name: name,
                interval: interval,
                deviceId: currentDeviceId,
                timerId: null,
                data: Array(MAX_DATA_POINTS).fill(null),
                chart: null,
                historyEnabled: false,  // 默认关闭
                historyRetentionHours: 720,  // 默认30天
                statsEnabled: false  // 默认关闭
            };

            // 如果是从deviceMonitors恢复的监控项，使用保存的状态
            if (!shouldSave && deviceMonitors.has(currentDeviceId)) {
                const savedMonitor = deviceMonitors.get(currentDeviceId).get(monitorId);
                if (savedMonitor) {
                    if (savedMonitor.historyEnabled !== undefined) {
                        monitor.historyEnabled = savedMonitor.historyEnabled;
                    }
                    if (savedMonitor.historyRetentionHours !== undefined) {
                        monitor.historyRetentionHours = savedMonitor.historyRetentionHours;
                    }
                    if (savedMonitor.statsEnabled !== undefined) {
                        monitor.statsEnabled = savedMonitor.statsEnabled;
                    }
                }
            }

            // 只在需要时保存到数据库
            if (shouldSave) {
                try {
                    const response = await fetch(`/api/device/${currentDeviceId}/data-item`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            id: monitorId,
                            name: name,
                            address: address,
                            refreshInterval: interval,
                            deviceId: currentDeviceId,
                            historyEnabled: monitor.historyEnabled,
                            historyRetentionHours: monitor.historyRetentionHours,
                            statsEnabled: monitor.statsEnabled
                        })
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || '保存监控项失败');
                    }
                } catch (error) {
                    console.error('保存监控项失败:', error);
                    alert('保存监控项失败：' + error.message);
                    return;
                }
            }

            // 添加到界面
            const container = document.getElementById('realTimeContainer');
            const row = document.createElement('tr');
            row.id = `monitor-${monitorId}`;
            row.innerHTML = `
                <td class="text-center">${name}<br><small class="text-muted">${address}</small></td>
                <td class="value-cell" id="value-${monitorId}">等待更新...</td>
                <td class="text-center">${interval} ms</td>
                <td class="chart-td">
                    <div class="chart-container">
                        <canvas id="chart-${monitorId}"></canvas>
                    </div>
                </td>
                <td class="history-status-column">
                    <div class="d-flex flex-column align-items-center">
                        <button class="btn history-toggle-btn ${monitor.historyEnabled ? 'btn-success' : 'btn-secondary'} btn-sm mb-2"
                                id="history-toggle-${monitorId}"
                                onclick="toggleHistory('${monitorId}')"
                                title="点击切换历史记录状态">
                            <i class="bi ${monitor.historyEnabled ? 'bi-check-circle' : 'bi-x-circle'} me-1"></i>
                            ${monitor.historyEnabled ? '已启用' : '已关闭'}
                        </button>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-primary"
                                onclick="showExportModal('${monitorId}')"
                                title="导出历史记录">
                                <i class="bi bi-download"></i>
                            </button>
                            <button class="btn btn-outline-secondary"
                                onclick="showRetentionConfigModal('${monitorId}')"
                                title="配置历史记录保留时间"
                                ${monitor.historyEnabled ? '' : 'disabled'}>
                                <i class="bi bi-gear"></i>
                            </button>
                        </div>
                    </div>
                </td>
                <td class="stats-status-column">
                    <div class="d-flex flex-column align-items-center">
                        <button class="btn stats-toggle-btn ${monitor.statsEnabled ? 'btn-success' : 'btn-secondary'} btn-sm mb-2"
                                id="stats-toggle-${monitorId}"
                                onclick="toggleStats('${monitorId}')"
                                title="点击切换统计功能状态">
                            <i class="bi ${monitor.statsEnabled ? 'bi-check-circle' : 'bi-x-circle'} me-1"></i>
                            ${monitor.statsEnabled ? '已启用' : '已关闭'}
                        </button>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-info"
                                onclick="showHourlyStatsModal('${monitorId}')"
                                title="查看小时统计数据"
                                ${monitor.statsEnabled ? '' : 'disabled'}>
                                <i class="bi bi-bar-chart"></i>
                            </button>
                        </div>
                    </div>
                </td>
                <td class="text-center">
                    <div class="btn-group-vertical btn-group-sm">
                        <button class="btn btn-outline-secondary btn-sm" onclick="moveMonitorUp('${monitorId}')" title="上移">
                            <i class="bi bi-arrow-up"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="moveMonitorDown('${monitorId}')" title="下移">
                            <i class="bi bi-arrow-down"></i>
                        </button>
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editMonitor('${monitorId}')" title="编辑监控项">
                            <i class="bi bi-pencil-square"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="removeMonitor('${monitorId}')" title="删除监控项">
                            <i class="bi bi-trash3"></i>
                        </button>
                    </div>
                </td>
            `;
            container.appendChild(row);

            // 初始化图表
            const ctx = document.getElementById(`chart-${monitorId}`).getContext('2d');
            monitor.chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(MAX_DATA_POINTS).fill(''),
                    datasets: [{
                        data: monitor.data,
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: false
                            }
                        },
                        x: {
                            ticks: {
                                display: false
                            },
                            grid: {
                                display: false
                            }
                        }
                    },
                    animation: {
                        duration: 0
                    }
                }
            });

            // 立即读取数据，不等待第一个定时周期
            (async () => {
                try {
                    const response = await fetch(`/api/data-items/${monitorId}/latest-value`);
                    if (!response.ok) {
                        throw new Error('获取数据失败');
                    }
                    const data = await response.json();
                    
                    // 更新显示值
                    const valueElement = document.getElementById(`value-${monitorId}`);
                    if (valueElement) {
                        valueElement.textContent = data.value;
                    }
                    
                    // 更新图表数据
                    monitor.data.push(data.value);
                    if (monitor.data.length > MAX_DATA_POINTS) {
                        monitor.data.shift();
                    }
                    
                    // 更新图表
                    if (monitor.chart) {
                        monitor.chart.data.datasets[0].data = monitor.data;
                        monitor.chart.update('none');
                    }
                } catch (error) {
                    console.error('初始数据获取失败:', error);
                    const valueElement = document.getElementById(`value-${monitorId}`);
                    if (valueElement) {
                        valueElement.textContent = '读取错误';
                    }
                }
            })();

            // 设置定时器获取最新数据
            monitor.timerId = setInterval(async () => {
                try {
                    const response = await fetch(`/api/data-items/${monitorId}/latest-value`);
                    if (!response.ok) {
                        throw new Error('获取数据失败');
                    }
                    const data = await response.json();
                    
                    // 更新显示值
                    const valueElement = document.getElementById(`value-${monitorId}`);
                    if (valueElement) {
                        valueElement.textContent = data.value;
                    }
                    
                    // 更新图表数据
                    monitor.data.push(data.value);
                    if (monitor.data.length > MAX_DATA_POINTS) {
                        monitor.data.shift();
                    }
                    
                    // 更新图表
                    if (monitor.chart) {
                        monitor.chart.data.datasets[0].data = monitor.data;
                        monitor.chart.update('none');
                    }
                } catch (error) {
                    console.error('获取数据失败:', error);
                    const valueElement = document.getElementById(`value-${monitorId}`);
                    if (valueElement) {
                        valueElement.textContent = '读取错误';
                    }
                }
            }, interval);

            realTimeMonitors.set(monitorId, monitor);
            
            // 同时保存到设备监控配置中
            if (!deviceMonitors.has(currentDeviceId)) {
                deviceMonitors.set(currentDeviceId, new Map());
            }
            deviceMonitors.get(currentDeviceId).set(monitorId, {
                id: monitorId,
                name: name,
                address: address,
                interval: interval,
                deviceId: currentDeviceId,
                historyEnabled: monitor.historyEnabled,
                historyRetentionHours: monitor.historyRetentionHours
            });
        }

        async function removeMonitor(monitorId) {
            const monitor = realTimeMonitors.get(monitorId);
            if (monitor) {
                // 添加确认对话框
                if (!confirm(`确定要删除监控项"${monitor.name}"吗？`)) {
                    return;
                }

                try {
                    // 从数据库中删除
                    const response = await fetch(`/api/data-item/${monitorId}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || '删除监控项失败');
                    }

                    // 停止定时器
                    if (monitor.timerId) {
                        clearInterval(monitor.timerId);
                    }
                    // 销毁图表
                    if (monitor.chart) {
                        monitor.chart.destroy();
                    }
                    // 移除DOM元素
                    const element = document.getElementById(`monitor-${monitorId}`);
                    if (element) {
                        element.remove();
                    }
                    // 从Map中删除
                    realTimeMonitors.delete(monitorId);
                    
                    // 同时从设备监控配置中删除
                    if (deviceMonitors.has(currentDeviceId)) {
                        const deviceMonitorMap = deviceMonitors.get(currentDeviceId);
                        deviceMonitorMap.delete(monitorId);
                    }
                } catch (error) {
                    console.error('删除监控项失败:', error);
                    alert('删除监控项失败：' + error.message);
                }
            }
        }

        function editMonitor(monitorId) {
            const monitor = realTimeMonitors.get(monitorId);
            if (monitor) {
                editingMonitorId = monitorId;
                
                // 填充表单
                document.getElementById('monitorName').value = monitor.name;
                document.getElementById('updateInterval').value = monitor.interval;
                document.getElementById('readAddress').value = monitor.address;
                
                // 修改模态框标题
                document.querySelector('#addRealTimeModal .modal-title').textContent = '编辑监控';
                
                // 显示模态框
                addModal.show();
            }
        }

        function stopAllMonitors() {
            realTimeMonitors.forEach(monitor => {
                if (monitor.timerId) {
                    clearInterval(monitor.timerId);
                    monitor.timerId = null;
                }
            });
        }

        // MQTT Functions
        async function connectMqtt() {
            const brokerUrl = document.getElementById('brokerUrl').value;
            try {
                const response = await fetch(`/api/mqtt/connect?brokerUrl=${encodeURIComponent(brokerUrl)}`, {
                    method: 'POST'
                });
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '连接失败');
                }
                alert('成功连接到MQTT broker');
            } catch (error) {
                alert('连接MQTT broker失败：' + error.message);
            }
        }

        async function publishMessage() {
            const topic = document.getElementById('topic').value;
            const message = document.getElementById('message').value;
            try {
                const response = await fetch(`/api/mqtt/publish?topic=${encodeURIComponent(topic)}&message=${encodeURIComponent(message)}`, {
                    method: 'POST'
                });
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '发布失败');
                }
                alert('消息发布成功');
            } catch (error) {
                alert('发布消息失败：' + error.message);
            }
        }

        async function updateMonitor() {
            const monitor = realTimeMonitors.get(editingMonitorId);
            if (!monitor) return;

            const newName = document.getElementById('monitorName').value;
            const newInterval = parseInt(document.getElementById('updateInterval').value);

            if (!newName) {
                alert('请输入监控名称');
                return;
            }

            if (newInterval < 100) {
                alert('更新间隔不能小于100毫秒');
                return;
            }

            try {
                // 添加调试日志
                console.log('更新监控项参数:', {
                    id: editingMonitorId,
                    name: newName,
                    refreshInterval: newInterval
                });

                // 调用后端API更新数据库
                const response = await fetch(`/api/data-item/${editingMonitorId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: newName,
                        refreshInterval: newInterval
                    })
                });

                let responseData;
                try {
                    responseData = await response.json();
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    throw new Error('服务器响应格式错误');
                }

                // 添加响应调试日志
                if (!response.ok) {
                    console.error('更新监控项响应:', responseData);
                    throw new Error(responseData.message || '更新失败');
                }

                console.log('更新监控项成功:', responseData);

                // 更新名称显示
                const nameCell = document.querySelector(`#monitor-${editingMonitorId} td:first-child`);
                nameCell.innerHTML = `${newName}<br><small class="text-muted">${monitor.address}</small>`;

                // 更新间隔单元格显示 - 新增代码
                const intervalCell = document.querySelector(`#monitor-${editingMonitorId} td:nth-child(3)`);
                intervalCell.innerHTML = `${newInterval} ms`;

                // 更新定时器间隔
                if (monitor.interval !== newInterval) {
                    clearInterval(monitor.timerId);
                    // 使用与添加监控项时相同的定时器逻辑
                    monitor.timerId = setInterval(async () => {
                        try {
                            // 使用monitor.id而非editingMonitorId确保正确获取数据
                            const response = await fetch(`/api/data-items/${monitor.id}/latest-value`);
                            if (!response.ok) {
                                throw new Error('获取数据失败');
                            }
                            const data = await response.json();
                            
                            // 更新显示值
                            const valueElement = document.getElementById(`value-${monitor.id}`);
                            if (valueElement) {
                                valueElement.textContent = data.value;
                            }
                            
                            // 更新图表数据
                            monitor.data.push(data.value);
                            if (monitor.data.length > MAX_DATA_POINTS) {
                                monitor.data.shift();
                            }
                            
                            // 更新图表
                            if (monitor.chart) {
                                monitor.chart.data.datasets[0].data = monitor.data;
                                monitor.chart.update('none');
                            }
                        } catch (error) {
                            console.error('获取数据失败:', error);
                            const valueElement = document.getElementById(`value-${monitor.id}`);
                            if (valueElement) {
                                valueElement.textContent = '读取错误';
                            }
                        }
                    }, newInterval);
                    
                    // 添加调试日志
                    console.log(`已更新监控项 ${monitor.id} 的定时器间隔为 ${newInterval}ms`);
                }

                // 更新监控项属性
                monitor.name = newName;
                monitor.interval = newInterval;

                // 同时更新设备监控配置中的数据
                if (deviceMonitors.has(currentDeviceId)) {
                    const deviceMonitorMap = deviceMonitors.get(currentDeviceId);
                    const savedMonitor = deviceMonitorMap.get(editingMonitorId);
                    if (savedMonitor) {
                        savedMonitor.name = newName;
                        savedMonitor.interval = newInterval;
                    }
                }

                // 关闭模态框
                addModal.hide();
            } catch (error) {
                console.error('更新监控项失败:', error);
                alert(error.message || '更新失败');
            }
        }

        // 修改模态框关闭事件，重置标题
        document.getElementById('addRealTimeModal').addEventListener('hidden.bs.modal', function () {
            document.querySelector('#addRealTimeModal .modal-title').textContent = '添加实时监控';
        });

        // Real-time Monitor Functions
        function showAddRealTimeModal() {
            const address = document.getElementById('readAddress').value;
            if (!address) {
                alert('请先输入要监控的寄存器地址');
                return;
            }
            // 重置模态框标题
            document.querySelector('#addRealTimeModal .modal-title').textContent = '添加实时监控';
            addModal.show();
        }

        function addRealTimeMonitor() {
            const address = document.getElementById('readAddress').value;
            const name = document.getElementById('monitorName').value;
            const interval = parseInt(document.getElementById('updateInterval').value);

            if (!name) {
                alert('请输入监控名称');
                return;
            }

            if (interval < 100) {
                alert('更新间隔不能小于100毫秒');
                return;
            }

            // 使用配置添加监控
            addRealTimeMonitorWithConfig(address, name, interval)
                .then(() => {
                    // 关闭模态框
                    addModal.hide();
                })
                .catch(error => {
                    console.error('添加监控失败:', error);
                });
        }

        // 添加写入保护的辅助函数
        function setWriteProtection(address, isWriting) {
            realTimeMonitors.forEach(monitor => {
                if (monitor.address === address) {
                    monitor.isWriting = isWriting;
                    if (isWriting) {
                        monitor.needsClear = true;  // 标记需要清空数据
                    }
                }
            });
        }

        function showAddDeviceModal() {
            document.getElementById('deviceName').value = '';
            document.getElementById('deviceIp').value = '';
            document.getElementById('devicePort').value = '502';
            addDeviceModal.show();
        }

        function showEditDeviceModal(deviceId) {
            const device = devices.get(deviceId);
            if (!device) {
                alert('设备不存在');
                return;
            }

            // 填充表单数据
            document.getElementById('editDeviceName').value = device.name;
            document.getElementById('editDeviceIp').value = device.address;
            document.getElementById('editDevicePort').value = device.port;

            // 保存当前编辑的设备ID
            document.getElementById('editDeviceForm').dataset.deviceId = deviceId;

            editDeviceModal.show();
        }

        async function saveDeviceChanges() {
            const form = document.getElementById('editDeviceForm');
            const deviceId = form.dataset.deviceId;

            if (!deviceId) {
                alert('设备ID不存在');
                return;
            }

            const name = document.getElementById('editDeviceName').value.trim();
            const address = document.getElementById('editDeviceIp').value.trim();
            const port = parseInt(document.getElementById('editDevicePort').value);

            // 验证输入
            if (!name || !address || !port) {
                alert('请填写完整的设备信息');
                return;
            }

            if (port < 1 || port > 65535) {
                alert('端口号必须在1-65535之间');
                return;
            }

            // IP地址格式验证
            const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            if (!ipPattern.test(address)) {
                alert('请输入有效的IP地址');
                return;
            }

            try {
                const response = await fetch(`/api/device/${deviceId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        address: address,
                        port: port
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '修改失败');
                }

                const updatedDevice = await response.json();
                devices.set(deviceId, updatedDevice);
                updateDeviceList();
                editDeviceModal.hide();

                alert('设备信息修改成功');
            } catch (error) {
                console.error('修改设备失败:', error);
                alert('修改设备失败: ' + error.message);
            }
        }

        // 页面加载完成后获取设备列表
        document.addEventListener('DOMContentLoaded', async function() {
            await loadDevices();
        });

        // 从后端加载设备列表
        async function loadDevices() {
            try {
                const response = await fetch('/api/devices');
                if (!response.ok) {
                    throw new Error('获取设备列表失败');
                }
                const deviceList = await response.json();
                devices.clear();
                deviceMonitors.clear();
                deviceList.forEach(device => {
                    devices.set(device.id, device);
                    deviceMonitors.set(device.id, new Map());
                });
                updateDeviceList();
            } catch (error) {
                console.error('加载设备列表失败:', error);
                alert('加载设备列表失败: ' + error.message);
            }
        }

        async function addDevice() {
            const name = document.getElementById('deviceName').value;
            const ip = document.getElementById('deviceIp').value;
            const port = document.getElementById('devicePort').value;

            if (!name || !ip || !port) {
                alert('请填写完整的设备信息');
                return;
            }

            try {
                const response = await fetch('/api/device', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: generateUUID(),
                        name: name,
                        address: ip,
                        port: parseInt(port)
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '添加设备失败');
                }

                const device = await response.json();
                devices.set(device.id, device);
                deviceMonitors.set(device.id, new Map());
                updateDeviceList();
                addDeviceModal.hide();
            } catch (error) {
                console.error('添加设备失败:', error);
                alert('添加设备失败: ' + error.message);
            }
        }

        function updateDeviceList() {
            const container = document.getElementById('deviceListContainer');
            container.innerHTML = '';

            devices.forEach((device, id) => {
                const deviceElement = document.createElement('div');
                deviceElement.className = `device-item ${id === currentDeviceId ? 'active' : ''}`;
                deviceElement.setAttribute('data-device-id', id);
                deviceElement.onclick = () => selectDevice(id);
                deviceElement.innerHTML = `
                    <div class="device-info-area">
                        <div class="d-flex align-items-center">
                            <span class="device-status ${device.connected ? 'connected' : 'disconnected'}"></span>
                            <div class="device-name">${device.name}</div>
                        </div>
                        <div class="device-address text-muted">${device.address}:${device.port}</div>
                        <div class="btn-group btn-group-sm mt-2" style="width: 100%;">
                            <button class="btn btn-sm btn-${device.connected ? 'danger' : 'success'}"
                                    onclick="event.stopPropagation(); ${device.connected ? 'disconnectDevice' : 'connectDevice'}('${id}')">
                                ${device.connected ? '断开' : '连接'}
                            </button>
                            <button class="btn btn-sm btn-warning"
                                    onclick="event.stopPropagation(); showEditDeviceModal('${id}')">
                                修改
                            </button>
                            <button class="btn btn-sm btn-primary"
                                    onclick="event.stopPropagation(); showDeviceConfig('${id}')">
                                配置
                            </button>
                            <button class="btn btn-sm btn-danger"
                                    onclick="event.stopPropagation(); deleteDevice('${id}')"
                                    ${device.connected ? 'disabled' : ''}>
                                删除
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(deviceElement);
            });
        }

        async function deleteDevice(deviceId) {
            const device = devices.get(deviceId);
            if (!device) return;

            if (device.connected) {
                alert('请先断开设备连接后再删除');
                return;
            }

            if (!confirm(`确定要删除设备 "${device.name}" 吗？`)) {
                return;
            }

            try {
                const response = await fetch(`/api/device/${deviceId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '删除设备失败');
                }

                // 如果删除的是当前选中的设备，清空当前设备
                if (deviceId === currentDeviceId) {
                    currentDeviceId = null;
                    realTimeMonitors.clear();
                    const container = document.getElementById('realTimeContainer');
                    container.innerHTML = '';
                    updateConnectionStatus();
                }

                // 删除设备相关的所有数据
                devices.delete(deviceId);
                deviceMonitors.delete(deviceId);
                
                updateDeviceList();
                
                // 清空读写结果
                document.getElementById('readResult').textContent = '';
                document.getElementById('readAddress').value = '';
                document.getElementById('writeAddress').value = '';
                document.getElementById('writeValues').value = '';
            } catch (error) {
                console.error('删除设备失败:', error);
                alert('删除设备失败: ' + error.message);
            }
        }

        async function switchDevice(deviceId) {
            if (currentDeviceId === deviceId) return;
            
            console.log('切换设备:', deviceId);
            
            // 停止当前设备的所有监控
            stopAllMonitors();
            
            // 隐藏当前设备的批量读取结果
            if (currentDeviceId) {
                const currentBatchContainer = document.getElementById(`batchReadTableContainer-${currentDeviceId}`);
                if (currentBatchContainer) {
                    currentBatchContainer.style.display = 'none';
                }
            }
            
            currentDeviceId = deviceId;
            
            // 清空当前监控
            realTimeMonitors.clear();
            const container = document.getElementById('realTimeContainer');
            container.innerHTML = '';
            
            try {
                // 恢复新设备的监控配置
                if (deviceMonitors.has(deviceId)) {
                    console.log('恢复设备监控配置:', deviceId);
                    const savedMonitors = deviceMonitors.get(deviceId);
                    console.log('已保存的监控项:', savedMonitors);
                    for (const monitor of savedMonitors.values()) {
                        try {
                            if (!realTimeMonitors.has(monitor.id)) {
                                await addRealTimeMonitorWithConfig(
                                    monitor.address,
                                    monitor.name,
                                    monitor.interval,
                                    monitor.id,
                                    false  // 不保存到数据库
                                );
                            }
                        } catch (error) {
                            console.error('恢复监控项失败:', monitor, error);
                        }
                    }
                }

                // 显示当前设备的批量读取结果（如果有）
                const batchContainer = document.getElementById(`batchReadTableContainer-${deviceId}`);
                if (batchContainer) {
                    batchContainer.style.display = 'block';
                }
                
                updateDeviceList();
                updateConnectionStatus();
                
                // 清空读写结果
                document.getElementById('readResult').textContent = '';
                
                console.log('设备切换完成:', deviceId);
            } catch (error) {
                console.error('设备切换失败:', error);
                alert('设备切换失败: ' + error.message);
            }
        }

        async function connectDevice(deviceId) {
            const device = devices.get(deviceId);
            if (!device) {
                console.error('设备不存在:', deviceId);
                return;
            }

            console.log('开始连接设备:', deviceId);
            try {
                const response = await fetch('/api/modbus/connect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        deviceId: deviceId,
                        host: device.address,
                        port: device.port
                    })
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '连接失败');
                }
                
                device.connected = true;
                if (deviceId === currentDeviceId) {
                    console.log('更新连接状态');
                    updateConnectionStatus();
                    
                    // 恢复之前保存的监控配置
                    if (deviceMonitors.has(deviceId)) {
                        console.log('恢复设备监控配置:', deviceId);
                        const savedMonitors = deviceMonitors.get(deviceId);
                        console.log('已保存的监控项:', savedMonitors);
                        for (const monitor of savedMonitors.values()) {
                            try {
                                if (!realTimeMonitors.has(monitor.id)) {
                                    await addRealTimeMonitorWithConfig(
                                        monitor.address,
                                        monitor.name,
                                        monitor.interval,
                                        monitor.id,
                                        false  // 不保存到数据库
                                    );
                                }
                            } catch (error) {
                                console.error('恢复监控项失败:', monitor, error);
                            }
                        }
                    }
                }
                updateDeviceList();
                console.log('设备连接成功:', deviceId);
                alert('设备连接成功');
            } catch (error) {
                console.error('连接设备失败:', error);
                alert('连接设备失败：' + error.message);
            }
        }

        async function disconnectDevice(deviceId) {
            const device = devices.get(deviceId);
            if (!device) return;

            try {
                const response = await fetch('/api/modbus/disconnect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ deviceId: deviceId })
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '断开连接失败');
                }
                
                device.connected = false;
                if (deviceId === currentDeviceId) {
                    stopAllMonitors();
                    // 保存监控配置但停止更新
                    if (!deviceMonitors.has(deviceId)) {
                        const monitorMap = new Map();
                        realTimeMonitors.forEach((monitor, id) => {
                            const checkbox = document.getElementById(`history-${id}`);
                            monitorMap.set(id, {
                                id: monitor.id,
                                name: monitor.name,
                                address: monitor.address,
                                interval: monitor.interval,
                                deviceId: monitor.deviceId,
                                historyEnabled: checkbox ? checkbox.checked : true,
                                historyRetentionHours: monitor.historyRetentionHours
                            });
                        });
                        deviceMonitors.set(deviceId, monitorMap);
                    }
                }
                updateDeviceList();
                updateConnectionStatus();
                alert('已断开设备连接');
            } catch (error) {
                alert('断开连接失败：' + error.message);
            }
        }

        // 修改批量读取功能
        async function batchReadRegisters() {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }
            
            const device = devices.get(currentDeviceId);
            if (!device || !device.connected) {
                alert('请先连接设备');
                return;
            }

            const startAddress = document.getElementById('batchReadStartAddress').value;
            const count = parseInt(document.getElementById('batchReadCount').value);

            if (!startAddress) {
                alert('请输入起始地址');
                return;
            }

            if (!count || count < 1 || count > 100) {
                alert('请输入有效的读取数量（1-100）');
                return;
            }

            try {
                // 禁用批量读取按钮，防止重复点击
                const batchReadButton = document.querySelector('button[onclick="batchReadRegisters()"]');
                batchReadButton.disabled = true;

                const response = await fetch(`/api/modbus/batch-read?deviceId=${currentDeviceId}&address=${encodeURIComponent(startAddress)}&count=${count}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                });

                // 重新启用批量读取按钮
                batchReadButton.disabled = false;

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '读取失败');
                }

                const data = await response.json();
                
                // 验证返回的数据是否属于当前设备
                if (data.deviceId !== currentDeviceId) {
                    throw new Error('返回的数据不属于当前设备');
                }
                
                // 获取或创建当前设备的批量读取结果容器
                let batchContainer = document.getElementById(`batchReadTableContainer-${currentDeviceId}`);
                if (!batchContainer) {
                    batchContainer = document.createElement('div');
                    batchContainer.id = `batchReadTableContainer-${currentDeviceId}`;
                    batchContainer.className = 'table-responsive';
                    batchContainer.innerHTML = `
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>寄存器地址</th>
                                    <th>数值</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="batchReadResult-${currentDeviceId}">
                            </tbody>
                        </table>
                    `;
                    document.getElementById('batchReadTableContainer').appendChild(batchContainer);
                }

                // 清除其他设备的批量读取结果
                document.querySelectorAll('[id^="batchReadTableContainer-"]').forEach(container => {
                    if (container.id !== `batchReadTableContainer-${currentDeviceId}`) {
                        container.remove();
                    }
                });
                
                // 显示当前设备的批量读取结果
                batchContainer.style.display = 'block';
                
                // 更新表格内容
                const tbody = document.getElementById(`batchReadResult-${currentDeviceId}`);
                tbody.innerHTML = '';
                
                // 显示设备信息
                const deviceInfo = document.createElement('tr');
                deviceInfo.innerHTML = `
                    <td colspan="3" class="table-light">
                        <strong>设备名称：${device.name}</strong><br>
                        <small class="text-muted">地址：${device.address}:${device.port}</small>
                    </td>
                `;
                tbody.appendChild(deviceInfo);
                
                // 显示数据
                data.values.forEach((value, index) => {
                    const actualAddress = parseInt(data.startAddress) + index;
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${actualAddress}</td>
                        <td class="value-cell" id="value-${currentDeviceId}-${actualAddress}">${value}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-success btn-sm" onclick="addToMonitor(${actualAddress})">
                                    添加监控
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="writeToRegister('${currentDeviceId}', ${actualAddress})">
                                    写入
                                </button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                // 确保按钮被重新启用
                const batchReadButton = document.querySelector('button[onclick="batchReadRegisters()"]');
                batchReadButton.disabled = false;

                if (error.message === 'Failed to fetch') {
                    alert('批量读取失败：网络连接错误，请检查设备连接状态');
                } else {
                    alert('批量读取失败：' + error.message);
                }
            }
        }

        // 修改写入寄存器功能
        function writeToRegister(deviceId, address) {
            if (deviceId !== currentDeviceId) {
                alert('设备ID不匹配');
                return;
            }
            
            const valueElement = document.getElementById(`value-${deviceId}-${address}`);
            const currentValue = valueElement.textContent;
            const writeValue = prompt('请输入要写入的值：', currentValue);
            
            if (writeValue === null) return; // 用户取消
            
            if (writeValue.trim() === '') {
                alert('请输入有效的值');
                return;
            }
            
            const numValue = parseInt(writeValue);
            if (isNaN(numValue)) {
                alert('请输入有效的数字');
                return;
            }
            
            document.getElementById('writeAddress').value = address;
            document.getElementById('writeValues').value = numValue;
            writeRegisters(false).then(() => {
                // 写入成功后更新显示的值
                valueElement.textContent = numValue;
            });
        }

        // 添加到监控的辅助函数
        function addToMonitor(address) {
            document.getElementById('readAddress').value = address;
            showAddRealTimeModal();
        }

        // 导出批量读取结果
        function exportBatchReadResult() {
            if (!currentDeviceId) return;
            
            const tbody = document.getElementById(`batchReadResult-${currentDeviceId}`);
            if (!tbody) return;
            
            let exportText = '';
            const rows = tbody.getElementsByTagName('tr');
            
            // 跳过第一行（设备信息）
            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                const address = cells[0].textContent;
                const value = cells[1].textContent;
                exportText += `${address},${value}\n`;
            }
            
            if (!exportText) {
                alert('没有可导出的数据');
                return;
            }
            
            // 创建并下载文件
            const blob = new Blob([exportText], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            const device = devices.get(currentDeviceId);
            const timestamp = new Date().toISOString().slice(0,19).replace(/[:-]/g, '');
            a.download = `${device.name}_${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 显示导入模态框
        function showImportModal() {
            if (!currentDeviceId) {
                alert('请先选择设备');
                return;
            }
            
            const device = devices.get(currentDeviceId);
            if (!device || !device.connected) {
                alert('请先连接设备');
                return;
            }
            
            document.getElementById('importFile').value = '';
            importModal.show();
        }

        // 导入数据并写入
        async function importAndWrite() {
            const fileInput = document.getElementById('importFile');
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('请选择要导入的文件');
                return;
            }
            
            const file = fileInput.files[0];
            if (file.size > 1024 * 1024) { // 限制文件大小为1MB
                alert('文件过大，请检查文件内容');
                return;
            }

            try {
                const text = await file.text();
                const lines = text.split('\n');
                const writeData = [];
                
                // 解析数据
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;
                    
                    const [address, value] = line.split(',').map(s => s.trim());
                    if (!address || !value) {
                        alert(`第${i + 1}行格式错误，请检查`);
                        return;
                    }
                    
                    const numValue = parseInt(value);
                    if (isNaN(numValue)) {
                        alert(`第${i + 1}行的值不是有效的数字`);
                        return;
                    }
                    
                    writeData.push({ address, value: numValue });
                }
                
                if (writeData.length === 0) {
                    alert('文件中没有有效的数据需要写入');
                    return;
                }
                
                // 确认写入
                if (!confirm(`确定要写入${writeData.length}个数据吗？`)) {
                    return;
                }
                
                // 逐个写入数据
                let successCount = 0;
                let failCount = 0;
                
                for (const data of writeData) {
                    try {
                        document.getElementById('writeAddress').value = data.address;
                        document.getElementById('writeValues').value = data.value;
                        await writeRegisters(true); // 使用静默模式写入
                        successCount++;
                    } catch (error) {
                        console.error(`写入地址 ${data.address} 失败:`, error);
                        failCount++;
                    }
                }
                
                importModal.hide();
                alert(`写入完成\n成功: ${successCount}\n失败: ${failCount}`);
            } catch (error) {
                alert('读取文件失败：' + error.message);
            }
        }

        // 添加历史记录开关函数
        async function toggleHistory(monitorId) {
            const monitor = realTimeMonitors.get(monitorId);
            if (!monitor) return;

            // 获取当前状态并切换
            const currentEnabled = monitor.historyEnabled;
            const newEnabled = !currentEnabled;

            try {
                const response = await fetch(`/api/data-items/${monitorId}/history-enabled`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled: newEnabled })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '更新失败');
                }

                // 更新监控项历史记录状态
                monitor.historyEnabled = newEnabled;

                // 更新切换按钮状态
                const toggleButton = document.getElementById(`history-toggle-${monitorId}`);
                if (toggleButton) {
                    // 更新按钮样式、图标和文本
                    toggleButton.className = `btn history-toggle-btn ${newEnabled ? 'btn-success' : 'btn-secondary'} btn-sm mb-2`;
                    toggleButton.innerHTML = `<i class="bi ${newEnabled ? 'bi-check-circle' : 'bi-x-circle'} me-1"></i>${newEnabled ? '已启用' : '已关闭'}`;
                }

                // 更新配置按钮状态
                const configButton = document.querySelector(`button[onclick="showRetentionConfigModal('${monitorId}')"]`);
                if (configButton) {
                    configButton.disabled = !newEnabled;
                }

                // 更新统计数据按钮状态
                const statsButton = document.querySelector(`button[onclick="showHourlyStatsModal('${monitorId}')"]`);
                if (statsButton) {
                    statsButton.disabled = !newEnabled;
                }

                console.log(`历史记录已${newEnabled ? '启用' : '禁用'}`);
            } catch (error) {
                console.error('更新历史记录设置失败:', error);
                alert(error.message || '更新失败');
            }
        }

        // 添加统计功能开关函数
        async function toggleStats(monitorId) {
            const monitor = realTimeMonitors.get(monitorId);
            if (!monitor) return;

            // 获取当前状态并切换
            const currentEnabled = monitor.statsEnabled;
            const newEnabled = !currentEnabled;

            try {
                const response = await fetch(`/api/data-items/${monitorId}/stats-enabled`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled: newEnabled })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '更新失败');
                }

                // 更新监控项统计功能状态
                monitor.statsEnabled = newEnabled;

                // 更新切换按钮状态
                const toggleButton = document.getElementById(`stats-toggle-${monitorId}`);
                if (toggleButton) {
                    // 更新按钮样式、图标和文本
                    toggleButton.className = `btn stats-toggle-btn ${newEnabled ? 'btn-success' : 'btn-secondary'} btn-sm mb-2`;
                    toggleButton.innerHTML = `<i class="bi ${newEnabled ? 'bi-check-circle' : 'bi-x-circle'} me-1"></i>${newEnabled ? '已启用' : '已关闭'}`;
                }

                // 更新统计数据按钮状态
                const statsButton = document.querySelector(`button[onclick="showHourlyStatsModal('${monitorId}')"]`);
                if (statsButton) {
                    statsButton.disabled = !newEnabled;
                }

                console.log(`统计功能已${newEnabled ? '启用' : '禁用'}`);
            } catch (error) {
                console.error('更新统计功能设置失败:', error);
                alert(error.message || '更新失败');
            }
        }

        // 添加导出相关的JavaScript函数
        function showExportModal(monitorId) {
            document.getElementById('exportMonitorId').value = monitorId;
            
            // 设置默认时间范围（最近24小时）
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            document.getElementById('exportStartTime').value = yesterday.toISOString().slice(0, 16);
            document.getElementById('exportEndTime').value = now.toISOString().slice(0, 16);
            
            exportModal.show();
        }

        async function exportHistory() {
            const monitorId = document.getElementById('exportMonitorId').value;
            const startTimeInput = document.getElementById('exportStartTime').value;
            const endTimeInput = document.getElementById('exportEndTime').value;
            
            if (!startTimeInput || !endTimeInput) {
                alert('请选择时间范围');
                return;
            }
            
            try {
                const monitor = realTimeMonitors.get(monitorId);
                if (!monitor) {
                    throw new Error('监控项不存在');
                }

                // 获取设备信息
                const device = devices.get(monitor.deviceId);
                if (!device) {
                    throw new Error('设备不存在');
                }
                
                // 格式化时间字符串
                const formatDateTime = (dateTimeStr) => {
                    const dt = new Date(dateTimeStr);
                    const year = dt.getFullYear();
                    const month = String(dt.getMonth() + 1).padStart(2, '0');
                    const day = String(dt.getDate()).padStart(2, '0');
                    const hours = String(dt.getHours()).padStart(2, '0');
                    const minutes = String(dt.getMinutes()).padStart(2, '0');
                    const seconds = String(dt.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };

                const startTime = formatDateTime(startTimeInput);
                const endTime = formatDateTime(endTimeInput);
                
                // 构建URL，包含查询参数
                const url = `/api/data-history/${monitorId}?startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}`;
                
                console.log('Requesting URL:', url);
                const response = await fetch(url);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '获取数据失败');
                }
                
                const data = await response.json();
                
                // 生成TXT格式的文本
                let txtContent = '';
                data.forEach(item => {
                    txtContent += `${device.name},${monitor.name},${monitor.address},${item.value},${item.timestamp}\n`;
                });
                
                // 创建Blob对象
                const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8;' });
                
                // 创建下载链接
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `历史记录_${device.name}_${monitor.name}_${startTime.replace(/[: ]/g, '-')}_${endTime.replace(/[: ]/g, '-')}.txt`;
                
                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                exportModal.hide();
            } catch (error) {
                console.error('导出历史记录失败:', error);
                alert('导出失败：' + error.message);
            }
        }

        // 修改设备点击事件处理函数
        function selectDevice(deviceId) {
            const device = devices.get(deviceId);
            if (!device) return;
            
            if (!device.connected) {
                alert('请先连接设备');
                return;
            }
            
            // 显示设备功能页
            document.getElementById('deviceFunctionSection').style.display = 'block';
            document.getElementById('deviceConfigSection').style.display = 'none';
            document.getElementById('deviceStatusSection').style.display = 'none';
            
            // 调用设备切换逻辑
            switchDevice(deviceId);
            
            // 更新选中状态
            const deviceItems = document.querySelectorAll('.device-item');
            deviceItems.forEach(item => item.classList.remove('active'));
            document.querySelector(`[data-device-id="${deviceId}"]`).classList.add('active');
        }
        
        // 显示主页（设备状态页）
        function showHome() {
            document.getElementById('deviceFunctionSection').style.display = 'none';
            document.getElementById('deviceConfigSection').style.display = 'none';
            document.getElementById('deviceStatusSection').style.display = 'block';
            // 移除设备列表中的选中状态
            const deviceItems = document.querySelectorAll('.device-item');
            deviceItems.forEach(item => item.classList.remove('active'));
            // 重置当前设备ID
            currentDeviceId = null;
            // 更新连接状态显示
            updateConnectionStatus();
        }
        
        // 显示设备功能页
        function showDeviceFunction() {
            document.getElementById('deviceFunctionSection').style.display = 'block';
            document.getElementById('deviceConfigSection').style.display = 'none';
            document.getElementById('deviceStatusSection').style.display = 'none';
        }

        // 添加显示设备配置页面的函数
        function showDeviceConfig(deviceId) {
            // 隐藏其他页面
            document.getElementById('deviceFunctionSection').style.display = 'none';
            document.getElementById('deviceStatusSection').style.display = 'none';
            
            // 显示配置页面
            const configSection = document.getElementById('deviceConfigSection');
            configSection.style.display = 'block';
            
            // 更新iframe的src
            const configFrame = document.getElementById('deviceConfigFrame');
            configFrame.src = `/device/${deviceId}/config`;
            
            // 更新设备选中状态
            const deviceItems = document.querySelectorAll('.device-item');
            deviceItems.forEach(item => item.classList.remove('active'));
            document.querySelector(`[data-device-id="${deviceId}"]`).classList.add('active');
            
            // 更新当前设备ID
            currentDeviceId = deviceId;
        }

        // 显示历史记录保留时间配置模态框
        function showRetentionConfigModal(monitorId) {
            const monitor = realTimeMonitors.get(monitorId);
            if (!monitor) return;
            
            // 设置当前编辑的监控项ID
            document.getElementById('retentionMonitorId').value = monitorId;
            
            // 设置当前的保留期限
            const retentionHours = monitor.historyRetentionHours || 720; // 默认30天
            
            // 设置单位和值
            let unit = 'hours';
            let value = retentionHours;
            
            if (retentionHours === -1) {
                // 永久保存
                document.getElementById('retentionUnit').value = 'permanent';
                document.getElementById('retentionValue').value = '';
                document.getElementById('retentionValue').disabled = true;
            } else if (retentionHours % 24 === 0 && retentionHours >= 24) {
                // 如果是整天数，则显示天数
                unit = 'days';
                value = retentionHours / 24;
                document.getElementById('retentionValue').disabled = false;
            } else {
                // 否则显示小时
                document.getElementById('retentionValue').disabled = false;
            }
            
            document.getElementById('retentionUnit').value = unit;
            document.getElementById('retentionValue').value = value;
            
            // 显示模态框
            retentionModal.show();
        }
        
        // 更新保留时间值字段（根据单位变化）
        function updateRetentionValueField() {
            const unit = document.getElementById('retentionUnit').value;
            const valueContainer = document.getElementById('retentionValueContainer');
            const valueInput = document.getElementById('retentionValue');
            
            if (unit === 'permanent') {
                // 永久保存不需要输入值
                valueInput.disabled = true;
                valueInput.value = '';
                valueContainer.style.display = 'none';
            } else {
                // 其他单位需要输入值
                valueInput.disabled = false;
                valueContainer.style.display = 'block';
                
                // 如果从永久保存切换回来，恢复默认值
                if (valueInput.value === '') {
                    valueInput.value = unit === 'days' ? '30' : '24';
                }
            }
        }
        
        // 保存历史记录保留时间配置
        async function saveRetentionConfig() {
            const monitorId = document.getElementById('retentionMonitorId').value;
            const unit = document.getElementById('retentionUnit').value;
            const valueInput = document.getElementById('retentionValue');
            
            // 获取并验证值
            let retentionHours;
            
            if (unit === 'permanent') {
                retentionHours = -1; // 永久保存
            } else {
                const value = parseInt(valueInput.value);
                
                if (isNaN(value) || value <= 0) {
                    alert('请输入有效的正整数');
                    return;
                }
                
                // 根据单位转换为小时
                retentionHours = (unit === 'days') ? value * 24 : value;
            }
            
            try {
                const monitor = realTimeMonitors.get(monitorId);
                if (!monitor) {
                    throw new Error('监控项不存在');
                }
                
                // 发送请求更新保留期限
                const response = await fetch(`/api/data-items/${monitorId}/history-retention`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ retentionHours: retentionHours })
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '更新失败');
                }
                
                // 更新本地数据
                monitor.historyRetentionHours = retentionHours;
                
                // 如果当前设备的监控配置中存在此监控项，也更新
                if (deviceMonitors.has(monitor.deviceId)) {
                    const deviceMonitorMap = deviceMonitors.get(monitor.deviceId);
                    if (deviceMonitorMap.has(monitorId)) {
                        deviceMonitorMap.get(monitorId).historyRetentionHours = retentionHours;
                    }
                }
                
                // 显示成功消息
                alert('历史记录保留时间设置成功');
                
                // 关闭模态框
                retentionModal.hide();
            } catch (error) {
                console.error('设置历史记录保留时间失败:', error);
                alert('设置失败：' + error.message);
            }
        }

        // 清理无效的历史数据
        async function cleanInvalidHistoryData() {
            if (!confirm('确定要清理无效的历史数据吗？这将永久删除不属于当前启用历史记录的监控项的历史数据。')) {
                return;
            }
            
            try {
                const response = await fetch('/api/data-history/clean-invalid', {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '清理失败');
                }
                
                const result = await response.json();
                alert(result.message);
            } catch (error) {
                console.error('清理无效历史数据失败:', error);
                alert('清理失败: ' + error.message);
            }
        }

        // 监控项排序功能
        async function moveMonitorUp(monitorId) {
            await moveMonitor(monitorId, 'up');
        }

        async function moveMonitorDown(monitorId) {
            await moveMonitor(monitorId, 'down');
        }

        async function moveMonitor(monitorId, direction) {
            if (!currentDeviceId) return;

            try {
                // 获取当前所有监控项的顺序
                const container = document.getElementById('realTimeContainer');
                const rows = Array.from(container.children);
                const currentIndex = rows.findIndex(row => row.id === `monitor-${monitorId}`);

                if (currentIndex === -1) return;

                let newIndex;
                if (direction === 'up' && currentIndex > 0) {
                    newIndex = currentIndex - 1;
                } else if (direction === 'down' && currentIndex < rows.length - 1) {
                    newIndex = currentIndex + 1;
                } else {
                    return; // 已经在边界位置
                }

                // 创建排序数据
                const sortItems = [];
                rows.forEach((row, index) => {
                    const itemId = row.id.replace('monitor-', '');
                    let sortOrder;

                    if (index === currentIndex) {
                        sortOrder = newIndex;
                    } else if (index === newIndex) {
                        sortOrder = currentIndex;
                    } else {
                        sortOrder = index;
                    }

                    sortItems.push({
                        id: itemId,
                        sortOrder: sortOrder
                    });
                });

                // 调用后端API更新排序
                const response = await fetch(`/api/data-items/device/${currentDeviceId}/sort-order`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ items: sortItems })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '更新排序失败');
                }

                // 更新DOM顺序
                const currentRow = rows[currentIndex];
                const targetRow = rows[newIndex];

                if (direction === 'up') {
                    container.insertBefore(currentRow, targetRow);
                } else {
                    container.insertBefore(currentRow, targetRow.nextSibling);
                }

                console.log(`监控项 ${monitorId} 已${direction === 'up' ? '上移' : '下移'}`);

            } catch (error) {
                console.error('移动监控项失败:', error);
                alert('移动监控项失败: ' + error.message);
            }
        }

        // 显示小时统计数据模态框
        function showHourlyStatsModal(monitorId) {
            const monitor = realTimeMonitors.get(monitorId);
            if (!monitor || !monitor.statsEnabled) {
                alert('该监控项未启用统计功能');
                return;
            }

            document.getElementById('statsMonitorId').value = monitorId;
            document.getElementById('statsTimeRange').value = '24';

            hourlyStatsModal.show();
            loadHourlyStats();
        }

        // 加载小时统计数据
        async function loadHourlyStats() {
            const monitorId = document.getElementById('statsMonitorId').value;
            const hours = document.getElementById('statsTimeRange').value;
            const contentDiv = document.getElementById('hourlyStatsContent');

            if (!monitorId) return;

            try {
                contentDiv.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                `;

                const response = await fetch(`/api/data-analysis/hourly-stats/${monitorId}?hours=${hours}`);
                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || '获取统计数据失败');
                }

                const stats = result.data;

                if (stats.length === 0) {
                    contentDiv.innerHTML = `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> 暂无统计数据
                        </div>
                    `;
                    return;
                }

                // 生成统计数据表格
                let tableHtml = `
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>时间</th>
                                    <th>最大值</th>
                                    <th>最小值</th>
                                    <th>平均值</th>
                                    <th>差值</th>
                                    <th>采样数</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                stats.forEach(stat => {
                    tableHtml += `
                        <tr>
                            <td>${stat.hourTimestamp}</td>
                            <td class="text-primary fw-bold">${stat.maxValue}</td>
                            <td class="text-success fw-bold">${stat.minValue}</td>
                            <td class="text-info fw-bold">${stat.avgValue}</td>
                            <td class="${stat.diffValue > 0 ? 'text-danger' : stat.diffValue < 0 ? 'text-success' : 'text-muted'} fw-bold">
                                ${stat.diffValue !== null ? (stat.diffValue > 0 ? '+' : '') + stat.diffValue : '-'}
                            </td>
                            <td class="text-muted">${stat.sampleCount}</td>
                        </tr>
                    `;
                });

                tableHtml += `
                            </tbody>
                        </table>
                    </div>
                `;

                contentDiv.innerHTML = tableHtml;

            } catch (error) {
                console.error('加载小时统计数据失败:', error);
                contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 加载失败：${error.message}
                    </div>
                `;
            }
        }

        // 手动计算统计数据
        async function manualCalculateStats() {
            try {
                const response = await fetch('/api/data-analysis/calculate-stats', {
                    method: 'POST'
                });

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || '计算失败');
                }

                alert('统计数据计算完成');
                loadHourlyStats(); // 重新加载数据

            } catch (error) {
                console.error('手动计算统计数据失败:', error);
                alert('计算失败：' + error.message);
            }
        }

        // 手动重新计算统计数据（根据选定时间范围）
        async function manualRecalculateStats() {
            const monitorId = document.getElementById('statsMonitorId').value;
            const hours = parseInt(document.getElementById('statsTimeRange').value);

            if (!monitorId) {
                alert('未选择监控项');
                return;
            }

            try {
                const response = await fetch(`/api/data-analysis/recalculate-stats/${monitorId}?hours=${hours}`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || '重新计算失败');
                }

                alert(result.message || '统计数据重新计算完成');
                loadHourlyStats(); // 重新加载数据

            } catch (error) {
                console.error('手动重新计算统计数据失败:', error);
                alert('重新计算失败：' + error.message);
            }
        }
    </script>
</body>
</html>