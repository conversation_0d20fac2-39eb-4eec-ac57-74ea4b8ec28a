<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - 组态系统</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/button-styles.css}">
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script type="text/javascript" th:src="@{/js/lib/jsplumb.min.js}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        
        /* 导航条样式 - 使用全局美化样式 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            padding: 0.5rem 1rem;
            margin-bottom: 0;
            height: 56px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.9);
        }

        .navbar .user-info {
            display: flex;
            align-items: center;
        }

        .navbar .username {
            color: white;
            margin-right: 15px;
            font-weight: 500;
        }

        .navbar .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: all 0.3s ease;
        }

        .navbar .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-1px);
        }
        
        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-right: 15px;
            font-weight: 500;
        }
        
        .navbar-nav .nav-link:hover {
            color: white;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 56px);
            overflow: hidden;
        }
        
        .sidebar {
            width: 250px;
            background-color: #ffffff;
            border-right: 1px solid #dee2e6;
            padding: 15px;
            overflow-y: auto;
            height: 100%;
        }
        
        /* 右侧配置栏样式 */
        .config-sidebar {
            width: 350px;
            background-color: #ffffff;
            border-left: 1px solid #dee2e6;
            padding: 15px;
            overflow-y: auto;
            height: 100%;
        }
        
        .config-title {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .config-panel {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .config-panel h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .config-panel .form-group {
            margin-bottom: 15px;
        }
        
        .config-panel label {
            display: block;
            margin-bottom: 5px;
            color: #495057;
            font-weight: 500;
        }
        
        .config-panel input,
        .config-panel select {
            width: 100%;
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: #fff;
        }
        
        .config-panel button {
            width: 100%;
            padding: 8px;
            margin-top: 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .config-panel button:hover {
            background-color: #0056b3;
        }
        
        .no-selection-message {
            color: #6c757d;
            text-align: center;
            padding: 20px;
            font-style: italic;
        }
        
        .canvas-container {
            flex: 1;
            background-color: #f8f9fa;
            position: relative;
            overflow: auto;
            height: 100%;
        }
        
        .canvas {
            position: absolute;
            width: 3000px;
            height: 2000px;
            background-color: #ffffff;
            background-image: 
                linear-gradient(#e9ecef 1px, transparent 1px),
                linear-gradient(90deg, #e9ecef 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .device-item {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: move;
            user-select: none;
        }
        
        .device-item:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .device-node {
            position: absolute;
            min-width: 150px;
            min-height: 100px;
            width: auto;
            height: auto;
            background-color: #ffffff;
            border: 2px solid #0d6efd;
            border-radius: 6px;
            padding: 10px;
            z-index: 10;
            cursor: move;
            user-select: none;
            overflow: hidden;
            /* 移除resize属性，使用自定义调整大小功能 */
        }
        
        /* 调整大小手柄样式 */
        .resize-handle {
            position: absolute;
            width: 10px;
            height: 10px;
            right: 0;
            bottom: 0;
            cursor: nwse-resize;
            background-color: #0d6efd;
            border-radius: 0 0 4px 0;
            z-index: 20;
            display: none; /* 默认隐藏 */
        }
        
        .device-node.has-image .resize-handle {
            background-color: rgba(13, 110, 253, 0.7);
        }
        
        .device-node.selected .resize-handle,
        .image-node.selected .resize-handle,
        .chart-node.selected .resize-handle {
            display: block; /* 选中时显示 */
            background-color: #dc3545;
        }
        
        .device-node.has-image {
            height: auto;
            min-height: 150px;
            padding: 5px;
            display: flex;
            flex-direction: column;
            /* 移除以下样式，不再自动隐藏背景和边框 */
            /* background-color: transparent; */
            /* border: none !important; */
            /* box-shadow: none !important; */
        }
        
        .device-node .device-image {
            text-align: center;
            margin-bottom: 5px;
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: visible;
            position: relative;
            min-height: 100px;
        }
        
        .device-node .device-image img {
            max-width: 100%;
            max-height: 100px;
            object-fit: contain;
            transform-origin: center center;
            position: relative;
        }
        
        .device-node.selected {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
        }
        
        /* 有图片的设备节点选中样式 */
        .device-node.has-image.selected {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.5) !important;
            background-color: rgba(220, 53, 69, 0.1);
        }
        
        .device-node .title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
            position: relative;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.7);
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .device-node .status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5px;
            position: relative;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.7);
        }
        
        .device-node .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .device-node .status-indicator.connected {
            background-color: #28a745;
        }
        
        .device-node .status-indicator.disconnected {
            background-color: #dc3545;
        }
        
        .endpoint {
            width: 12px;
            height: 12px;
            background-color: #0d6efd;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .controls {
            position: fixed;
            top: 66px; /* 导航栏高度(56px) + 10px的间距 */
            right: 360px; /* 右侧配置栏宽度(350px) + 10px的间距 */
            z-index: 100;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            max-width: 300px;
        }
        
        .btn-group {
            margin-bottom: 10px;
        }
        
        /* 文本组件样式 */
        .component-item {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: move;
            user-select: none;
        }
        
        .component-item:hover {
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .text-component {
            background-color: #e6f7ff;
            border-color: #91d5ff;
        }
        
        /* 图片组件样式 */
        .image-component {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }
        
        /* 图表组件样式 */
        .chart-component {
            background-color: #fff2e8;
            border-color: #ffbb96;
        }
        
        /* 文本节点样式 */
        .text-node {
            position: absolute;
            min-width: 100px;
            min-height: 40px;
            background-color: transparent;
            border: 1px dashed #ccc;
            padding: 8px;
            z-index: 10;
            cursor: move;
            user-select: none;
        }
        
        .text-node.selected {
            border: 1px dashed #dc3545;
        }
        
        .text-node.no-border {
            border: none;
        }
        
        .text-node.no-border.selected {
            border: 1px dashed #dc3545;
        }
        
        .text-node {
            font-size: 18px;
            font-weight: bold;
        }
        
        .text-node .editable {
            min-width: 50px;
            min-height: 20px;
            outline: none;
            width: 100%;
            height: 100%;
        }
        
        .text-node .resize-handle {
            position: absolute;
            width: 10px;
            height: 10px;
            right: 0;
            bottom: 0;
            cursor: nwse-resize;
            background-color: #dc3545;
            border-radius: 0 0 4px 0;
            z-index: 20;
            display: none;
        }
        
        .text-node.selected .resize-handle {
            display: block;
        }
        
        /* 连接线动画效果 */
        @keyframes flowAnimation {
            0% {
                stroke-dashoffset: 24;
            }
            100% {
                stroke-dashoffset: 0;
            }
        }
        
        /* 反向动画效果 */
        @keyframes reverseFlowAnimation {
            0% {
                stroke-dashoffset: 0;
            }
            100% {
                stroke-dashoffset: 24;
            }
        }
        
        /* 新的动态流动效果 */
        .jtk-connector.animated-connection path {
            stroke-dasharray: 10 5 !important;
            animation: flowAnimation linear infinite;
        }
        
        /* 反向动态流动效果 */
        .jtk-connector.animated-connection.reverse-flow path {
            animation-name: reverseFlowAnimation;
        }
        
        .jtk-connector.animation-slow path {
            animation-duration: 3s;
        }
        
        .jtk-connector.animation-medium path {
            animation-duration: 2s;
        }
        
        .jtk-connector.animation-fast path {
            animation-duration: 1s;
        }
        
        /* 预警状态项样式 */
        .alert-status-container {
            margin-top: 5px;
            font-size: 12px;
            max-height: 80px;
            overflow-y: auto;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            padding: 4px;
        }
        
        .alert-status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
            padding: 2px 4px;
            border-radius: 3px;
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .alert-name {
            font-weight: 500;
            margin-right: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
        }
        
        .alert-status-badge {
            font-size: 10px;
            padding: 2px 5px;
        }
        
        /* 徽章样式 */
        .badge {
            display: inline-block;
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-success {
            color: #fff;
            background-color: #198754;
        }
        
        .badge-warning {
            color: #000;
            background-color: #ffc107;
        }
        
        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }
        
        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }
        
        /* 表格样式调整 */
        .config-sidebar .table {
            width: 100%;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .config-sidebar .table th,
        .config-sidebar .table td {
            padding: 0.5rem;
            vertical-align: middle;
        }
        
        /* 表单元素样式调整 */
        .config-sidebar .form-control,
        .config-sidebar .form-select {
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
        }
        
        .config-sidebar .form-label {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }
        
        /* 按钮样式调整 */
        .config-sidebar .btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
        }
        
        .config-sidebar .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        
        /* 状态项容器样式调整 */
        .config-sidebar .alert-items-container {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #ffffff;
        }
        
        /* 图片节点样式 */
        .image-node {
            position: absolute;
            min-width: 150px;
            min-height: 100px;
            width: 200px;
            height: 150px;
            background-color: #ffffff;
            border: 2px solid #0d6efd;
            border-radius: 6px;
            padding: 10px;
            z-index: 10;
            cursor: move;
            user-select: none;
            overflow: hidden;
        }
        
        .image-node.selected {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .image-node .image-container {
            width: 100%;
            height: calc(100% - 30px);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .image-node .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .image-node .image-title {
            height: 30px;
            line-height: 30px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }
        
        /* 图表节点样式 */
        .chart-node {
            position: absolute;
            min-width: 200px;
            min-height: 150px;
            background-color: #ffffff;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            cursor: move;
            user-select: none;
            display: flex;
            flex-direction: column;
        }
        
        .chart-node.selected {
            border-color: #fa8c16;
            box-shadow: 0 0 0 2px rgba(250, 140, 22, 0.2);
        }
        
        .chart-node .chart-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .chart-node .chart-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 120px;
        }
    </style>
</head>
<body>
    <!-- 引入导航条片段，添加自定义按钮 -->
    <div th:with="customButtons='
        <a href=&quot;/topology/publish-management&quot; class=&quot;btn btn-outline-light btn-sm ms-3&quot;>
            <i class=&quot;bi bi-share&quot;></i> 组态发布管理
        </a>
    '">
        <nav th:replace="fragments/navbar :: navbar('胜大科技智联管理系统 - 组态系统', 'topology', true, true, true, ${customButtons})"></nav>
    </div>
    
    <div class="main-container">
        <!-- 左侧设备列表 -->
        <div class="sidebar">
            <!-- 添加布局名称输入框 -->
            <div class="mb-3">
                <label for="layoutName" class="form-label">布局名称 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="layoutName" placeholder="请输入布局名称" required>
                <div class="invalid-feedback">请输入布局名称</div>
            </div>
            <h5>组件列表</h5>
            <!-- 添加组件类型选择 -->
            <ul class="nav nav-tabs" id="componentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="device-tab" data-bs-toggle="tab" data-bs-target="#device-panel" type="button" role="tab" aria-controls="device-panel" aria-selected="true">设备</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-panel" type="button" role="tab" aria-controls="text-panel" aria-selected="false">文本</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="image-tab" data-bs-toggle="tab" data-bs-target="#image-panel" type="button" role="tab" aria-controls="image-panel" aria-selected="false">图片</button>
                </li>
            </ul>
            
            <!-- 组件面板 -->
            <div class="tab-content" id="componentTabContent">
                <!-- 设备组件面板 -->
                <div class="tab-pane fade show active" id="device-panel" role="tabpanel" aria-labelledby="device-tab">
                    <div id="deviceList">
                        <!-- 设备列表将动态加载 -->
                    </div>
                </div>
                
                <!-- 文本组件面板 -->
                <div class="tab-pane fade" id="text-panel" role="tabpanel" aria-labelledby="text-tab">
                    <div class="mb-3">
                        <div class="component-item text-component" draggable="true" data-component-type="text">
                            <div><strong>文本组件</strong></div>
                            <div><small>拖拽添加文本</small></div>
                        </div>
                    </div>
                </div>
                
                <!-- 图片组件面板 -->
                <div class="tab-pane fade" id="image-panel" role="tabpanel" aria-labelledby="image-tab">
                    <div class="mb-3">
                        <div class="component-item image-component" draggable="true" data-component-type="image">
                            <div><strong>图片组件</strong></div>
                            <div><small>拖拽添加图片</small></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主画布区域 -->
        <div class="canvas-container">
            <div class="canvas" id="canvas">
                <!-- 设备节点将动态添加到这里 -->
            </div>
            
            <!-- 控制按钮 -->
            <div class="controls">
                <div class="btn-group">
                    <button class="btn btn-primary btn-sm" id="saveBtn">
                        <i class="bi bi-save"></i> 保存布局
                    </button>
                    <button class="btn btn-secondary btn-sm" id="resetBtn">
                        <i class="bi bi-arrow-counterclockwise"></i> 重置
                    </button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-danger btn-sm" id="deleteBtn" disabled>
                        <i class="bi bi-trash"></i> 删除选中
                    </button>
                </div>
                <!-- 添加布局列表下拉菜单 -->
                <div class="mt-2">
                    <div class="d-flex">
                        <select class="form-select form-select-sm me-2" id="layoutSelector">
                        <option value="">选择已保存的布局</option>
                        <!-- 布局列表将动态加载 -->
                    </select>
                        <button class="btn btn-danger btn-sm me-2" id="deleteLayoutBtn" disabled>
                            <i class="bi bi-trash"></i>
                        </button>
                        <button class="btn btn-info btn-sm" id="previewLayoutBtn" disabled>
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧配置栏 -->
        <div class="config-sidebar" id="configSidebar">
            <h5 class="config-title">属性配置</h5>
            
            <!-- 设备节点配置 -->
            <div class="config-panel" id="deviceConfig" style="display: none;">
                <div class="form-group mb-3">
                    <label class="form-label">设备名称</label>
                    <div class="d-flex align-items-center">
                        <input type="text" class="form-control" id="deviceName">
                        <div class="form-check ms-2 mb-0" style="white-space: nowrap;">
                            <input class="form-check-input" type="checkbox" id="showDeviceTitle" checked>
                            <label class="form-check-label" for="showDeviceTitle">
                                显示
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">设备图片</label>
                    <div class="d-flex flex-column">
                        <div class="mb-2" id="deviceImagePreview" style="display: none;">
                            <img src="" alt="设备图片" style="max-width: 100%; max-height: 100px; object-fit: contain;">
                            <button class="btn btn-sm btn-danger mt-1" id="removeDeviceImage">移除图片</button>
                        </div>
                        <div class="input-group">
                            <input type="text" class="form-control" id="deviceImageUrl" placeholder="图片URL">
                            <button class="btn btn-outline-secondary" type="button" id="loadImageUrl">加载</button>
                        </div>
                        <small class="text-muted mt-1">输入图片URL或选择本地图片</small>
                        <input type="file" class="form-control mt-2" id="deviceImageFile" accept="image/*">
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">图片缩放 (<span id="scaleValue">1.0</span>倍)</label>
                    <input type="range" class="form-range" id="imageScale" min="0.1" max="3" step="0.1" value="1.0">
                    <div class="d-flex justify-content-between">
                        <small>0.1x</small>
                        <small>3.0x</small>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">图片偏移</label>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">X偏移 (px)</label>
                            <input type="number" class="form-control" id="imageOffsetX" value="0">
                        </div>
                        <div class="col-6">
                            <label class="form-label">Y偏移 (px)</label>
                            <input type="number" class="form-control" id="imageOffsetY" value="0">
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">颜色设置</label>
                    <div class="row mb-2">
                        <div class="col-6">
                    <label class="form-label">背景颜色</label>
                    <input type="color" class="form-control" id="deviceBgColor" value="#ffffff">
                </div>
                        <div class="col-6">
                    <label class="form-label">背景透明度 (<span id="bgOpacityValue">100</span>%)</label>
                    <input type="range" class="form-range" id="deviceBgOpacity" min="0" max="100" step="1" value="100">
                    <div class="d-flex justify-content-between">
                        <small>透明</small>
                        <small>不透明</small>
                    </div>
                </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                    <label class="form-label">边框颜色</label>
                    <input type="color" class="form-control" id="deviceBorderColor" value="#0d6efd">
                </div>
                        <div class="col-6">
                    <label class="form-label">边框透明度 (<span id="borderOpacityValue">100</span>%)</label>
                    <input type="range" class="form-range" id="deviceBorderOpacity" min="0" max="100" step="1" value="100">
                    <div class="d-flex justify-content-between">
                        <small>透明</small>
                        <small>不透明</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">组件尺寸</label>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">宽度 (px)</label>
                            <input type="number" class="form-control" id="deviceWidth" min="50" max="500" value="150">
                        </div>
                        <div class="col-6">
                            <label class="form-label">高度 (px)</label>
                            <input type="number" class="form-control" id="deviceHeight" min="50" max="500" value="100">
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary btn-sm" id="applyDeviceConfig">应用</button>
                
                <!-- 添加状态项配置部分 -->
                <div class="form-group mt-4">
                    <label class="form-label">状态项配置</label>
                    <div class="d-flex mb-2">
                        <select class="form-select form-select-sm me-2" id="alertSelector" style="flex: 3;">
                            <option value="">选择预警配置</option>
                            <!-- 预警配置列表将动态加载 -->
                        </select>
                        <button class="btn btn-sm btn-outline-primary" id="addAlertBtn" style="width: 60px;">添加</button>
                    </div>
                    <div class="alert-items-container mt-2 p-2">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 50%;">名称</th>
                                    <th style="width: 30%;">状态</th>
                                    <th style="width: 20%;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="alertItemsTable">
                                <!-- 状态项列表将动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 文本节点配置 -->
            <div class="config-panel" id="textConfig" style="display: none;">
                <div class="form-group mb-3">
                    <label class="form-label">文本内容</label>
                    <input type="text" class="form-control" id="textContent">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">字体大小</label>
                    <input type="number" class="form-control" id="fontSize" min="10" max="36" value="14">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">字体颜色</label>
                    <input type="color" class="form-control" id="fontColor" value="#000000">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">字体粗细</label>
                    <select class="form-select" id="fontWeight">
                        <option value="normal">正常</option>
                        <option value="bold">粗体</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">文本对齐</label>
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-secondary" data-align="left">
                            <i class="bi bi-text-left"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" data-align="center">
                            <i class="bi bi-text-center"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" data-align="right">
                            <i class="bi bi-text-right"></i>
                        </button>
                    </div>
                    <input type="hidden" id="textAlign" value="left">
                </div>
                <div class="form-group mb-3">
                    <div class="form-check" style="width: fit-content;">
                        <input class="form-check-input" type="checkbox" id="showBorder" checked>
                        <label class="form-check-label" for="showBorder" style="white-space: nowrap;">
                            显示边框
                        </label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">节点尺寸</label>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">宽度</label>
                            <input type="number" class="form-control" id="textWidth" min="100">
                        </div>
                        <div class="col-6">
                            <label class="form-label">高度</label>
                            <input type="number" class="form-control" id="textHeight" min="40">
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary btn-sm" id="applyTextConfig">应用</button>
            </div>
            
            <!-- 连接线配置 -->
            <div class="config-panel" id="connectionConfig" style="display: none;">
                <div class="form-group mb-3">
                    <label class="form-label">线条颜色</label>
                    <input type="color" class="form-control" id="lineColor" value="#0d6efd">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">线条粗细</label>
                    <input type="number" class="form-control" id="lineWidth" min="1" max="10" value="2">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">线条样式</label>
                    <select class="form-select" id="lineStyle">
                        <option value="solid">实线</option>
                        <option value="dashed">虚线</option>
                        <option value="dotted">点线</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">动态效果</label>
                    <div class="d-flex flex-column">
                        <div class="row mb-2">
                            <div class="col-6 text-center">
                            <input class="form-check-input" type="checkbox" id="enableAnimation">
                            </div>
                            <div class="col-6 text-center">
                                <input class="form-check-input" type="checkbox" id="reverseFlow">
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6 text-center">
                            <label class="form-check-label" for="enableAnimation">启用动态效果</label>
                        </div>
                            <div class="col-6 text-center">
                                <label class="form-check-label" for="reverseFlow">反向</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                            <select class="form-select form-select-sm" id="animationSpeed">
                                <option value="slow">慢速</option>
                                <option value="medium" selected>中速</option>
                                <option value="fast">快速</option>
                            </select>
                        </div>
                    </div>
                    </div>
                </div>
                <button class="btn btn-primary btn-sm" id="applyConnectionConfig">应用</button>
            </div>
            
            <!-- 图片节点配置 -->
            <div class="config-panel" id="imageConfig" style="display: none;">
                <div class="form-group mb-3">
                    <label class="form-label">图片标题</label>
                    <div class="d-flex align-items-center">
                        <input type="text" class="form-control" id="imageTitle">
                        <div class="form-check ms-2 mb-0" style="white-space: nowrap;">
                            <input class="form-check-input" type="checkbox" id="showImageTitle" checked>
                            <label class="form-check-label" for="showImageTitle">
                                显示
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">图片</label>
                    <div class="d-flex flex-column">
                        <div class="mb-2" id="imageNodePreview" style="display: none;">
                            <img src="" alt="图片预览" style="max-width: 100%; max-height: 100px; object-fit: contain;">
                            <button class="btn btn-sm btn-danger mt-1" id="removeImageNodeImage">移除图片</button>
                        </div>
                        <div class="input-group">
                            <input type="text" class="form-control" id="imageNodeUrl" placeholder="图片URL">
                            <button class="btn btn-outline-secondary" type="button" id="loadImageNodeUrl">加载</button>
                        </div>
                        <small class="text-muted mt-1">输入图片URL或选择本地图片</small>
                        <input type="file" class="form-control mt-2" id="imageNodeFile" accept="image/*">
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">图片缩放 (<span id="imageNodeScaleValue">1.0</span>倍)</label>
                    <input type="range" class="form-range" id="imageNodeScale" min="0.1" max="3" step="0.1" value="1.0">
                    <div class="d-flex justify-content-between">
                        <small>0.1x</small>
                        <small>3.0x</small>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">背景颜色</label>
                    <input type="color" class="form-control" id="imageBgColor" value="#ffffff">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">背景透明度 (<span id="imageBgOpacityValue">100</span>%)</label>
                    <input type="range" class="form-range" id="imageBgOpacity" min="0" max="100" step="1" value="100">
                    <div class="d-flex justify-content-between">
                        <small>透明</small>
                        <small>不透明</small>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">边框颜色</label>
                    <input type="color" class="form-control" id="imageBorderColor" value="#d9d9d9">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">边框透明度 (<span id="imageBorderOpacityValue">100</span>%)</label>
                    <input type="range" class="form-range" id="imageBorderOpacity" min="0" max="100" step="1" value="100">
                    <div class="d-flex justify-content-between">
                        <small>透明</small>
                        <small>不透明</small>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">组件尺寸</label>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">宽度 (px)</label>
                            <input type="number" class="form-control" id="imageWidth" min="50" max="800" value="200">
                        </div>
                        <div class="col-6">
                            <label class="form-label">高度 (px)</label>
                            <input type="number" class="form-control" id="imageHeight" min="50" max="800" value="150">
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary btn-sm" id="applyImageConfig">应用</button>
            </div>
            
            <!-- 无选中项时的提示 -->
            <div class="config-panel" id="noSelectionConfig">
                <h5 class="config-title">画布配置</h5>
                <div class="form-group mb-3">
                    <label class="form-label">画布尺寸</label>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">宽度 (px)</label>
                            <input type="number" class="form-control" id="canvasWidth" min="1000" max="5000" value="3000">
                        </div>
                        <div class="col-6">
                            <label class="form-label">高度 (px)</label>
                            <input type="number" class="form-control" id="canvasHeight" min="1000" max="5000" value="2000">
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">网格设置</label>
                    <div class="d-flex align-items-center mb-2">
                        <div class="form-check" style="width: fit-content; margin: 0;">
                            <input class="form-check-input" type="checkbox" id="showGrid" checked>
                            <label class="form-check-label" for="showGrid" style="white-space: nowrap;">
                                显示网格线
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">网格大小 (px)</label>
                            <input type="number" class="form-control" id="gridSize" min="10" max="100" value="20">
                        </div>
                        <div class="col-6">
                            <label class="form-label">网格颜色</label>
                            <input type="color" class="form-control" id="gridColor" value="#e9ecef">
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">背景颜色</label>
                    <input type="color" class="form-control" id="canvasBackgroundColor" value="#ffffff">
                </div>
                <button class="btn btn-primary btn-sm" id="applyCanvasConfig">应用</button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化jsPlumb实例
            const jsPlumbInstance = jsPlumb.getInstance({
                Endpoint: ["Dot", { radius: 4 }],
                Connector: ["Flowchart", { cornerRadius: 5 }],
                PaintStyle: { 
                    stroke: "#0d6efd", 
                    strokeWidth: 2 
                },
                HoverPaintStyle: { 
                    stroke: "#1e8151", 
                    strokeWidth: 3 
                },
                Container: "canvas"
            });
            
            // 当前选中的节点
            let selectedNode = null;
            let selectedConnection = null;
            
            // 初始化jsPlumb事件监听器
            function initializeJsPlumbEvents() {
            // 添加连接创建事件监听器
            jsPlumbInstance.bind("connection", function(info) {
                // 获取连接对象
                const connection = info.connection;
                
                // 记录连接创建
                console.log(`创建连接: 源节点=${connection.sourceId}, 目标节点=${connection.targetId}`);
                
                // 默认不设置反向流动
                connection.isReverseFlow = false;
                
                // 如果连接有动画效果，应用动画
                if (connection.hasAnimation) {
                    const connector = connection.connector.canvas;
                    if (connector) {
                        console.log(`应用动画效果，反向流动=${connection.isReverseFlow}`);
                    }
                }
            });
            
            // 添加连接线点击事件
            jsPlumbInstance.bind("click", function(connection, originalEvent) {
                originalEvent.stopPropagation();
                
                // 取消之前选中的节点
                if (selectedNode) {
                    selectedNode.classList.remove('selected');
                    selectedNode = null;
                }
                
                // 保存选中的连接线
                selectedConnection = connection;
                
                // 启用删除按钮
                document.getElementById('deleteBtn').disabled = false;
                
                // 更新配置面板
                updateConnectionConfigPanel(connection);
            });
            }
            
            // 初始化事件监听器
            initializeJsPlumbEvents();
            
            // 加载设备列表
            loadDevices();
            
            // 加载布局列表
            loadLayoutList();
            
            // 加载保存的组态布局
            loadTopology();
            
            // 保存按钮点击事件
            document.getElementById('saveBtn').addEventListener('click', saveTopology);
            
            // 重置按钮点击事件
            document.getElementById('resetBtn').addEventListener('click', resetCanvas);
            
            // 删除按钮点击事件
            document.getElementById('deleteBtn').addEventListener('click', deleteSelectedNode);
            
            // 初始化文本组件拖拽
            initTextComponents();
            
            // 初始化图片和图表组件拖拽
            initImageAndChartComponents();
            
            // 动态效果复选框的事件监听器
            document.getElementById('enableAnimation').addEventListener('change', function(event) {
                // 当启用动态效果时，禁用线条样式选择
                document.getElementById('lineStyle').disabled = event.target.checked;
                // 当启用动态效果时，启用反向流动选项；否则禁用
                document.getElementById('reverseFlow').disabled = !event.target.checked;
            });
            
            // 在页面完全加载后，延迟加载WebSocket相关脚本
            setTimeout(function() {
                // 加载WebSocket相关脚本
                loadScripts();
            }, 2000);
            
            // 更新连接线配置面板
            function updateConnectionConfigPanel(connection) {
                // 隐藏所有配置面板
                document.getElementById('deviceConfig').style.display = 'none';
                document.getElementById('textConfig').style.display = 'none';
                document.getElementById('connectionConfig').style.display = 'none';
                document.getElementById('noSelectionConfig').style.display = 'none';
                
                // 显示连接线配置面板
                document.getElementById('connectionConfig').style.display = 'block';
                
                // 获取当前样式
                const paintStyle = connection.getPaintStyle();
                
                // 设置线条颜色
                const strokeColor = paintStyle.stroke || '#0d6efd';
                document.getElementById('lineColor').value = strokeColor;
                
                // 设置线条粗细
                const strokeWidth = paintStyle.strokeWidth || 2;
                document.getElementById('lineWidth').value = strokeWidth;
                
                // 设置线条样式
                let lineStyle = 'solid';
                if (paintStyle.strokeDasharray) {
                    if (paintStyle.strokeDasharray === '2 2') {
                        lineStyle = 'dotted';
                    } else if (paintStyle.strokeDasharray === '5 5') {
                        lineStyle = 'dashed';
                    }
                }
                document.getElementById('lineStyle').value = lineStyle;
                
                // 设置动画效果
                const hasAnimation = connection.hasAnimation === true;
                document.getElementById('enableAnimation').checked = hasAnimation;
                
                // 设置反向流动
                const isReverseFlow = connection.isReverseFlow === true;
                document.getElementById('reverseFlow').checked = isReverseFlow;
                
                // 当启用动态效果时，禁用线条样式选择
                document.getElementById('lineStyle').disabled = hasAnimation;
                
                // 当启用动态效果时，启用反向流动选项；否则禁用
                document.getElementById('reverseFlow').disabled = !hasAnimation;
                
                // 动画速度
                let animationSpeed = connection.animationSpeed || 'medium';
                document.getElementById('animationSpeed').value = animationSpeed;
            }
            
            // 加载设备列表
            async function loadDevices() {
                try {
                    const response = await fetch('/api/devices');
                    if (!response.ok) {
                        throw new Error('获取设备列表失败');
                    }
                    const devices = await response.json();
                    
                    const deviceListContainer = document.getElementById('deviceList');
                    deviceListContainer.innerHTML = '';
                    
                    devices.forEach(device => {
                        const deviceElement = document.createElement('div');
                        deviceElement.className = 'device-item';
                        deviceElement.setAttribute('data-device-id', device.id);
                        deviceElement.setAttribute('data-device-name', device.name);
                        deviceElement.setAttribute('data-device-address', device.address);
                        deviceElement.setAttribute('data-device-port', device.port);
                        deviceElement.setAttribute('draggable', 'true');
                        deviceElement.innerHTML = `
                            <div><strong>${device.name}</strong></div>
                            <div><small>${device.address}:${device.port}</small></div>
                        `;
                        
                        // 添加拖拽事件
                        deviceElement.addEventListener('dragstart', handleDragStart);
                        
                        deviceListContainer.appendChild(deviceElement);
                    });
                } catch (error) {
                    console.error('加载设备列表失败:', error);
                    alert('加载设备列表失败: ' + error.message);
                }
            }
            
            // 处理拖拽开始事件
            function handleDragStart(event) {
                const deviceId = event.target.getAttribute('data-device-id');
                const deviceName = event.target.getAttribute('data-device-name');
                const deviceAddress = event.target.getAttribute('data-device-address');
                const devicePort = event.target.getAttribute('data-device-port');
                
                event.dataTransfer.setData('text/plain', JSON.stringify({
                    id: deviceId,
                    name: deviceName,
                    address: deviceAddress,
                    port: devicePort
                }));
            }
            
            // 设置画布拖放事件
            const canvas = document.getElementById('canvas');
            
            canvas.addEventListener('dragover', function(event) {
                event.preventDefault();
            });
            
            canvas.addEventListener('drop', function(event) {
                event.preventDefault();
                
                const data = JSON.parse(event.dataTransfer.getData('text/plain'));
                
                // 计算放置位置（相对于画布）
                const canvasRect = canvas.getBoundingClientRect();
                const x = event.clientX - canvasRect.left;
                const y = event.clientY - canvasRect.top;
                
                // 根据组件类型创建不同的节点
                if (data.type === 'text' || data.type === 'label') {
                    createTextNode(data, x, y);
                } else if (data.type === 'image') {
                    createImageNode(data, x, y);
                } else {
                    createDeviceNode(data, x, y);
                }
            });
            
            // 创建设备节点
            function createDeviceNode(device, x, y) {
                // 检查设备ID是否有效
                if (!device || !device.id || device.id === 'undefined') {
                    console.error('无法创建设备节点: 无效的设备ID', device);
                    return null;
                }
                
                const nodeId = `node-${device.id}`;
                
                // 检查节点是否已存在
                if (document.getElementById(nodeId)) {
                    console.log(`设备节点 ${nodeId} 已存在，不重复创建`);
                    return document.getElementById(nodeId);
                }
                
                console.log(`创建设备节点: ${device.name} (${device.id}), 位置: ${x}, ${y}`);
                
                // 创建节点元素
                const nodeElement = document.createElement('div');
                nodeElement.id = nodeId;
                nodeElement.className = 'device-node';
                nodeElement.setAttribute('data-device-id', device.id);
                nodeElement.setAttribute('data-device-name', device.name);
                nodeElement.setAttribute('data-device-address', device.address);
                nodeElement.setAttribute('data-device-port', device.port);
                nodeElement.style.left = `${x}px`;
                nodeElement.style.top = `${y}px`;
                
                // 设置自定义宽高（如果有）
                if (device.width) {
                    nodeElement.style.width = `${device.width}px`;
                }
                if (device.height) {
                    nodeElement.style.height = `${device.height}px`;
                }
                
                // 设置背景和边框颜色（如果有）
                if (device.backgroundColor) {
                    nodeElement.style.backgroundColor = device.backgroundColor;
                }
                if (device.borderColor) {
                    nodeElement.style.borderColor = device.borderColor;
                }
                
                // 设置节点内容
                if (device.imageUrl) {
                    // 如果有图片，显示图片
                    nodeElement.innerHTML = `
                        <div class="device-image">
                            <img src="${device.imageUrl}" alt="${device.name}" style="transform: scale(${device.imageScale || 1.0}); transform-origin: center center; position: relative; left: ${device.imageOffsetX || 0}px; top: ${device.imageOffsetY || 0}px;" />
                        </div>
                        <div class="title" ${device.showTitle === false ? 'style="display: none;"' : ''}>${device.name}</div>
                        <div class="status">
                            <span class="status-indicator disconnected"></span>
                            <span class="status-text">正在获取状态...</span>
                        </div>
                        <div class="alert-status-container">
                            <!-- 预警状态将动态添加到这里 -->
                        </div>
                        <div class="resize-handle"></div>
                    `;
                    nodeElement.classList.add('has-image');
                } else {
                    // 没有图片，使用默认样式
                    nodeElement.innerHTML = `
                        <div class="title" ${device.showTitle === false ? 'style="display: none;"' : ''}>${device.name}</div>
                        <div class="status">
                            <span class="status-indicator disconnected"></span>
                            <span class="status-text">正在获取状态...</span>
                        </div>
                        <div class="alert-status-container">
                            <!-- 预警状态将动态添加到这里 -->
                        </div>
                        <div class="resize-handle"></div>
                    `;
                }
                
                // 添加点击事件（选中节点）
                nodeElement.addEventListener('click', function(event) {
                    event.stopPropagation();
                    selectNode(nodeElement);
                });
                
                // 添加到画布
                canvas.appendChild(nodeElement);
                
                // 添加调整大小的功能
                const resizeHandle = nodeElement.querySelector('.resize-handle');
                if (resizeHandle) {
                    resizeHandle.addEventListener('mousedown', function(e) {
                        e.stopPropagation();
                        e.preventDefault();
                        
                        // 记录初始位置和大小
                        const startX = e.clientX;
                        const startY = e.clientY;
                        const startWidth = nodeElement.offsetWidth;
                        const startHeight = nodeElement.offsetHeight;
                        
                        // 创建调整大小的函数
                        function resize(e) {
                            // 计算新的宽度和高度
                            const newWidth = Math.max(150, startWidth + e.clientX - startX);
                            const newHeight = Math.max(100, startHeight + e.clientY - startY);
                            
                            // 应用新的宽度和高度
                            nodeElement.style.width = `${newWidth}px`;
                            nodeElement.style.height = `${newHeight}px`;
                            
                            // 重绘jsPlumb连接
                            jsPlumbInstance.repaintEverything();
                        }
                        
                        // 创建停止调整大小的函数
                        function stopResize() {
                            document.removeEventListener('mousemove', resize);
                            document.removeEventListener('mouseup', stopResize);
                        }
                        
                        // 添加事件监听器
                        document.addEventListener('mousemove', resize);
                        document.addEventListener('mouseup', stopResize);
                    });
                }
                
                // 使节点可拖动
                jsPlumbInstance.draggable(nodeId, {
                    grid: [10, 10]
                });
                
                // 添加连接点
                jsPlumbInstance.addEndpoint(nodeId, {
                    anchor: ["Right", [1, 0.5, 1, 0]], // 使用精确的位置坐标
                    isSource: true,
                    isTarget: true,
                    maxConnections: -1,
                    cssClass: "endpoint right-endpoint",
                    endpoint: ["Dot", { radius: 4 }],
                    paintStyle: { fill: "#0d6efd" },
                    connectorStyle: { stroke: "#0d6efd", strokeWidth: 2 },
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    uuid: nodeId + "-right"
                });
                
                jsPlumbInstance.addEndpoint(nodeId, {
                    anchor: ["Left", [0, 0.5, -1, 0]], // 使用精确的位置坐标
                    isSource: true,
                    isTarget: true,
                    maxConnections: -1,
                    cssClass: "endpoint left-endpoint",
                    endpoint: ["Dot", { radius: 4 }],
                    paintStyle: { fill: "#0d6efd" },
                    connectorStyle: { stroke: "#0d6efd", strokeWidth: 2 },
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    uuid: nodeId + "-left"
                });
                
                jsPlumbInstance.addEndpoint(nodeId, {
                    anchor: ["Top", [0.5, 0, 0, -1]], // 使用精确的位置坐标
                    isSource: true,
                    isTarget: true,
                    maxConnections: -1,
                    cssClass: "endpoint top-endpoint",
                    endpoint: ["Dot", { radius: 4 }],
                    paintStyle: { fill: "#0d6efd" },
                    connectorStyle: { stroke: "#0d6efd", strokeWidth: 2 },
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    uuid: nodeId + "-top"
                });
                
                jsPlumbInstance.addEndpoint(nodeId, {
                    anchor: ["Bottom", [0.5, 1, 0, 1]], // 使用精确的位置坐标
                    isSource: true,
                    isTarget: true,
                    maxConnections: -1,
                    cssClass: "endpoint bottom-endpoint",
                    endpoint: ["Dot", { radius: 4 }],
                    paintStyle: { fill: "#0d6efd" },
                    connectorStyle: { stroke: "#0d6efd", strokeWidth: 2 },
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    uuid: nodeId + "-bottom"
                });
                
                // 添加预警项
                if (device.alertItems && device.alertItems.length > 0) {
                    const alertStatusContainer = nodeElement.querySelector('.alert-status-container');
                    
                    device.alertItems.forEach(alert => {
                        const alertItem = document.createElement('div');
                        alertItem.className = 'alert-status-item';
                        alertItem.dataset.alertId = alert.id;
                        alertItem.dataset.alertName = alert.name;
                        
                        // 创建名称元素
                        const nameSpan = document.createElement('span');
                        nameSpan.className = 'alert-name';
                        nameSpan.textContent = alert.name + ':';
                        alertItem.appendChild(nameSpan);
                        
                        // 创建状态徽章元素
                        const badgeSpan = document.createElement('span');
                        badgeSpan.className = 'badge badge-secondary';
                        badgeSpan.textContent = '未知';
                        alertItem.appendChild(badgeSpan);
                        
                        alertStatusContainer.appendChild(alertItem);
                        
                        console.log(`加载布局时添加预警项，HTML: ${alertItem.outerHTML}`);
                        
                        // 开始更新预警状态
                        updateAlertStatus(alert.id, device.id);
                    });
                    
                    // 自动调整节点高度
                    setTimeout(() => adjustNodeHeight(nodeElement), 100);
                }
                
                // 延迟一点时间再更新设备状态，确保节点已完全创建
                setTimeout(() => {
                    // 更新设备状态
                    updateDeviceStatus(device.id);
                }, 500);
                
                // 返回创建的节点元素
                return nodeElement;
            }
            
            // 选中节点
            function selectNode(node) {
                // 取消之前的选中状态
                if (selectedNode) {
                    selectedNode.classList.remove('selected');
                }
                
                // 取消连接线的选中状态
                if (selectedConnection) {
                    selectedConnection.setPaintStyle({
                        stroke: selectedConnection.originalStroke || "#0d6efd",
                        strokeWidth: selectedConnection.originalStrokeWidth || 2
                    });
                    selectedConnection = null;
                }
                
                // 设置新的选中节点
                selectedNode = node;
                selectedNode.classList.add('selected');
                
                // 启用删除按钮
                document.getElementById('deleteBtn').disabled = false;
                
                // 隐藏所有配置面板
                document.getElementById('noSelectionConfig').style.display = 'none';
                document.getElementById('deviceConfig').style.display = 'none';
                document.getElementById('textConfig').style.display = 'none';
                document.getElementById('connectionConfig').style.display = 'none';
                document.getElementById('imageConfig').style.display = 'none';
                
                // 根据节点类型显示对应的配置面板
                if (node.classList.contains('device-node')) {
                    // 显示设备配置面板
                    document.getElementById('deviceConfig').style.display = 'block';
                    
                    // 填充设备配置表单
                    document.getElementById('deviceName').value = node.querySelector('.title').textContent;
                    
                    // 设置显示标题复选框状态
                    const titleElement = node.querySelector('.title');
                    document.getElementById('showDeviceTitle').checked = titleElement.style.display !== 'none';
                    
                    // 获取背景颜色和透明度
                    const bgColor = node.style.backgroundColor;
                    let bgHex = '#ffffff';
                    let bgOpacity = 100;
                    
                    if (bgColor) {
                        const rgba = parseRgba(bgColor);
                        if (rgba) {
                            bgHex = rgbToHex(rgba.r, rgba.g, rgba.b);
                            bgOpacity = Math.round(rgba.a * 100);
                        }
                    }
                    
                    // 获取边框颜色和透明度
                    const borderColor = node.style.borderColor;
                    let borderHex = '#0d6efd';
                    let borderOpacity = 100;
                    
                    if (borderColor) {
                        const rgba = parseRgba(borderColor);
                        if (rgba) {
                            borderHex = rgbToHex(rgba.r, rgba.g, rgba.b);
                            borderOpacity = Math.round(rgba.a * 100);
                        }
                    }
                    
                    // 设置颜色和透明度值
                    document.getElementById('deviceBgColor').value = bgHex;
                    document.getElementById('deviceBgOpacity').value = bgOpacity;
                    document.getElementById('bgOpacityValue').textContent = bgOpacity;
                    
                    document.getElementById('deviceBorderColor').value = borderHex;
                    document.getElementById('deviceBorderOpacity').value = borderOpacity;
                    document.getElementById('borderOpacityValue').textContent = borderOpacity;
                    
                    // 设置尺寸
                    const width = parseInt(node.style.width) || 150;
                    const height = parseInt(node.style.height) || 100;
                    document.getElementById('deviceWidth').value = width;
                    document.getElementById('deviceHeight').value = height;
                    
                    // 处理图片相关设置
                    const imageElement = node.querySelector('.device-image img');
                    const imagePreview = document.getElementById('deviceImagePreview');
                    
                    if (imageElement) {
                        // 有图片
                        document.getElementById('deviceImageUrl').value = imageElement.src;
                        imagePreview.style.display = 'block';
                        imagePreview.querySelector('img').src = imageElement.src;
                        
                        // 获取图片缩放比例
                        let scale = 1.0;
                        const transform = imageElement.style.transform;
                        if (transform) {
                            const match = transform.match(/scale\(([^)]+)\)/);
                            if (match && match[1]) {
                                scale = parseFloat(match[1]);
                            }
                        }
                        document.getElementById('imageScale').value = scale;
                        document.getElementById('scaleValue').textContent = scale.toFixed(1);
                        
                        // 获取图片偏移
                        const offsetX = parseInt(imageElement.style.left) || 0;
                        const offsetY = parseInt(imageElement.style.top) || 0;
                        document.getElementById('imageOffsetX').value = offsetX;
                        document.getElementById('imageOffsetY').value = offsetY;
                    } else {
                        // 无图片
                        document.getElementById('deviceImageUrl').value = '';
                        imagePreview.style.display = 'none';
                        document.getElementById('imageScale').value = 1.0;
                        document.getElementById('scaleValue').textContent = '1.0';
                        document.getElementById('imageOffsetX').value = 0;
                        document.getElementById('imageOffsetY').value = 0;
                    }
                    
                    // 加载设备的预警项
                    loadDeviceAlerts(node.getAttribute('data-device-id'));
                } else if (node.classList.contains('text-node')) {
                    // 显示文本配置面板
                    document.getElementById('textConfig').style.display = 'block';
                    
                    // 填充文本信息
                    const textElement = node.querySelector('.editable');
                    document.getElementById('textContent').value = textElement.textContent;
                    
                    // 获取字体样式
                    const fontSize = parseInt(textElement.style.fontSize) || 14;
                    const fontColor = textElement.style.color || '#000000';
                    const fontWeight = textElement.style.fontWeight || 'normal';
                    const textAlign = textElement.style.textAlign || 'left';
                    
                    // 设置字体样式
                    document.getElementById('fontSize').value = fontSize;
                    document.getElementById('fontColor').value = fontColor;
                    document.getElementById('fontWeight').value = fontWeight;
                    document.getElementById('textAlign').value = textAlign;
                    
                    // 更新对齐按钮状态
                    document.querySelectorAll('#textConfig .btn-group button').forEach(button => {
                        button.classList.remove('active');
                        if (button.dataset.align === textAlign) {
                            button.classList.add('active');
                        }
                    });
                    
                    // 设置节点尺寸
                    const width = parseInt(node.style.width) || 100;
                    const height = parseInt(node.style.height) || 40;
                    document.getElementById('textWidth').value = width;
                    document.getElementById('textHeight').value = height;
                } else if (node.classList.contains('image-node')) {
                    // 显示图片配置面板
                    document.getElementById('imageConfig').style.display = 'block';
                    
                    // 填充图片信息
                    const titleElement = node.querySelector('.image-title');
                    document.getElementById('imageTitle').value = titleElement ? titleElement.textContent : '图片';
                    
                    // 设置显示标题复选框状态
                    document.getElementById('showImageTitle').checked = titleElement ? titleElement.style.display !== 'none' : true;
                    
                    // 获取背景颜色和透明度
                    const bgColor = node.style.backgroundColor;
                    let bgHex = '#ffffff';
                    let bgOpacity = 100;
                    
                    if (bgColor) {
                        const rgba = parseRgba(bgColor);
                        if (rgba) {
                            bgHex = rgbToHex(rgba.r, rgba.g, rgba.b);
                            bgOpacity = Math.round(rgba.a * 100);
                        }
                    }
                    
                    // 获取边框颜色和透明度
                    const borderColor = node.style.borderColor;
                    let borderHex = '#d9d9d9';
                    let borderOpacity = 100;
                    
                    if (borderColor) {
                        const rgba = parseRgba(borderColor);
                        if (rgba) {
                            borderHex = rgbToHex(rgba.r, rgba.g, rgba.b);
                            borderOpacity = Math.round(rgba.a * 100);
                        }
                    }
                    
                    // 设置颜色和透明度值
                    document.getElementById('imageBgColor').value = bgHex;
                    document.getElementById('imageBgOpacity').value = bgOpacity;
                    document.getElementById('imageBgOpacityValue').textContent = bgOpacity;
                    
                    document.getElementById('imageBorderColor').value = borderHex;
                    document.getElementById('imageBorderOpacity').value = borderOpacity;
                    document.getElementById('imageBorderOpacityValue').textContent = borderOpacity;
                    
                    // 设置尺寸
                    const width = parseInt(node.style.width) || 200;
                    const height = parseInt(node.style.height) || 150;
                    document.getElementById('imageWidth').value = width;
                    document.getElementById('imageHeight').value = height;
                    
                    // 处理图片相关设置
                    const imageElement = node.querySelector('.image-container img');
                    const imagePreview = document.getElementById('imageNodePreview');
                    
                    if (imageElement) {
                        // 有图片
                        document.getElementById('imageNodeUrl').value = imageElement.src;
                        imagePreview.style.display = 'block';
                        imagePreview.querySelector('img').src = imageElement.src;
                        
                        // 获取图片缩放比例
                        let scale = 1.0;
                        const transform = imageElement.style.transform;
                        if (transform) {
                            const match = transform.match(/scale\(([0-9.]+)\)/);
                            if (match && match[1]) {
                                scale = parseFloat(match[1]);
                            }
                        }
                        document.getElementById('imageNodeScale').value = scale;
                        document.getElementById('imageNodeScaleValue').textContent = scale.toFixed(1);
                    } else {
                        // 无图片
                        document.getElementById('imageNodeUrl').value = '';
                        imagePreview.style.display = 'none';
                        document.getElementById('imageNodeScale').value = 1.0;
                        document.getElementById('imageNodeScaleValue').textContent = '1.0';
                    }
                } else if (node.classList.contains('chart-node')) {
                    // 显示图表配置面板
                    document.getElementById('chartConfig').style.display = 'block';
                    
                    // 填充图表信息
                    const titleElement = node.querySelector('.chart-title');
                    document.getElementById('chartTitle').value = titleElement ? titleElement.textContent : '图表';
                    
                    // 设置显示标题复选框状态
                    document.getElementById('showChartTitle').checked = titleElement ? titleElement.style.display !== 'none' : true;
                    
                    // 获取图表类型
                    const chartType = node.getAttribute('data-component-type');
                    document.getElementById('chartType').value = chartType;
                    
                    // 获取背景和边框颜色
                    const bgColor = node.style.backgroundColor || '#ffffff';
                    const borderColor = node.style.borderColor || '#d9d9d9';
                    
                    document.getElementById('chartBgColor').value = bgColor;
                    document.getElementById('chartBorderColor').value = borderColor;
                    
                    // 设置尺寸
                    const width = parseInt(node.style.width) || 300;
                    const height = parseInt(node.style.height) || 200;
                    document.getElementById('chartWidth').value = width;
                    document.getElementById('chartHeight').value = height;
                    
                    // 获取刷新间隔
                    const refreshInterval = node.getAttribute('data-refresh-interval') || 5;
                    document.getElementById('chartRefreshInterval').value = refreshInterval;
                    
                    // 获取数据源
                    const dataSource = node.getAttribute('data-data-source') || '';
                    document.getElementById('chartDataSource').value = dataSource;
                } else if (node.classList.contains('connection')) {
                    // 显示连接线配置面板
                    document.getElementById('connectionConfig').style.display = 'block';
                } else {
                    // 显示无选中项提示
                    document.getElementById('noSelectionConfig').style.display = 'block';
                }
            }
            
            // RGB颜色转十六进制
            function rgbToHex(rgb) {
                // 默认返回黑色
                if (!rgb || rgb === 'rgba(0, 0, 0, 0)') return '#000000';
                
                // 处理rgb格式
                const rgbMatch = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                if (rgbMatch) {
                    const r = parseInt(rgbMatch[1]);
                    const g = parseInt(rgbMatch[2]);
                    const b = parseInt(rgbMatch[3]);
                    return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                }
                
                // 如果已经是十六进制格式，直接返回
                if (rgb.startsWith('#')) return rgb;
                
                return '#000000';
            }
            
            // 取消选中
            document.getElementById('canvas').addEventListener('click', function(event) {
                if (event.target === this) {
                    // 取消节点选中状态
                    if (selectedNode) {
                        selectedNode.classList.remove('selected');
                        selectedNode = null;
                    }
                    
                    // 取消连接线选中状态
                    if (selectedConnection) {
                        selectedConnection = null;
                    }
                    
                    // 禁用删除按钮
                    document.getElementById('deleteBtn').disabled = true;
                    
                    // 显示无选中项提示
                    document.getElementById('deviceConfig').style.display = 'none';
                    document.getElementById('textConfig').style.display = 'none';
                    document.getElementById('connectionConfig').style.display = 'none';
                    document.getElementById('imageConfig').style.display = 'none';
                    document.getElementById('noSelectionConfig').style.display = 'block';
                }
            });
            
            // 应用设备配置
            document.getElementById('applyDeviceConfig').addEventListener('click', function() {
                if (!selectedNode) return;
                
                const deviceName = document.getElementById('deviceName').value;
                const deviceBgColor = document.getElementById('deviceBgColor').value;
                const deviceBgOpacity = document.getElementById('deviceBgOpacity').value;
                const deviceBorderColor = document.getElementById('deviceBorderColor').value;
                const deviceBorderOpacity = document.getElementById('deviceBorderOpacity').value;
                const imageUrl = document.getElementById('deviceImageUrl').value;
                const imageScale = parseFloat(document.getElementById('imageScale').value);
                const imageOffsetX = parseInt(document.getElementById('imageOffsetX').value) || 0;
                const imageOffsetY = parseInt(document.getElementById('imageOffsetY').value) || 0;
                const deviceWidth = parseInt(document.getElementById('deviceWidth').value) || 150;
                const deviceHeight = parseInt(document.getElementById('deviceHeight').value) || 100;
                const showTitle = document.getElementById('showDeviceTitle').checked;
                
                // 更新设备名称
                const titleElement = selectedNode.querySelector('.title');
                titleElement.textContent = deviceName;
                
                // 更新标题显示状态
                titleElement.style.display = showTitle ? '' : 'none';
                
                // 计算带透明度的颜色值
                const bgOpacity = deviceBgOpacity / 100;
                const borderOpacity = deviceBorderOpacity / 100;
                
                // 将HEX颜色转换为RGBA
                const bgRgba = hexToRgba(deviceBgColor, bgOpacity);
                const borderRgba = hexToRgba(deviceBorderColor, borderOpacity);
                
                // 更新样式
                selectedNode.style.backgroundColor = bgRgba;
                selectedNode.style.borderColor = borderRgba;
                selectedNode.style.width = `${deviceWidth}px`;
                selectedNode.style.height = `${deviceHeight}px`;
                
                // 处理图片
                if (imageUrl) {
                    // 检查是否已有图片元素
                    let imageContainer = selectedNode.querySelector('.device-image');
                    
                    if (!imageContainer) {
                        // 创建图片容器
                        imageContainer = document.createElement('div');
                        imageContainer.className = 'device-image';
                        
                        // 插入到标题前面
                        selectedNode.insertBefore(imageContainer, selectedNode.querySelector('.title'));
                        
                        // 添加has-image类
                        selectedNode.classList.add('has-image');
                    }
                    
                    // 更新或创建图片元素
                    let imageElement = imageContainer.querySelector('img');
                    if (!imageElement) {
                        imageElement = document.createElement('img');
                        imageContainer.appendChild(imageElement);
                    }
                    
                    // 设置图片属性
                    imageElement.src = imageUrl;
                    imageElement.alt = deviceName;
                    imageElement.style.transform = `scale(${imageScale})`;
                    imageElement.style.transformOrigin = 'center center';
                    imageElement.style.position = 'relative';
                    imageElement.style.left = `${imageOffsetX}px`;
                    imageElement.style.top = `${imageOffsetY}px`;
                } else {
                    // 移除图片元素
                    const imageContainer = selectedNode.querySelector('.device-image');
                    if (imageContainer) {
                        imageContainer.remove();
                        selectedNode.classList.remove('has-image');
                    }
                }
                
                // 重绘jsPlumb连接
                jsPlumbInstance.repaintEverything();
            });
            
            // 加载图片URL
            document.getElementById('loadImageUrl').addEventListener('click', function() {
                const imageUrl = document.getElementById('deviceImageUrl').value;
                if (!imageUrl) return;
                
                const imagePreview = document.getElementById('deviceImagePreview');
                imagePreview.style.display = 'block';
                imagePreview.querySelector('img').src = imageUrl;
            });
            
            // 移除图片
            document.getElementById('removeDeviceImage').addEventListener('click', function() {
                document.getElementById('deviceImageUrl').value = '';
                document.getElementById('deviceImagePreview').style.display = 'none';
            });
            
            // 处理本地图片上传
            document.getElementById('deviceImageFile').addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件');
                    return;
                }
                
                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);
                
                // 显示上传中提示
                    const imagePreview = document.getElementById('deviceImagePreview');
                    imagePreview.style.display = 'block';
                imagePreview.querySelector('img').src = '/images/loading.gif';
                
                // 上传图片到服务器
                fetch('/api/upload/image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '上传失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    // 更新预览和URL输入框
                    const imageUrl = data.url; // 使用服务器返回的完整URL
                    document.getElementById('deviceImageUrl').value = imageUrl;
                    imagePreview.querySelector('img').src = imageUrl;
                })
                .catch(error => {
                    alert('图片上传失败: ' + error.message);
                    imagePreview.style.display = 'none';
                });
            });
            
            // 显示文本配置面板
            function showTextConfig(node) {
                hideAllConfig();
                const textConfig = document.getElementById('textConfig');
                textConfig.style.display = 'block';
                
                const textElement = node.querySelector('.editable');
                
                // 设置文本内容
                document.getElementById('textContent').value = textElement.textContent;
                
                // 设置字体大小
                const fontSize = parseInt(textElement.style.fontSize) || 14;
                document.getElementById('fontSize').value = fontSize;
                
                // 设置字体颜色
                document.getElementById('fontColor').value = textElement.style.color || '#000000';
                
                // 设置字体粗细
                document.getElementById('fontWeight').value = textElement.style.fontWeight || 'normal';
                
                // 设置文本对齐
                document.getElementById('textAlign').value = textElement.style.textAlign || 'left';
                
                // 设置边框显示状态
                document.getElementById('showBorder').checked = !node.classList.contains('no-border');
                
                // 设置节点尺寸
                document.getElementById('textWidth').value = node.offsetWidth;
                document.getElementById('textHeight').value = node.offsetHeight;
                
                // 更新文本对齐按钮状态
                updateTextAlignButtons(textElement.style.textAlign || 'left');
                
                // 添加事件监听
                setupTextConfigListeners(node);
            }
            
            // 应用文本配置
            document.getElementById('applyTextConfig').addEventListener('click', function() {
                if (selectedNode && selectedNode.classList.contains('text-node')) {
                    const textElement = selectedNode.querySelector('.editable');
                    
                    // 应用文本内容
                    textElement.textContent = document.getElementById('textContent').value;
                    
                    // 应用字体样式
                    textElement.style.fontSize = `${document.getElementById('fontSize').value}px`;
                    textElement.style.color = document.getElementById('fontColor').value;
                    textElement.style.fontWeight = document.getElementById('fontWeight').value;
                    textElement.style.textAlign = document.getElementById('textAlign').value;
                    
                    // 应用节点尺寸
                    selectedNode.style.width = `${document.getElementById('textWidth').value}px`;
                    selectedNode.style.height = `${document.getElementById('textHeight').value}px`;
                    
                    // 应用边框显示状态
                    const showBorder = document.getElementById('showBorder').checked;
                    if (showBorder) {
                        selectedNode.classList.remove('no-border');
                    } else {
                        selectedNode.classList.add('no-border');
                    }
                }
            });
            
            // 应用连接线配置
            document.getElementById('applyConnectionConfig').addEventListener('click', function() {
                if (selectedConnection) {
                    // 获取配置值
                    const lineColor = document.getElementById('lineColor').value;
                    const lineWidth = parseInt(document.getElementById('lineWidth').value);
                    const lineStyle = document.getElementById('lineStyle').value;
                    const enableAnimation = document.getElementById('enableAnimation').checked;
                    const animationSpeed = document.getElementById('animationSpeed').value;
                    const reverseFlow = document.getElementById('reverseFlow').checked;
                    
                    // 设置线条样式
                    let strokeDasharray = null;
                    if (lineStyle === 'dotted') {
                        strokeDasharray = '2 2';
                    } else if (lineStyle === 'dashed') {
                        strokeDasharray = '5 5';
                    }
                    
                    // 应用线条样式
                    selectedConnection.setPaintStyle({
                        stroke: lineColor,
                        strokeWidth: lineWidth,
                        strokeDasharray: strokeDasharray
                    });
                    
                    // 直接设置SVG路径的虚线样式
                    if (selectedConnection.canvas) {
                        const path = selectedConnection.canvas;
                        if (strokeDasharray) {
                            path.setAttribute('stroke-dasharray', strokeDasharray);
                        } else {
                            path.removeAttribute('stroke-dasharray');
                        }
                    }
                    
                    // 处理动态效果 - 使用自定义属性存储
                    selectedConnection.hasAnimation = enableAnimation;
                    selectedConnection.animationSpeed = animationSpeed;
                    selectedConnection.isReverseFlow = reverseFlow;
                    
                    // 获取连接线的SVG容器元素
                    const connector = selectedConnection.connector.canvas;
                    
                    if (connector) {
                        // 移除所有动画相关的类
                        connector.classList.remove('animated-connection');
                        connector.classList.remove('reverse-flow');
                        connector.classList.remove('animation-slow');
                        connector.classList.remove('animation-medium');
                        connector.classList.remove('animation-fast');
                        
                        if (enableAnimation) {
                            // 添加动画类
                            connector.classList.add('animated-connection');
                            connector.classList.add('animation-' + animationSpeed);
                            
                            // 根据用户选择应用反向流动
                            if (reverseFlow) {
                                connector.classList.add('reverse-flow');
                            }
                            
                            // 当启用动态效果时，禁用线条样式选择
                            document.getElementById('lineStyle').disabled = true;
                        } else {
                            // 当禁用动态效果时，启用线条样式选择
                            document.getElementById('lineStyle').disabled = false;
                        }
                    }
                    
                    // 刷新连接线显示
                    jsPlumbInstance.repaint(selectedConnection.sourceId);
                    jsPlumbInstance.repaint(selectedConnection.targetId);
                }
            });
            
            // 删除选中的节点或连接线
            function deleteSelectedNode() {
                if (selectedNode) {
                    // 删除与节点相关的所有连接
                    jsPlumbInstance.removeAllEndpoints(selectedNode.id);
                    
                    // 从DOM中移除节点
                    selectedNode.remove();
                    
                    // 重置选中状态
                    selectedNode = null;
                    document.getElementById('deleteBtn').disabled = true;
                    
                    // 显示无选中项提示
                    document.getElementById('deviceConfig').style.display = 'none';
                    document.getElementById('textConfig').style.display = 'none';
                    document.getElementById('connectionConfig').style.display = 'none';
                    document.getElementById('imageConfig').style.display = 'none';
                    document.getElementById('chartConfig').style.display = 'none';
                    document.getElementById('noSelectionConfig').style.display = 'block';
                } else if (selectedConnection) {
                    // 删除选中的连接线
                    jsPlumbInstance.deleteConnection(selectedConnection);
                    
                    // 重置选中状态
                    selectedConnection = null;
                    document.getElementById('deleteBtn').disabled = true;
                    
                    // 显示无选中项提示
                    document.getElementById('deviceConfig').style.display = 'none';
                    document.getElementById('textConfig').style.display = 'none';
                    document.getElementById('connectionConfig').style.display = 'none';
                    document.getElementById('imageConfig').style.display = 'none';
                    document.getElementById('chartConfig').style.display = 'none';
                    document.getElementById('noSelectionConfig').style.display = 'block';
                }
            }
            
            // 保存组态布局
            async function saveTopology() {
                try {
                    // 获取布局名称
                    const layoutName = document.getElementById('layoutName').value.trim();
                    if (!layoutName) {
                        // 显示错误提示
                        document.getElementById('layoutName').classList.add('is-invalid');
                        return;
                    } else {
                        document.getElementById('layoutName').classList.remove('is-invalid');
                    }
                    
                    // 获取所有节点
                    const nodes = [];
                    const deviceNodes = document.querySelectorAll('.device-node');
                    const textNodes = document.querySelectorAll('.text-node');
                    const imageNodes = document.querySelectorAll('.image-node');
                    const chartNodes = document.querySelectorAll('.chart-node');
                    const connections = [];
                    
                    // 收集设备节点信息
                    deviceNodes.forEach(node => {
                        const nodeRect = node.getBoundingClientRect();
                        const canvasRect = document.getElementById('canvas').getBoundingClientRect();
                        
                        // 获取节点位置（相对于画布）
                        const x = parseInt(node.style.left);
                        const y = parseInt(node.style.top);
                        
                        // 获取节点尺寸
                        const width = parseInt(node.style.width) || 150;
                        const height = parseInt(node.style.height) || 100;
                        
                        // 获取设备信息
                        const deviceId = node.getAttribute('data-device-id');
                        const deviceName = node.querySelector('.title').textContent;
                        const deviceAddress = node.getAttribute('data-device-address');
                        const devicePort = node.getAttribute('data-device-port');
                        
                        // 获取图片信息
                        const imageElement = node.querySelector('.device-image img');
                        let imageUrl = '';
                        if (imageElement) {
                            imageUrl = imageElement.src;
                        }
                        
                        // 获取图片缩放比例
                        let imageScale = 1.0;
                        if (imageElement && imageElement.style.transform) {
                            const match = imageElement.style.transform.match(/scale\(([0-9.]+)\)/);
                            if (match && match[1]) {
                                imageScale = parseFloat(match[1]);
                            }
                        }
                        
                        // 获取图片偏移
                        let imageOffsetX = 0;
                        let imageOffsetY = 0;
                        const imageContainer = node.querySelector('.device-image');
                        if (imageContainer) {
                            if (imageContainer.style.left) {
                                imageOffsetX = parseInt(imageContainer.style.left);
                            }
                            if (imageContainer.style.top) {
                                imageOffsetY = parseInt(imageContainer.style.top);
                            }
                        }
                        
                        // 获取背景和边框颜色
                        const backgroundColor = node.style.backgroundColor;
                        const borderColor = node.style.borderColor;
                        
                        // 获取标题显示状态
                        const titleElement = node.querySelector('.title');
                        const showTitle = titleElement ? titleElement.style.display !== 'none' : true;
                        
                        // 获取预警项
                        const alerts = [];
                        const alertItems = node.querySelectorAll('.alert-status-item');
                        alertItems.forEach(item => {
                            alerts.push({
                                id: item.dataset.alertId,
                                name: item.dataset.alertName
                            });
                        });
                        
                        // 添加节点信息
                        nodes.push({
                            type: 'device',
                            id: node.id,
                            deviceId: deviceId,
                            name: deviceName,
                            address: deviceAddress,
                            port: devicePort,
                            x: x,
                            y: y,
                            width: width,
                            height: height,
                            imageUrl: imageUrl,
                            imageScale: imageScale,
                            imageOffsetX: imageOffsetX,
                            imageOffsetY: imageOffsetY,
                            backgroundColor: backgroundColor,
                            borderColor: borderColor,
                            showTitle: showTitle,
                            alertItems: alerts  // 添加预警项到节点数据中
                        });
                    });
                    
                    // 收集文本节点信息
                    textNodes.forEach(node => {
                        const x = parseInt(node.style.left);
                        const y = parseInt(node.style.top);
                        const width = parseInt(node.style.width) || 100;
                        const height = parseInt(node.style.height) || 40;
                        
                        const textElement = node.querySelector('.editable');
                        const text = textElement ? textElement.textContent : '';
                        const fontSize = textElement ? parseInt(textElement.style.fontSize) : 14;
                        const fontColor = textElement ? textElement.style.color : '#000000';
                        const fontWeight = textElement ? textElement.style.fontWeight : 'normal';
                        const textAlign = textElement ? textElement.style.textAlign : 'left';
                        const showBorder = !node.classList.contains('no-border');
                        
                        nodes.push({
                            type: 'text',
                            id: node.id,
                            componentType: 'text',
                            text: text,
                            x: x,
                            y: y,
                            width: width,
                            height: height,
                            fontSize: fontSize,
                            fontColor: fontColor,
                            fontWeight: fontWeight,
                            textAlign: textAlign,
                            showBorder: showBorder
                        });
                    });
                    
                    // 收集图片节点信息
                    const imageNodesData = [];  // 创建单独的数组存储图片节点数据
                    imageNodes.forEach(node => {
                        const x = parseInt(node.style.left);
                        const y = parseInt(node.style.top);
                        const width = parseInt(node.style.width) || 200;
                        const height = parseInt(node.style.height) || 150;
                        
                        // 获取图片标题
                        const titleElement = node.querySelector('.image-title');
                        const title = titleElement ? titleElement.textContent : '图片';
                        
                        // 获取标题显示状态
                        const showTitle = titleElement ? titleElement.style.display !== 'none' : true;
                        
                        // 获取图片URL
                        const imageElement = node.querySelector('.image-container img');
                        let imageUrl = '';
                        if (imageElement) {
                            imageUrl = imageElement.src;
                            // 如果URL是完整路径，只保留相对路径部分
                            if (imageUrl.startsWith(window.location.origin)) {
                                imageUrl = imageUrl.substring(window.location.origin.length);
                            }
                        }
                        
                        // 获取图片缩放比例
                        let imageScale = 1.0;
                        if (imageElement && imageElement.style.transform) {
                            const match = imageElement.style.transform.match(/scale\(([0-9.]+)\)/);
                            if (match && match[1]) {
                                imageScale = parseFloat(match[1]);
                            }
                        }
                        
                        // 获取背景和边框颜色
                        const backgroundColor = node.style.backgroundColor;
                        const borderColor = node.style.borderColor;
                        
                        // 添加节点信息到imageNodesData数组
                        imageNodesData.push({
                            type: 'image',
                            id: node.id,
                            className: node.className,
                            componentType: 'image',
                            title: title,
                            showTitle: showTitle,
                            imageUrl: imageUrl,
                            imageScale: imageScale,
                            x: x,
                            y: y,
                            width: width,
                            height: height,
                            backgroundColor: backgroundColor,
                            borderColor: borderColor
                        });
                    });
                    
                    // 收集连接信息
                    jsPlumbInstance.getConnections().forEach(connection => {
                        const sourceId = connection.sourceId;
                        const targetId = connection.targetId;
                        const sourceEndpoint = connection.endpoints[0];
                        const targetEndpoint = connection.endpoints[1];
                        
                        // 获取连接样式
                        const paintStyle = connection.getPaintStyle();
                        const strokeColor = paintStyle.stroke;
                        const strokeWidth = paintStyle.strokeWidth;
                        const strokeDasharray = paintStyle.strokeDasharray;
                        
                        // 获取动画设置
                        const hasAnimation = connection.hasAnimation === true;
                        const isReverseFlow = connection.isReverseFlow === true;
                        const animationSpeed = connection.animationSpeed || 'medium';
                        
                        connections.push({
                            sourceId: sourceId,
                            targetId: targetId,
                            sourceEndpointUuid: sourceEndpoint.getUuid(),
                            targetEndpointUuid: targetEndpoint.getUuid(),
                            strokeColor: strokeColor,
                            strokeWidth: strokeWidth,
                            strokeDasharray: strokeDasharray,
                            hasAnimation: hasAnimation,
                            isReverseFlow: isReverseFlow,
                            animationSpeed: animationSpeed
                        });
                    });
                    
                    // 构建保存数据
                    const saveData = {
                        name: layoutName,
                        nodes: nodes.filter(node => node.type === 'device'),
                        textNodes: nodes.filter(node => node.type === 'text'),
                        imageNodes: imageNodesData,  // 使用单独收集的图片节点数据
                        connections: connections,
                        // 添加画布配置信息
                        canvasConfig: {
                            width: parseInt(document.getElementById('canvasWidth').value) || 3000,
                            height: parseInt(document.getElementById('canvasHeight').value) || 2000,
                            showGrid: document.getElementById('showGrid').checked,
                            gridSize: parseInt(document.getElementById('gridSize').value) || 20,
                            gridColor: document.getElementById('gridColor').value || '#e9ecef',
                            backgroundColor: document.getElementById('canvasBackgroundColor').value || '#ffffff'
                        }
                    };

                    console.log('保存的数据:', saveData);  // 添加日志输出
                    
                    // 发送保存请求
                    const response = await fetch('/topology/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(saveData)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`保存失败: ${response.status} ${response.statusText}`);
                    }
                    
                    // 更新布局下拉列表
                    loadLayoutList();
                    
                    alert('组态布局保存成功');
                } catch (error) {
                    console.error('保存组态布局失败:', error);
                    alert('保存失败: ' + error.message);
                }
            }
            
            // 加载保存的组态布局
            async function loadTopology() {
                try {
                    const response = await fetch('/topology/load');
                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || '加载失败');
                    }
                    
                    const topology = await response.json();
                    
                    // 设置布局名称
                    if (topology.name) {
                        document.getElementById('layoutName').value = topology.name;
                    }
                    
                    // 清空画布
                    resetCanvas();
                    
                    // 应用画布配置（如果有）
                    if (topology.canvasConfig) {
                        const canvas = document.getElementById('canvas');
                        const config = topology.canvasConfig;
                        
                        // 设置画布尺寸
                        if (config.width) {
                            canvas.style.width = `${config.width}px`;
                            document.getElementById('canvasWidth').value = config.width;
                        }
                        
                        if (config.height) {
                            canvas.style.height = `${config.height}px`;
                            document.getElementById('canvasHeight').value = config.height;
                        }
                        
                        // 设置背景颜色
                        if (config.backgroundColor) {
                            canvas.style.backgroundColor = config.backgroundColor;
                            document.getElementById('canvasBackgroundColor').value = config.backgroundColor;
                        }
                        
                        // 设置网格
                        document.getElementById('showGrid').checked = config.showGrid !== false;
                        
                        if (config.gridSize) {
                            document.getElementById('gridSize').value = config.gridSize;
                        }
                        
                        if (config.gridColor) {
                            document.getElementById('gridColor').value = config.gridColor;
                        }
                        
                        // 应用网格样式
                        if (config.showGrid !== false) {
                            canvas.style.backgroundImage = `
                                linear-gradient(${config.gridColor || '#e9ecef'} 1px, transparent 1px),
                                linear-gradient(90deg, ${config.gridColor || '#e9ecef'} 1px, transparent 1px)
                            `;
                            canvas.style.backgroundSize = `${config.gridSize || 20}px ${config.gridSize || 20}px`;
                        } else {
                            canvas.style.backgroundImage = 'none';
                        }
                    }
                    
                    // 创建设备节点
                    if (topology.nodes && topology.nodes.length > 0) {
                        // 先获取设备信息
                        const devicesResponse = await fetch('/api/devices');
                        if (!devicesResponse.ok) {
                            throw new Error('获取设备列表失败');
                        }
                        const devices = await devicesResponse.json();
                        const deviceMap = new Map();
                        devices.forEach(device => {
                            deviceMap.set(device.id, device);
                        });
                        
                        // 创建设备节点
                        topology.nodes.forEach(node => {
                            // 检查设备ID是否有效
                            if (!node.deviceId || node.deviceId === 'undefined') {
                                console.warn(`跳过无效的设备节点: ${JSON.stringify(node)}`);
                                return;
                            }
                            
                            // 获取设备信息
                            const device = {
                                id: node.deviceId,
                                name: node.name || '未命名设备',
                                address: node.address || 'localhost',
                                port: node.port || '0',
                                imageUrl: node.imageUrl || null,
                                imageScale: node.imageScale || 1.0,
                                imageOffsetX: node.imageOffsetX || 0,
                                imageOffsetY: node.imageOffsetY || 0,
                                alertItems: node.alertItems || []
                            };
                            
                            // 创建节点
                            const deviceNode = createDeviceNode(device, node.x, node.y);
                            
                            // 应用保存的样式（如果有）
                            if (deviceNode) {
                                if (node.backgroundColor) {
                                    deviceNode.style.backgroundColor = node.backgroundColor;
                                }
                                if (node.borderColor) {
                                    deviceNode.style.borderColor = node.borderColor;
                                }
                                
                                // 应用保存的宽高
                                if (node.width) {
                                    deviceNode.style.width = `${node.width}px`;
                                }
                                if (node.height) {
                                    deviceNode.style.height = `${node.height}px`;
                                }
                            }
                        });
                        
                        // 等待所有节点渲染完成后更新端点位置
                        setTimeout(() => {
                            document.querySelectorAll('.device-node').forEach(node => {
                                // 重新验证节点以更新端点位置
                                jsPlumbInstance.revalidate(node.id);
                            });
                            jsPlumbInstance.repaintEverything();
                        }, 100);
                    }
                        
                        // 创建文本节点
                        if (topology.textNodes && topology.textNodes.length > 0) {
                            topology.textNodes.forEach(node => {
                                const textNode = document.createElement('div');
                                textNode.id = node.id;
                                textNode.className = 'text-node';
                                if (node.showBorder === false) {
                                    textNode.classList.add('no-border');
                                }
                                textNode.setAttribute('data-component-type', 'text');
                                textNode.style.left = `${node.x}px`;
                                textNode.style.top = `${node.y}px`;
                                
                                // 应用保存的宽高
                                textNode.style.width = node.width ? `${node.width}px` : '100px';
                                textNode.style.height = node.height ? `${node.height}px` : '40px';
                                
                                const textElement = document.createElement('div');
                                textElement.className = 'editable';
                                textElement.contentEditable = 'true';
                                textElement.textContent = node.text;
                                
                                // 应用文本样式
                                if (node.fontSize) {
                                    textElement.style.fontSize = `${node.fontSize}px`;
                                }
                                if (node.fontColor) {
                                    textElement.style.color = node.fontColor;
                                }
                                if (node.fontWeight) {
                                    textElement.style.fontWeight = node.fontWeight;
                                }
                                if (node.textAlign) {
                                    textElement.style.textAlign = node.textAlign;
                                }
                                
                                // 创建调整大小的手柄
                                const resizeHandle = document.createElement('div');
                                resizeHandle.className = 'resize-handle';
                                
                                // 添加调整大小的事件处理
                                resizeHandle.addEventListener('mousedown', function(e) {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    
                                    // 记录初始位置和大小
                                    const startX = e.clientX;
                                    const startY = e.clientY;
                                    const startWidth = textNode.offsetWidth;
                                    const startHeight = textNode.offsetHeight;
                                    
                                    // 创建调整大小的函数
                                    function resize(e) {
                                        // 计算新的宽度和高度
                                        const newWidth = Math.max(100, startWidth + e.clientX - startX);
                                        const newHeight = Math.max(40, startHeight + e.clientY - startY);
                                        
                                        // 应用新的宽度和高度
                                        textNode.style.width = `${newWidth}px`;
                                        textNode.style.height = `${newHeight}px`;
                                        
                                        // 如果当前节点被选中，更新配置面板中的尺寸值
                                        if (textNode.classList.contains('selected')) {
                                            document.getElementById('textWidth').value = newWidth;
                                            document.getElementById('textHeight').value = newHeight;
                                        }
                                    }
                                    
                                    // 创建停止调整大小的函数
                                    function stopResize() {
                                        document.removeEventListener('mousemove', resize);
                                        document.removeEventListener('mouseup', stopResize);
                                    }
                                    
                                    // 添加事件监听器
                                    document.addEventListener('mousemove', resize);
                                    document.addEventListener('mouseup', stopResize);
                                });
                                
                                // 添加元素到节点
                                textNode.appendChild(textElement);
                                textNode.appendChild(resizeHandle);
                                
                                // 添加点击事件（选中节点）
                                textNode.addEventListener('click', function(event) {
                                    event.stopPropagation();
                                    selectNode(textNode);
                                });
                                
                                // 添加双击事件（编辑文本）
                                textElement.addEventListener('dblclick', function(event) {
                                    event.stopPropagation();
                                    this.focus();
                                });
                                
                                // 添加到画布
                                canvas.appendChild(textNode);
                                
                                // 使节点可拖动
                                jsPlumbInstance.draggable(node.id, {
                                    grid: [10, 10]
                                });
                            });
                        }
                        
                        // 创建连接
                        if (topology.connections && topology.connections.length > 0) {
                            // 延迟创建连接，确保节点和端点都已就绪
                            setTimeout(() => {
                                topology.connections.forEach(conn => {
                                    try {
                                        let connection = jsPlumbInstance.connect({
                                            uuids: [conn.sourceEndpointUuid, conn.targetEndpointUuid]
                                        });
                                        
                                        if (connection) {
                                            // 应用线条样式
                                            connection.setPaintStyle({
                                                stroke: conn.strokeColor,
                                                strokeWidth: conn.strokeWidth,
                                                strokeDasharray: conn.strokeDasharray
                                            });
                                            
                                            // 应用动画效果
                                            if (conn.hasAnimation) {
                                                connection.hasAnimation = conn.hasAnimation;
                                                connection.isReverseFlow = conn.isReverseFlow;
                                                connection.animationSpeed = conn.animationSpeed;
                                                
                                                // 获取连接线的SVG容器元素
                                                const connector = connection.connector.canvas;
                                                if (connector) {
                                                    // 添加动画类
                                                    connector.classList.add('animated-connection');
                                                    connector.classList.add('animation-' + conn.animationSpeed);
                                                    
                                                    // 根据保存的设置应用反向流动
                                                    if (conn.isReverseFlow) {
                                                        connector.classList.add('reverse-flow');
                                                    }
                                                }
                                            }
                                        }
                                    } catch (error) {
                                        console.error('创建连接失败:', error);
                                    }
                                });
                                
                                // 最后再次重绘
                                jsPlumbInstance.repaintEverything();
                            }, 200);
                    }
                        
                        // 创建图片节点
                        if (topology.imageNodes && topology.imageNodes.length > 0) {
                            topology.imageNodes.forEach(node => {
                                const imageNode = document.createElement('div');
                                imageNode.id = node.id;
                                imageNode.className = node.className || 'image-node';
                                imageNode.setAttribute('data-component-type', 'image');
                                imageNode.style.left = `${node.x}px`;
                                imageNode.style.top = `${node.y}px`;
                                
                                // 应用保存的宽高
                                if (node.width) {
                                    imageNode.style.width = `${node.width}px`;
                                }
                                if (node.height) {
                                    imageNode.style.height = `${node.height}px`;
                                }
                                
                                // 应用背景和边框颜色
                                if (node.backgroundColor) {
                                    imageNode.style.backgroundColor = node.backgroundColor;
                                }
                                if (node.borderColor) {
                                    imageNode.style.borderColor = node.borderColor;
                                }
                                
                                // 创建图片标题
                                const titleElement = document.createElement('div');
                                titleElement.className = 'image-title';
                                titleElement.textContent = node.title || '图片';
                                if (node.showTitle === false) {
                                    titleElement.style.display = 'none';
                                }
                                
                                // 创建图片容器
                                const imageContainer = document.createElement('div');
                                imageContainer.className = 'image-container';
                                
                                // 如果有图片URL，创建图片元素
                                if (node.imageUrl) {
                                    const imageElement = document.createElement('img');
                                    imageElement.src = node.imageUrl;
                                    imageElement.alt = node.title || '图片';
                                    
                                    // 设置图片缩放
                                    if (node.imageScale) {
                                        imageElement.style.transform = `scale(${node.imageScale})`;
                                    }
                                    
                                    imageContainer.appendChild(imageElement);
                                        } else {
                                    // 没有图片URL，显示提示
                                    imageContainer.innerHTML = '<div class="no-image">请添加图片</div>';
                                }
                                
                                // 添加元素到节点
                                imageNode.appendChild(titleElement);
                                imageNode.appendChild(imageContainer);
                                
                                // 创建调整大小的手柄
                                const resizeHandle = document.createElement('div');
                                resizeHandle.className = 'resize-handle';
                                imageNode.appendChild(resizeHandle);
                                
                                // 添加调整大小的事件处理
                                if (resizeHandle) {
                                    resizeHandle.addEventListener('mousedown', function(e) {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        
                                        // 记录初始位置和大小
                                        const startX = e.clientX;
                                        const startY = e.clientY;
                                        const startWidth = imageNode.offsetWidth;
                                        const startHeight = imageNode.offsetHeight;
                                        
                                        // 创建调整大小的函数
                                        function resize(e) {
                                            // 计算新的宽度和高度
                                            const newWidth = Math.max(150, startWidth + e.clientX - startX);
                                            const newHeight = Math.max(100, startHeight + e.clientY - startY);
                                            
                                            // 应用新的宽度和高度
                                            imageNode.style.width = `${newWidth}px`;
                                            imageNode.style.height = `${newHeight}px`;
                                            
                                            // 如果当前节点被选中，更新配置面板中的尺寸值
                                            if (imageNode.classList.contains('selected')) {
                                                document.getElementById('imageWidth').value = newWidth;
                                                document.getElementById('imageHeight').value = newHeight;
                                            }
                                        }
                                        
                                        // 创建停止调整大小的函数
                                        function stopResize() {
                                            document.removeEventListener('mousemove', resize);
                                            document.removeEventListener('mouseup', stopResize);
                                        }
                                        
                                        // 添加事件监听器
                                        document.addEventListener('mousemove', resize);
                                        document.addEventListener('mouseup', stopResize);
                                    });
                                }
                                
                                // 添加点击事件（选中节点）
                                imageNode.addEventListener('click', function(event) {
                                    event.stopPropagation();
                                    selectNode(imageNode);
                                });
                                
                                // 添加到画布
                                canvas.appendChild(imageNode);
                                
                                // 使节点可拖动
                                jsPlumbInstance.draggable(node.id, {
                                    grid: [10, 10]
                                });
                            });
                        }
                        
                        // 创建图表节点
                        if (topology.chartNodes && topology.chartNodes.length > 0) {
                            topology.chartNodes.forEach(node => {
                                const chartNode = document.createElement('div');
                                chartNode.id = node.id;
                                chartNode.className = 'chart-node';
                                chartNode.setAttribute('data-component-type', node.type);
                                chartNode.style.left = `${node.x}px`;
                                chartNode.style.top = `${node.y}px`;
                                
                                // 应用保存的宽高
                                if (node.width) {
                                    chartNode.style.width = `${node.width}px`;
                                }
                                if (node.height) {
                                    chartNode.style.height = `${node.height}px`;
                                }
                                
                                // 应用背景和边框颜色
                                if (node.backgroundColor) {
                                    chartNode.style.backgroundColor = node.backgroundColor;
                                }
                                if (node.borderColor) {
                                    chartNode.style.borderColor = node.borderColor;
                                }
                                
                                // 设置刷新间隔和数据源（如果有）
                                if (node.refreshInterval) {
                                    chartNode.setAttribute('data-refresh-interval', node.refreshInterval);
                                }
                                if (node.dataSource) {
                                    chartNode.setAttribute('data-data-source', node.dataSource);
                                }
                                
                                // 创建图表标题
                                const titleElement = document.createElement('div');
                                titleElement.className = 'chart-title';
                                titleElement.textContent = node.title || '图表';
                                if (node.showTitle === false) {
                                    titleElement.style.display = 'none';
                                }
                                
                                // 创建图表容器
                                const chartContainer = document.createElement('div');
                                chartContainer.className = 'chart-container';
                                
                                // 根据图表类型创建不同的图表
                                switch (node.type) {
                                    case 'line-chart':
                                        chartContainer.innerHTML = '<div class="placeholder-chart">折线图</div>';
                                        break;
                                    case 'bar-chart':
                                        chartContainer.innerHTML = '<div class="placeholder-chart">柱状图</div>';
                                        break;
                                    case 'gauge-chart':
                                        chartContainer.innerHTML = '<div class="placeholder-chart">仪表盘</div>';
                                        break;
                                    default:
                                        chartContainer.innerHTML = '<div class="placeholder-chart">请选择图表类型</div>';
                                }
                                
                                // 创建调整大小的手柄
                                const resizeHandle = document.createElement('div');
                                resizeHandle.className = 'resize-handle';
                                
                                // 添加元素到节点
                                chartNode.appendChild(titleElement);
                                chartNode.appendChild(chartContainer);
                                chartNode.appendChild(resizeHandle);
                                
                                // 添加点击事件（选中节点）
                                chartNode.addEventListener('click', function(event) {
                                    event.stopPropagation();
                                    selectNode(chartNode);
                                });
                                
                                // 添加到画布
                                canvas.appendChild(chartNode);
                                
                                // 使节点可拖动
                                jsPlumbInstance.draggable(node.id, {
                                    grid: [10, 10]
                                });
                                
                                // 添加调整大小的功能
                                if (resizeHandle) {
                                    resizeHandle.addEventListener('mousedown', function(e) {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        
                                        const startX = e.clientX;
                                        const startY = e.clientY;
                                        const startWidth = parseInt(chartNode.style.width) || chartNode.offsetWidth;
                                        const startHeight = parseInt(chartNode.style.height) || chartNode.offsetHeight;
                                        
                                        function handleMouseMove(e) {
                                            const newWidth = startWidth + e.clientX - startX;
                                            const newHeight = startHeight + e.clientY - startY;
                                            
                                            chartNode.style.width = `${Math.max(200, newWidth)}px`;
                                            chartNode.style.height = `${Math.max(150, newHeight)}px`;
                                            
                                            // 重绘jsPlumb连接
                                            jsPlumbInstance.repaintEverything();
                                        }
                                        
                                        function handleMouseUp() {
                                            document.removeEventListener('mousemove', handleMouseMove);
                                            document.removeEventListener('mouseup', handleMouseUp);
                                        }
                                        
                                        document.addEventListener('mousemove', handleMouseMove);
                                        document.addEventListener('mouseup', handleMouseUp);
                                    });
                                }
                            });
                    }
                } catch (error) {
                    console.error('加载组态布局失败:', error);
                    // 不显示错误提示，因为可能是首次使用
                    console.log('可能是首次使用，没有保存的布局');
                }
            }
            
            // 重置画布
            function resetCanvas() {
                // 删除所有连接
                jsPlumbInstance.deleteEveryConnection();
                
                // 删除所有节点和端点
                document.querySelectorAll('.device-node, .text-node, .image-node, .chart-node').forEach(node => {
                    jsPlumbInstance.removeAllEndpoints(node.id);
                    node.remove();
                });
                
                // 重置jsPlumb实例
                jsPlumbInstance.reset();
                
                // 重新初始化jsPlumb实例
                jsPlumbInstance.setContainer("canvas");
                
                // 重新绑定事件监听器
                jsPlumbInstance.bind("click", function(connection, originalEvent) {
                    originalEvent.stopPropagation();
                    
                    // 取消之前选中的节点
                    if (selectedNode) {
                        selectedNode.classList.remove('selected');
                        selectedNode = null;
                    }
                    
                    // 保存选中的连接线
                    selectedConnection = connection;
                    
                    // 启用删除按钮
                    document.getElementById('deleteBtn').disabled = false;
                    
                    // 更新配置面板
                    updateConnectionConfigPanel(connection);
                });
                
                // 重置选中状态
                selectedNode = null;
                selectedConnection = null;
                document.getElementById('deleteBtn').disabled = true;
                
                // 显示无选中项提示
                const deviceConfig = document.getElementById('deviceConfig');
                if (deviceConfig) deviceConfig.style.display = 'none';
                
                const textConfig = document.getElementById('textConfig');
                if (textConfig) textConfig.style.display = 'none';
                
                const connectionConfig = document.getElementById('connectionConfig');
                if (connectionConfig) connectionConfig.style.display = 'none';
                
                const imageConfig = document.getElementById('imageConfig');
                if (imageConfig) imageConfig.style.display = 'none';
                
                const chartConfig = document.getElementById('chartConfig');
                if (chartConfig) chartConfig.style.display = 'none';
                
                const noSelectionConfig = document.getElementById('noSelectionConfig');
                if (noSelectionConfig) noSelectionConfig.style.display = 'block';
                
                // 重新加载设备列表
                loadDevices();
            }
            
            // 更新设备状态
            function updateDeviceStatus(deviceId) {
                // 检查设备ID是否有效
                if (!deviceId || deviceId === 'undefined') {
                    console.error('无法更新设备状态: 无效的设备ID');
                    return;
                }
                
                console.log(`正在获取设备 ${deviceId} 的状态...`);
                
                // 添加超时处理
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('获取设备状态超时')), 5000);
                });
                
                // 实际的fetch请求
                const fetchPromise = fetch(`/api/device/${deviceId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`获取设备状态失败，状态码: ${response.status}`);
                        }
                        return response.json();
                    });
                
                // 使用Promise.race来处理超时
                Promise.race([fetchPromise, timeoutPromise])
                    .then(device => {
                        const nodeId = `node-${device.id}`;
                        const node = document.getElementById(nodeId);
                        if (node) {
                            const statusIndicator = node.querySelector('.status-indicator');
                            const statusText = node.querySelector('.status-text');
                            
                            statusIndicator.className = `status-indicator ${device.connected ? 'connected' : 'disconnected'}`;
                            statusText.textContent = device.connected ? '已连接' : '未连接';
                            
                            console.log(`设备 ${deviceId} 状态更新为: ${device.connected ? '已连接' : '未连接'}`);
                        }
                    })
                    .catch(error => {
                        console.error(`获取设备 ${deviceId} 状态失败:`, error);
                        
                        // 在失败后尝试重新获取（最多重试3次）
                        const retryCount = parseInt(localStorage.getItem(`retry_${deviceId}`) || '0');
                        if (retryCount < 3) {
                            localStorage.setItem(`retry_${deviceId}`, (retryCount + 1).toString());
                            console.log(`正在重试获取设备 ${deviceId} 状态，第 ${retryCount + 1} 次尝试...`);
                            setTimeout(() => updateDeviceStatus(deviceId), 2000);
                        } else {
                            localStorage.removeItem(`retry_${deviceId}`);
                            console.error(`获取设备 ${deviceId} 状态失败，已达到最大重试次数`);
                            
                            // 显示错误状态
                            const nodeId = `node-${deviceId}`;
                            const node = document.getElementById(nodeId);
                            if (node) {
                                const statusIndicator = node.querySelector('.status-indicator');
                                const statusText = node.querySelector('.status-text');
                                
                                statusIndicator.className = 'status-indicator disconnected';
                                statusText.textContent = '状态获取失败';
                            }
                        }
                    });
            }
            
            // 设置WebSocket连接，接收设备状态更新
            function setupWebSocket() {
                console.log('正在初始化WebSocket连接...');
                
                // 确保SockJS和STOMP库已加载
                if (typeof SockJS === 'undefined' || typeof Stomp === 'undefined') {
                    console.log('SockJS或STOMP库尚未加载，稍后重试...');
                    setTimeout(setupWebSocket, 1000);
                    return;
                }
                
                try {
                    const socket = new SockJS('/ws');
                    const stompClient = Stomp.over(socket);
                    
                    // 禁用STOMP调试日志
                    stompClient.debug = null;
                    
                    // 连接参数
                    const connectHeaders = {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    };
                    
                    // 连接超时时间
                    const connectTimeout = setTimeout(function() {
                        console.log('WebSocket连接超时，正在重试...');
                        socket.close();
                        setTimeout(setupWebSocket, 3000);
                    }, 10000);
                    
                    stompClient.connect(connectHeaders, function(frame) {
                        clearTimeout(connectTimeout);
                        console.log('WebSocket连接成功');
                        
                        // 订阅设备状态主题
                        stompClient.subscribe('/topic/devices', function(message) {
                            try {
                                const devices = JSON.parse(message.body);
                                
                                // 更新设备状态
                                devices.forEach(device => {
                                    const nodeId = `node-${device.id}`;
                                    const node = document.getElementById(nodeId);
                                    if (node) {
                                        const statusIndicator = node.querySelector('.status-indicator');
                                        const statusText = node.querySelector('.status-text');
                                        
                                        statusIndicator.className = `status-indicator ${device.connected ? 'connected' : 'disconnected'}`;
                                        statusText.textContent = device.connected ? '已连接' : '未连接';
                                    }
                                });
                            } catch (error) {
                                console.error('处理WebSocket消息失败:', error);
                            }
                        });
                    }, function(error) {
                        clearTimeout(connectTimeout);
                        console.error('WebSocket连接失败:', error);
                        // 连接失败后，尝试重新连接
                        setTimeout(setupWebSocket, 5000);
                    });
                    
                    // 处理连接断开的情况
                    socket.onclose = function() {
                        console.log('WebSocket连接已关闭，正在重新连接...');
                        // 尝试重新连接
                        setTimeout(setupWebSocket, 5000);
                    };
                } catch (error) {
                    console.error('初始化WebSocket时发生错误:', error);
                    setTimeout(setupWebSocket, 5000);
                }
            }
            
            // 加载SockJS和STOMP库
            function loadScripts() {
                console.log('正在加载WebSocket相关库...');
                
                // 检查是否已加载
                if (document.querySelector('script[src="/js/lib/sockjs.min.js"]') && 
                    document.querySelector('script[src="/js/lib/stomp.min.js"]')) {
                    console.log('WebSocket相关库已加载，直接初始化连接');
                    // 延迟初始化WebSocket，确保页面完全加载
                    setTimeout(setupWebSocket, 1000);
                    return;
                }
                
                // 加载SockJS
                const sockjsScript = document.createElement('script');
                sockjsScript.src = '/js/lib/sockjs.min.js';
                sockjsScript.onload = function() {
                    console.log('SockJS库加载完成');
                    
                    // 加载STOMP
                    const stompScript = document.createElement('script');
                    stompScript.src = '/js/lib/stomp.min.js';
                    stompScript.onload = function() {
                        console.log('STOMP库加载完成');
                        // 延迟初始化WebSocket，确保页面完全加载
                        setTimeout(setupWebSocket, 1000);
                    };
                    stompScript.onerror = function() {
                        console.error('加载STOMP库失败');
                    };
                    document.head.appendChild(stompScript);
                };
                sockjsScript.onerror = function() {
                    console.error('加载SockJS库失败');
                };
                document.head.appendChild(sockjsScript);
            }
            
            // 初始化文本组件拖拽
            function initTextComponents() {
                const textComponents = document.querySelectorAll('.text-component');
                textComponents.forEach(component => {
                    component.addEventListener('dragstart', handleTextDragStart);
                });
            }
            
            // 处理文本组件拖拽开始事件
            function handleTextDragStart(event) {
                const componentType = event.target.getAttribute('data-component-type');
                event.dataTransfer.setData('text/plain', JSON.stringify({
                    type: componentType,
                    text: '文本'
                }));
            }
            
            // 初始化图片和图表组件拖拽
            function initImageAndChartComponents() {
                // 初始化图片组件拖拽
                const imageComponents = document.querySelectorAll('.image-component');
                imageComponents.forEach(component => {
                    component.addEventListener('dragstart', handleImageDragStart);
                });
                
                // 初始化图表组件拖拽
                const chartComponents = document.querySelectorAll('.chart-component');
                chartComponents.forEach(component => {
                    component.addEventListener('dragstart', handleChartDragStart);
                });
            }
            
            // 修改为只初始化图片组件
            function initImageComponents() {
                // 初始化图片组件拖拽
                const imageComponents = document.querySelectorAll('.image-component');
                imageComponents.forEach(component => {
                    component.addEventListener('dragstart', handleImageDragStart);
                });
                
                // 初始化图片上传
                document.getElementById('imageUpload').addEventListener('change', handleImageUpload);
            }
            
            // 处理图片上传
            function handleImageUpload(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件');
                    return;
                }
                
                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);
                
                // 上传图片到服务器
                fetch('/api/upload/image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '上传失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    // 创建临时图片组件
                    const imageComponent = document.querySelector('.image-component');
                    
                    // 触发拖拽事件，传递图片数据
                    const dragEvent = new DragEvent('dragstart', {
                        bubbles: true,
                        cancelable: true,
                        dataTransfer: new DataTransfer()
                    });
                    
                    // 设置拖拽数据
                    const imageData = {
                        type: 'image',
                        imageUrl: data.url,
                        title: file.name
                    };
                    
                    // 手动创建图片节点
                    const canvasRect = canvas.getBoundingClientRect();
                    const x = canvasRect.width / 2 - 100;
                    const y = canvasRect.height / 2 - 75;
                    
                    createImageNode(imageData, x, y);
                })
                .catch(error => {
                    alert('图片上传失败: ' + error.message);
                })
                .finally(() => {
                    // 清空文件输入框
                    event.target.value = '';
                });
            }
            
            // 图片缩放滑动条事件
            document.getElementById('imageScale').addEventListener('input', function(event) {
                const scale = parseFloat(event.target.value);
                document.getElementById('scaleValue').textContent = scale.toFixed(1);
                
                // 不再更新预览图片的缩放，保持正常大小
            });
            
            // 加载布局列表
            async function loadLayoutList() {
                try {
                    const response = await fetch('/api/topologies');
                    if (!response.ok) {
                        throw new Error('获取布局列表失败');
                    }
                    
                    const layouts = await response.json();
                    const layoutSelector = document.getElementById('layoutSelector');
                    
                    // 清空现有选项（保留第一个默认选项）
                    while (layoutSelector.options.length > 1) {
                        layoutSelector.remove(1);
                    }
                    
                    // 添加布局选项
                    layouts.forEach(layout => {
                        const option = document.createElement('option');
                        option.value = layout.name;
                        option.textContent = layout.name;
                        layoutSelector.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载布局列表失败:', error);
                }
            }
            
            // 布局选择器变更事件
            document.getElementById('layoutSelector').addEventListener('change', async function(event) {
                const layoutName = event.target.value;
                document.getElementById('deleteLayoutBtn').disabled = !layoutName;
                document.getElementById('previewLayoutBtn').disabled = !layoutName;
                if (!layoutName) return;
                
                try {
                    const response = await fetch(`/topology/load/${layoutName}`);
                    if (!response.ok) {
                        throw new Error('加载布局失败');
                    }
                    
                    const topology = await response.json();
                    
                    // 设置布局名称
                    document.getElementById('layoutName').value = topology.name || layoutName;
                    
                    // 清空画布
                    resetCanvas();
                    
                    // 应用画布配置（如果有）
                    if (topology.canvasConfig) {
                        const canvas = document.getElementById('canvas');
                        const config = topology.canvasConfig;
                        
                        // 设置画布尺寸
                        if (config.width) {
                            canvas.style.width = `${config.width}px`;
                            document.getElementById('canvasWidth').value = config.width;
                        }
                        
                        if (config.height) {
                            canvas.style.height = `${config.height}px`;
                            document.getElementById('canvasHeight').value = config.height;
                        }
                        
                        // 设置背景颜色
                        if (config.backgroundColor) {
                            canvas.style.backgroundColor = config.backgroundColor;
                            document.getElementById('canvasBackgroundColor').value = config.backgroundColor;
                        }
                        
                        // 设置网格
                        document.getElementById('showGrid').checked = config.showGrid !== false;
                        
                        if (config.gridSize) {
                            document.getElementById('gridSize').value = config.gridSize;
                        }
                        
                        if (config.gridColor) {
                            document.getElementById('gridColor').value = config.gridColor;
                        }
                        
                        // 应用网格样式
                        if (config.showGrid !== false) {
                            canvas.style.backgroundImage = `
                                linear-gradient(${config.gridColor || '#e9ecef'} 1px, transparent 1px),
                                linear-gradient(90deg, ${config.gridColor || '#e9ecef'} 1px, transparent 1px)
                            `;
                            canvas.style.backgroundSize = `${config.gridSize || 20}px ${config.gridSize || 20}px`;
                        } else {
                            canvas.style.backgroundImage = 'none';
                        }
                    }
                    
                    // 创建设备节点
                    if (topology.nodes && topology.nodes.length > 0) {
                        // 先获取设备信息
                        const devicesResponse = await fetch('/api/devices');
                        if (!devicesResponse.ok) {
                            throw new Error('获取设备列表失败');
                        }
                        const devices = await devicesResponse.json();
                        const deviceMap = new Map();
                        devices.forEach(device => {
                            deviceMap.set(device.id, device);
                        });
                        
                        // 创建设备节点
                        topology.nodes.forEach(node => {
                            // 检查设备ID是否有效
                            if (!node.deviceId || node.deviceId === 'undefined') {
                                console.warn(`跳过无效的设备节点: ${JSON.stringify(node)}`);
                                return;
                            }
                            
                            // 获取设备信息
                            const device = {
                                id: node.deviceId,
                                name: node.name || '未命名设备',
                                address: node.address || 'localhost',
                                port: node.port || '0',
                                imageUrl: node.imageUrl || null,
                                imageScale: node.imageScale || 1.0,
                                imageOffsetX: node.imageOffsetX || 0,
                                imageOffsetY: node.imageOffsetY || 0,
                                alertItems: node.alertItems || []
                            };
                            
                            // 创建节点
                            const deviceNode = createDeviceNode(device, node.x, node.y);
                            
                            // 应用保存的样式（如果有）
                            if (deviceNode) {
                                if (node.backgroundColor) {
                                    deviceNode.style.backgroundColor = node.backgroundColor;
                                }
                                if (node.borderColor) {
                                    deviceNode.style.borderColor = node.borderColor;
                                }
                                
                                // 应用保存的宽高
                                if (node.width) {
                                    deviceNode.style.width = `${node.width}px`;
                                }
                                if (node.height) {
                                    deviceNode.style.height = `${node.height}px`;
                                }
                            }
                        });
                        
                        // 等待所有节点渲染完成后更新端点位置
                        setTimeout(() => {
                            document.querySelectorAll('.device-node').forEach(node => {
                                // 重新验证节点以更新端点位置
                                jsPlumbInstance.revalidate(node.id);
                            });
                            jsPlumbInstance.repaintEverything();
                        }, 100);
                    }
                        
                        // 创建文本节点
                        if (topology.textNodes && topology.textNodes.length > 0) {
                            topology.textNodes.forEach(node => {
                                const textNode = document.createElement('div');
                                textNode.id = node.id;
                                textNode.className = 'text-node';
                                if (node.showBorder === false) {
                                    textNode.classList.add('no-border');
                                }
                                textNode.setAttribute('data-component-type', 'text');
                                textNode.style.left = `${node.x}px`;
                                textNode.style.top = `${node.y}px`;
                                
                                // 应用保存的宽高
                                textNode.style.width = node.width ? `${node.width}px` : '100px';
                                textNode.style.height = node.height ? `${node.height}px` : '40px';
                                
                                const textElement = document.createElement('div');
                                textElement.className = 'editable';
                                textElement.contentEditable = 'true';
                                textElement.textContent = node.text;
                                
                                // 应用文本样式
                                if (node.fontSize) {
                                    textElement.style.fontSize = `${node.fontSize}px`;
                                }
                                if (node.fontColor) {
                                    textElement.style.color = node.fontColor;
                                }
                                if (node.fontWeight) {
                                    textElement.style.fontWeight = node.fontWeight;
                                }
                                if (node.textAlign) {
                                    textElement.style.textAlign = node.textAlign;
                                }
                                
                                // 创建调整大小的手柄
                                const resizeHandle = document.createElement('div');
                                resizeHandle.className = 'resize-handle';
                                
                                // 添加调整大小的事件处理
                                resizeHandle.addEventListener('mousedown', function(e) {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    
                                    // 记录初始位置和大小
                                    const startX = e.clientX;
                                    const startY = e.clientY;
                                    const startWidth = textNode.offsetWidth;
                                    const startHeight = textNode.offsetHeight;
                                    
                                    // 创建调整大小的函数
                                    function resize(e) {
                                        // 计算新的宽度和高度
                                        const newWidth = Math.max(100, startWidth + e.clientX - startX);
                                        const newHeight = Math.max(40, startHeight + e.clientY - startY);
                                        
                                        // 应用新的宽度和高度
                                        textNode.style.width = `${newWidth}px`;
                                        textNode.style.height = `${newHeight}px`;
                                        
                                        // 如果当前节点被选中，更新配置面板中的尺寸值
                                        if (textNode.classList.contains('selected')) {
                                            document.getElementById('textWidth').value = newWidth;
                                            document.getElementById('textHeight').value = newHeight;
                                        }
                                    }
                                    
                                    // 创建停止调整大小的函数
                                    function stopResize() {
                                        document.removeEventListener('mousemove', resize);
                                        document.removeEventListener('mouseup', stopResize);
                                    }
                                    
                                    // 添加事件监听器
                                    document.addEventListener('mousemove', resize);
                                    document.addEventListener('mouseup', stopResize);
                                });
                                
                                // 添加元素到节点
                                textNode.appendChild(textElement);
                                textNode.appendChild(resizeHandle);
                                
                                // 添加点击事件（选中节点）
                                textNode.addEventListener('click', function(event) {
                                    event.stopPropagation();
                                    selectNode(textNode);
                                });
                                
                                // 添加双击事件（编辑文本）
                                textElement.addEventListener('dblclick', function(event) {
                                    event.stopPropagation();
                                    this.focus();
                                });
                                
                                // 添加到画布
                                canvas.appendChild(textNode);
                                
                                // 使节点可拖动
                                jsPlumbInstance.draggable(node.id, {
                                    grid: [10, 10]
                                });
                            });
                        }
                        
                    // 创建连接
                    if (topology.connections && topology.connections.length > 0) {
                        // 延迟创建连接，确保节点和端点都已就绪
                        setTimeout(() => {
                            topology.connections.forEach(conn => {
                                try {
                                    let connection = jsPlumbInstance.connect({
                                        uuids: [conn.sourceEndpointUuid, conn.targetEndpointUuid]
                                    });
                                    
                                    if (connection) {
                                        // 应用线条样式
                                        connection.setPaintStyle({
                                            stroke: conn.strokeColor,
                                            strokeWidth: conn.strokeWidth,
                                            strokeDasharray: conn.strokeDasharray
                                        });
                                        
                                        // 应用动画效果
                                        if (conn.hasAnimation) {
                                            connection.hasAnimation = conn.hasAnimation;
                                            connection.isReverseFlow = conn.isReverseFlow;
                                            connection.animationSpeed = conn.animationSpeed;
                                            
                                            // 获取连接线的SVG容器元素
                                            const connector = connection.connector.canvas;
                                            if (connector) {
                                                // 添加动画类
                                                connector.classList.add('animated-connection');
                                                connector.classList.add('animation-' + conn.animationSpeed);
                                                
                                                // 根据保存的设置应用反向流动
                                                if (conn.isReverseFlow) {
                                                    connector.classList.add('reverse-flow');
                                                }
                                            }
                                        }
                                    }
                                } catch (error) {
                                    console.error('创建连接失败:', error);
                                }
                            });
                            
                            // 最后再次重绘
                                            jsPlumbInstance.repaintEverything();
                        }, 200);
                        }
                        
                        // 创建图片节点
                        if (topology.imageNodes && topology.imageNodes.length > 0) {
                            topology.imageNodes.forEach(node => {
                                const imageNode = document.createElement('div');
                                imageNode.id = node.id;
                                imageNode.className = node.className || 'image-node';
                                imageNode.setAttribute('data-component-type', 'image');
                                imageNode.style.left = `${node.x}px`;
                                imageNode.style.top = `${node.y}px`;
                                
                                // 应用保存的宽高
                                if (node.width) {
                                    imageNode.style.width = `${node.width}px`;
                                }
                                if (node.height) {
                                    imageNode.style.height = `${node.height}px`;
                                }
                                
                                // 应用背景和边框颜色
                                if (node.backgroundColor) {
                                    imageNode.style.backgroundColor = node.backgroundColor;
                                }
                                if (node.borderColor) {
                                    imageNode.style.borderColor = node.borderColor;
                                }
                                
                                // 创建图片标题
                                const titleElement = document.createElement('div');
                                titleElement.className = 'image-title';
                                titleElement.textContent = node.title || '图片';
                                if (node.showTitle === false) {
                                    titleElement.style.display = 'none';
                                }
                                
                                // 创建图片容器
                                const imageContainer = document.createElement('div');
                                imageContainer.className = 'image-container';
                                
                                // 如果有图片URL，创建图片元素
                                if (node.imageUrl) {
                                    const imageElement = document.createElement('img');
                                    imageElement.src = node.imageUrl;
                                    imageElement.alt = node.title || '图片';
                                    
                                    // 设置图片缩放
                                    if (node.imageScale) {
                                        imageElement.style.transform = `scale(${node.imageScale})`;
                                    }
                                    
                                    imageContainer.appendChild(imageElement);
                                } else {
                                    // 没有图片URL，显示提示
                                    imageContainer.innerHTML = '<div class="no-image">请添加图片</div>';
                                }
                                
                                // 添加元素到节点
                                imageNode.appendChild(titleElement);
                                imageNode.appendChild(imageContainer);
                                
                                // 创建调整大小的手柄
                                const resizeHandle = document.createElement('div');
                                resizeHandle.className = 'resize-handle';
                                imageNode.appendChild(resizeHandle);
                                
                                // 添加调整大小的事件处理
                                if (resizeHandle) {
                                    resizeHandle.addEventListener('mousedown', function(e) {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        
                                        // 记录初始位置和大小
                                        const startX = e.clientX;
                                        const startY = e.clientY;
                                        const startWidth = imageNode.offsetWidth;
                                        const startHeight = imageNode.offsetHeight;
                                        
                                        // 创建调整大小的函数
                                        function resize(e) {
                                            // 计算新的宽度和高度
                                            const newWidth = Math.max(150, startWidth + e.clientX - startX);
                                            const newHeight = Math.max(100, startHeight + e.clientY - startY);
                                            
                                            // 应用新的宽度和高度
                                            imageNode.style.width = `${newWidth}px`;
                                            imageNode.style.height = `${newHeight}px`;
                                            
                                            // 如果当前节点被选中，更新配置面板中的尺寸值
                                            if (imageNode.classList.contains('selected')) {
                                                document.getElementById('imageWidth').value = newWidth;
                                                document.getElementById('imageHeight').value = newHeight;
                                            }
                                        }
                                        
                                        // 创建停止调整大小的函数
                                        function stopResize() {
                                            document.removeEventListener('mousemove', resize);
                                            document.removeEventListener('mouseup', stopResize);
                                        }
                                        
                                        // 添加事件监听器
                                        document.addEventListener('mousemove', resize);
                                        document.addEventListener('mouseup', stopResize);
                                    });
                                }
                                
                                // 添加点击事件（选中节点）
                                imageNode.addEventListener('click', function(event) {
                                    event.stopPropagation();
                                    selectNode(imageNode);
                                });
                                
                                // 添加到画布
                                canvas.appendChild(imageNode);
                                
                                // 使节点可拖动
                                jsPlumbInstance.draggable(node.id, {
                                    grid: [10, 10]
                                });
                            });
                        }
                        
                        // 创建图表节点
                        if (topology.chartNodes && topology.chartNodes.length > 0) {
                            topology.chartNodes.forEach(node => {
                                const chartNode = document.createElement('div');
                                chartNode.id = node.id;
                                chartNode.className = 'chart-node';
                                chartNode.setAttribute('data-component-type', node.type);
                                chartNode.style.left = `${node.x}px`;
                                chartNode.style.top = `${node.y}px`;
                                
                                // 应用保存的宽高
                                if (node.width) {
                                    chartNode.style.width = `${node.width}px`;
                                }
                                if (node.height) {
                                    chartNode.style.height = `${node.height}px`;
                                }
                                
                                // 应用背景和边框颜色
                                if (node.backgroundColor) {
                                    chartNode.style.backgroundColor = node.backgroundColor;
                                }
                                if (node.borderColor) {
                                    chartNode.style.borderColor = node.borderColor;
                                }
                                
                                // 设置刷新间隔和数据源（如果有）
                                if (node.refreshInterval) {
                                    chartNode.setAttribute('data-refresh-interval', node.refreshInterval);
                                }
                                if (node.dataSource) {
                                    chartNode.setAttribute('data-data-source', node.dataSource);
                                }
                                
                                // 创建图表标题
                                const titleElement = document.createElement('div');
                                titleElement.className = 'chart-title';
                                titleElement.textContent = node.title || '图表';
                                if (node.showTitle === false) {
                                    titleElement.style.display = 'none';
                                }
                                
                                // 创建图表容器
                                const chartContainer = document.createElement('div');
                                chartContainer.className = 'chart-container';
                                
                                // 根据图表类型创建不同的图表
                                switch (node.type) {
                                    case 'line-chart':
                                        chartContainer.innerHTML = '<div class="placeholder-chart">折线图</div>';
                                        break;
                                    case 'bar-chart':
                                        chartContainer.innerHTML = '<div class="placeholder-chart">柱状图</div>';
                                        break;
                                    case 'gauge-chart':
                                        chartContainer.innerHTML = '<div class="placeholder-chart">仪表盘</div>';
                                        break;
                                    default:
                                        chartContainer.innerHTML = '<div class="placeholder-chart">请选择图表类型</div>';
                                }
                                
                                // 创建调整大小的手柄
                                const resizeHandle = document.createElement('div');
                                resizeHandle.className = 'resize-handle';
                                
                                // 添加元素到节点
                                chartNode.appendChild(titleElement);
                                chartNode.appendChild(chartContainer);
                                chartNode.appendChild(resizeHandle);
                                
                                // 添加点击事件（选中节点）
                                chartNode.addEventListener('click', function(event) {
                                    event.stopPropagation();
                                    selectNode(chartNode);
                                });
                                
                                // 添加到画布
                                canvas.appendChild(chartNode);
                                
                                // 使节点可拖动
                                jsPlumbInstance.draggable(node.id, {
                                    grid: [10, 10]
                                });
                                
                                // 添加调整大小的功能
                                if (resizeHandle) {
                                    resizeHandle.addEventListener('mousedown', function(e) {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        
                                        const startX = e.clientX;
                                        const startY = e.clientY;
                                        const startWidth = parseInt(chartNode.style.width) || chartNode.offsetWidth;
                                        const startHeight = parseInt(chartNode.style.height) || chartNode.offsetHeight;
                                        
                                        function handleMouseMove(e) {
                                            const newWidth = startWidth + e.clientX - startX;
                                            const newHeight = startHeight + e.clientY - startY;
                                            
                                            chartNode.style.width = `${Math.max(200, newWidth)}px`;
                                            chartNode.style.height = `${Math.max(150, newHeight)}px`;
                                            
                                            // 重绘jsPlumb连接
                                            jsPlumbInstance.repaintEverything();
                                        }
                                        
                                        function handleMouseUp() {
                                            document.removeEventListener('mousemove', handleMouseMove);
                                            document.removeEventListener('mouseup', handleMouseUp);
                                        }
                                        
                                        document.addEventListener('mousemove', handleMouseMove);
                                        document.addEventListener('mouseup', handleMouseUp);
                                    });
                                }
                            });
                    }
                } catch (error) {
                    console.error('加载布局失败:', error);
                    alert('加载布局失败: ' + error.message);
                }
            });
            
            // 页面加载完成后，加载布局列表
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化设备拖拽
                initDeviceDragAndDrop();
                
                // 初始化文本组件拖拽
                initTextComponents();
                
                // 初始化图片组件拖拽
                initImageComponents();
                
                // 加载布局列表
                loadLayoutList();
            });
            
            // 加载设备的预警配置
            async function loadDeviceAlerts(deviceId) {
                try {
                    // 清空预警选择器
                    const alertSelector = document.getElementById('alertSelector');
                    alertSelector.innerHTML = '<option value="">选择预警配置</option>';
                    
                    // 清空已添加的预警项表格
                    const alertItemsTable = document.getElementById('alertItemsTable');
                    alertItemsTable.innerHTML = '';
                    
                    // 获取设备的预警配置
                    const response = await fetch(`/api/alerts/device/${deviceId}`);
                    if (!response.ok) {
                        throw new Error('获取预警配置失败');
                    }
                    
                    const alerts = await response.json();
                    
                    // 填充预警选择器
                    alerts.forEach(alert => {
                        const option = document.createElement('option');
                        option.value = alert.id;
                        option.textContent = alert.name;
                        option.dataset.dataItemName = alert.dataItem.name;
                        alertSelector.appendChild(option);
                    });
                    
                    // 获取已添加到节点的预警项
                    const nodeElement = document.getElementById(`node-${deviceId}`);
                    if (nodeElement) {
                        const alertStatusContainer = nodeElement.querySelector('.alert-status-container');
                        const alertItems = alertStatusContainer.querySelectorAll('.alert-status-item');
                        
                        // 填充已添加的预警项表格
                        alertItems.forEach(item => {
                            const alertId = item.dataset.alertId;
                            const alertName = item.dataset.alertName;
                            
                            addAlertToTable(alertId, alertName);
                        });
                    }
                } catch (error) {
                    console.error('加载预警配置失败:', error);
                }
            }
            
            // 添加预警项到表格
            function addAlertToTable(alertId, alertName) {
                const alertItemsTable = document.getElementById('alertItemsTable');
                
                // 检查是否已存在
                if (alertItemsTable.querySelector(`tr[data-alert-id="${alertId}"]`)) {
                    return;
                }
                
                const row = document.createElement('tr');
                row.dataset.alertId = alertId;
                row.innerHTML = `
                    <td>${alertName}</td>
                    <td><span class="badge badge-secondary">未知</span></td>
                    <td>
                        <button class="btn btn-sm btn-danger remove-alert-btn">删除</button>
                    </td>
                `;
                
                // 添加删除按钮事件
                row.querySelector('.remove-alert-btn').addEventListener('click', function() {
                    // 从表格中移除
                    row.remove();
                    
                    // 从节点中移除
                    if (selectedNode) {
                        const deviceId = selectedNode.getAttribute('data-device-id');
                        const alertStatusContainer = selectedNode.querySelector('.alert-status-container');
                        const alertItem = alertStatusContainer.querySelector(`.alert-status-item[data-alert-id="${alertId}"]`);
                        if (alertItem) {
                            alertItem.remove();
                        }
                    }
                });
                
                alertItemsTable.appendChild(row);
            }
            
            // 添加预警按钮点击事件
            document.getElementById('addAlertBtn').addEventListener('click', function() {
                if (!selectedNode) return;
                
                const alertSelector = document.getElementById('alertSelector');
                const alertId = alertSelector.value;
                
                if (!alertId) {
                    alert('请选择预警配置');
                    return;
                }
                
                const alertName = alertSelector.options[alertSelector.selectedIndex].text;
                
                // 添加到表格
                addAlertToTable(alertId, alertName);
                
                // 添加到节点
                const deviceId = selectedNode.getAttribute('data-device-id');
                const alertStatusContainer = selectedNode.querySelector('.alert-status-container');
                
                // 检查是否已存在
                if (alertStatusContainer.querySelector(`.alert-status-item[data-alert-id="${alertId}"]`)) {
                    return;
                }
                
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-status-item';
                alertItem.dataset.alertId = alertId;
                alertItem.dataset.alertName = alertName;
                
                // 创建名称元素
                const nameSpan = document.createElement('span');
                nameSpan.className = 'alert-name';
                nameSpan.textContent = alertName + ':';
                alertItem.appendChild(nameSpan);
                
                // 创建状态徽章元素
                const badgeSpan = document.createElement('span');
                badgeSpan.className = 'badge badge-secondary';
                badgeSpan.textContent = '未知';
                alertItem.appendChild(badgeSpan);
                
                alertStatusContainer.appendChild(alertItem);
                
                console.log(`添加预警项到节点，HTML: ${alertItem.outerHTML}`);
                
                // 自动调整节点高度
                adjustNodeHeight(selectedNode);
                
                // 开始更新预警状态
                updateAlertStatus(alertId, deviceId);
            });
            
            // 自动调整节点高度
            function adjustNodeHeight(node) {
                if (!node) return;
                
                // 获取节点内容的实际高度
                const title = node.querySelector('.title');
                const status = node.querySelector('.status');
                const alertContainer = node.querySelector('.alert-status-container');
                const deviceImage = node.querySelector('.device-image');
                
                let contentHeight = 0;
                
                if (title) contentHeight += title.offsetHeight + 5; // 标题高度 + 下边距
                if (status) contentHeight += status.offsetHeight + 5; // 状态高度 + 下边距
                if (alertContainer) {
                    // 计算预警容器的实际内容高度
                    let alertContainerHeight = 0;
                    const alertItems = alertContainer.querySelectorAll('.alert-status-item');
                    alertItems.forEach(item => {
                        alertContainerHeight += item.offsetHeight + 2; // 每个预警项高度 + 下边距
                    });
                    
                    // 设置预警容器的高度，最大80px，超出显示滚动条
                    if (alertContainerHeight > 0) {
                        alertContainer.style.height = `${Math.min(alertContainerHeight + 8, 80)}px`;
                        contentHeight += Math.min(alertContainerHeight + 8, 80) + 5; // 预警容器高度 + 下边距
                    }
                }
                if (deviceImage) contentHeight += deviceImage.offsetHeight;
                
                // 添加内边距
                contentHeight += 20; // 上下内边距各10px
                
                // 设置最小高度
                const minHeight = 100;
                const newHeight = Math.max(contentHeight, minHeight);
                
                // 如果节点没有明确设置高度或高度小于内容高度，则自动调整
                const currentHeight = parseInt(node.style.height) || 0;
                if (currentHeight < newHeight) {
                    node.style.height = `${newHeight}px`;
                    console.log(`调整节点高度为: ${newHeight}px`);
                    
                    // 重绘jsPlumb连接
                    if (typeof jsPlumbInstance !== 'undefined') {
                        jsPlumbInstance.repaintEverything();
                    }
                }
            }
            
            // 更新预警状态
            async function updateAlertStatus(alertId, deviceId) {
                try {
                    console.log(`正在获取预警 ${alertId} 的状态...`);
                    
                    // 首先检查设备是否连接
                    const deviceResponse = await fetch(`/api/device/${deviceId}`);
                    if (!deviceResponse.ok) {
                        throw new Error(`获取设备状态失败，状态码: ${deviceResponse.status}`);
                    }
                    
                    const deviceData = await deviceResponse.json();
                    console.log(`设备 ${deviceId} 连接状态:`, deviceData.connected);
                    
                    // 获取状态徽章HTML
                    function getStatusBadge(status) {
                        console.log(`生成状态徽章，状态值: ${status}, 类型: ${typeof status}`);
                        // 确保状态是数字
                        const statusNum = parseInt(status);
                        switch (statusNum) {
                            case 1:
                                return '<span class="badge badge-success">正常</span>';
                            case 2:
                                return '<span class="badge badge-warning">预警</span>';
                            case 3:
                                return '<span class="badge badge-danger">异常</span>';
                            default:
                                return '<span class="badge badge-secondary">未知</span>';
                        }
                    }
                    
                    let statusData = { status: 0 }; // 默认为未知状态
                    
                    // 只有在设备连接时，才获取实际的预警状态
                    if (deviceData.connected) {
                        // 添加超时处理
                        const timeoutPromise = new Promise((_, reject) => {
                            setTimeout(() => reject(new Error('获取预警状态超时')), 5000);
                        });
                        
                        // 实际的fetch请求
                        const fetchPromise = fetch(`/api/alerts/${alertId}/status`)
                            .then(response => {
                                console.log(`预警 ${alertId} 状态响应状态码: ${response.status}`);
                                if (!response.ok) {
                                    throw new Error(`获取预警状态失败，状态码: ${response.status}`);
                                }
                                return response.json();
                            });
                        
                        try {
                            // 使用Promise.race来处理超时
                            statusData = await Promise.race([fetchPromise, timeoutPromise]);
                            console.log(`预警 ${alertId} 状态数据:`, JSON.stringify(statusData));
                            
                            // 检查数据格式
                            if (!statusData || typeof statusData.status === 'undefined') {
                                console.error(`预警 ${alertId} 状态数据格式不正确:`, statusData);
                                statusData = { status: 0 }; // 设置为未知状态
                            }
                        } catch (error) {
                            console.error(`获取预警 ${alertId} 状态失败:`, error);
                            statusData = { status: 0 }; // 设置为未知状态
                        }
                    } else {
                        console.log(`设备 ${deviceId} 未连接，预警 ${alertId} 状态设置为未知`);
                    }
                    
                    // 更新节点中的预警状态
                    const nodeElement = document.getElementById(`node-${deviceId}`);
                    if (nodeElement) {
                        console.log(`找到节点元素: node-${deviceId}`);
                        const alertItem = nodeElement.querySelector(`.alert-status-item[data-alert-id="${alertId}"]`);
                        if (alertItem) {
                            console.log(`找到预警项元素: ${alertItem.outerHTML}`);
                            
                            // 查找徽章元素 - 尝试多种选择器
                            let badgeElement = alertItem.querySelector('.badge');
                            if (!badgeElement) {
                                badgeElement = alertItem.querySelector('span:last-child');
                            }
                            
                            if (badgeElement) {
                                console.log(`找到徽章元素，当前HTML: ${badgeElement.outerHTML}`);
                                const newBadgeHtml = getStatusBadge(statusData.status);
                                console.log(`新徽章HTML: ${newBadgeHtml}`);
                                badgeElement.outerHTML = newBadgeHtml;
                            } else {
                                console.log(`未找到徽章元素，尝试查找最后一个子元素`);
                                // 如果找不到徽章元素，尝试替换最后一个子元素
                                const lastChild = alertItem.lastElementChild;
                                if (lastChild) {
                                    console.log(`找到最后一个子元素: ${lastChild.outerHTML}`);
                                    lastChild.outerHTML = getStatusBadge(statusData.status);
                                } else {
                                    console.error(`无法找到徽章元素或最后一个子元素`);
                                    // 如果实在找不到，直接添加一个新的徽章
                                    alertItem.innerHTML += getStatusBadge(statusData.status);
                                }
                            }
                        } else {
                            console.error(`未找到预警项元素: alert-status-item[data-alert-id="${alertId}"]`);
                        }
                    } else {
                        console.error(`未找到节点元素: node-${deviceId}`);
                    }
                    
                    // 更新表格中的预警状态
                    if (selectedNode && selectedNode.getAttribute('data-device-id') === deviceId) {
                        const alertItemsTable = document.getElementById('alertItemsTable');
                        if (alertItemsTable) {
                            const row = alertItemsTable.querySelector(`tr[data-alert-id="${alertId}"]`);
                            if (row) {
                                console.log(`找到表格行: ${row.outerHTML}`);
                                const statusCell = row.querySelector('td:nth-child(2)');
                                if (statusCell) {
                                    console.log(`找到状态单元格: ${statusCell.outerHTML}`);
                                    statusCell.innerHTML = getStatusBadge(statusData.status);
                                } else {
                                    console.error(`未找到状态单元格`);
                                }
                            } else {
                                console.error(`未找到表格行: tr[data-alert-id="${alertId}"]`);
                            }
                        } else {
                            console.error(`未找到表格: alertItemsTable`);
                        }
                    }
                    
                    // 3秒后再次更新
                    setTimeout(() => updateAlertStatus(alertId, deviceId), 3000);
                } catch (error) {
                    console.error(`更新预警 ${alertId} 状态失败:`, error);
                    // 发生错误时，10秒后重试
                    setTimeout(() => updateAlertStatus(alertId, deviceId), 10000);
                }
            }
            
            // 添加透明度滑块的事件监听器
            document.getElementById('deviceBgOpacity').addEventListener('input', function(event) {
                document.getElementById('bgOpacityValue').textContent = event.target.value;
            });
            
            document.getElementById('deviceBorderOpacity').addEventListener('input', function(event) {
                document.getElementById('borderOpacityValue').textContent = event.target.value;
            });
            
            // 辅助函数：将HEX颜色转换为RGBA
            function hexToRgba(hex, alpha) {
                // 移除#号（如果有）
                hex = hex.replace('#', '');
                
                // 解析RGB值
                let r, g, b;
                if (hex.length === 3) {
                    // 简写形式 (#RGB)
                    r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
                    g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
                    b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
                } else {
                    // 完整形式 (#RRGGBB)
                    r = parseInt(hex.substring(0, 2), 16);
                    g = parseInt(hex.substring(2, 4), 16);
                    b = parseInt(hex.substring(4, 6), 16);
                }
                
                // 返回RGBA格式
                return `rgba(${r}, ${g}, ${b}, ${alpha})`;
            }
            
            // 辅助函数：解析RGBA颜色
            function parseRgba(rgba) {
                // 匹配rgba(r, g, b, a)或rgb(r, g, b)格式
                const rgbaMatch = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
                if (rgbaMatch) {
                    return {
                        r: parseInt(rgbaMatch[1]),
                        g: parseInt(rgbaMatch[2]),
                        b: parseInt(rgbaMatch[3]),
                        a: rgbaMatch[4] ? parseFloat(rgbaMatch[4]) : 1.0
                    };
                }
                return null;
            }
            
            // 辅助函数：RGB转HEX
            function rgbToHex(r, g, b) {
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }
            
            // 处理图片组件拖拽开始事件
            function handleImageDragStart(event) {
                event.dataTransfer.setData('text/plain', JSON.stringify({
                    type: 'image',
                    title: '图片'
                }));
            }
            
            // 处理图表组件拖拽开始事件
            function handleChartDragStart(event) {
                const chartType = event.target.getAttribute('data-chart-type');
                
                event.dataTransfer.setData('text/plain', JSON.stringify({
                    type: 'chart',
                    chartType: chartType
                }));
            }
            
            // 创建文本节点
            function createTextNode(data, x, y) {
                const nodeId = `text-${Date.now()}`;
                
                // 创建节点元素
                const nodeElement = document.createElement('div');
                nodeElement.id = nodeId;
                nodeElement.className = 'text-node';
                if (data.showBorder === false) {
                    nodeElement.classList.add('no-border');
                }
                nodeElement.setAttribute('data-component-type', 'text');
                nodeElement.style.left = `${x}px`;
                nodeElement.style.top = `${y}px`;
                
                // 设置默认或保存的宽高
                nodeElement.style.width = data.width ? `${data.width}px` : '100px';
                nodeElement.style.height = data.height ? `${data.height}px` : '40px';
                
                // 创建可编辑文本元素
                const textElement = document.createElement('div');
                textElement.className = 'editable';
                textElement.contentEditable = 'true';
                textElement.textContent = data.text || '文本';
                
                // 创建调整大小的手柄
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'resize-handle';
                
                // 添加调整大小的事件处理
                resizeHandle.addEventListener('mousedown', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    
                    // 记录初始位置和大小
                    const startX = e.clientX;
                    const startY = e.clientY;
                    const startWidth = nodeElement.offsetWidth;
                    const startHeight = nodeElement.offsetHeight;
                    
                    // 创建调整大小的函数
                    function resize(e) {
                        // 计算新的宽度和高度
                        const newWidth = Math.max(100, startWidth + e.clientX - startX);
                        const newHeight = Math.max(40, startHeight + e.clientY - startY);
                        
                        // 应用新的宽度和高度
                        nodeElement.style.width = `${newWidth}px`;
                        nodeElement.style.height = `${newHeight}px`;
                        
                        // 如果当前节点被选中，更新配置面板中的尺寸值
                        if (nodeElement.classList.contains('selected')) {
                            document.getElementById('textWidth').value = newWidth;
                            document.getElementById('textHeight').value = newHeight;
                        }
                    }
                    
                    // 创建停止调整大小的函数
                    function stopResize() {
                        document.removeEventListener('mousemove', resize);
                        document.removeEventListener('mouseup', stopResize);
                    }
                    
                    // 添加事件监听器
                    document.addEventListener('mousemove', resize);
                    document.addEventListener('mouseup', stopResize);
                });
                
                // 添加元素到节点
                nodeElement.appendChild(textElement);
                nodeElement.appendChild(resizeHandle);
                
                // 添加点击事件（选中节点）
                nodeElement.addEventListener('click', function(event) {
                    event.stopPropagation();
                    selectNode(nodeElement);
                });
                
                // 添加双击事件（编辑文本）
                textElement.addEventListener('dblclick', function(event) {
                    event.stopPropagation();
                    this.focus();
                });
                
                // 添加到画布
                canvas.appendChild(nodeElement);
                
                // 使节点可拖动
                jsPlumbInstance.draggable(nodeId, {
                    grid: [10, 10]
                });
                
                // 选中新创建的节点
                selectNode(nodeElement);
                
                return nodeElement;
            }
            
            // 创建图片节点
            function createImageNode(data, x, y) {
                const nodeId = `image-${Date.now()}`;
                
                // 创建节点元素
                const nodeElement = document.createElement('div');
                nodeElement.id = nodeId;
                nodeElement.className = 'image-node';
                nodeElement.setAttribute('data-component-type', 'image');
                nodeElement.style.left = `${x}px`;
                nodeElement.style.top = `${y}px`;
                
                // 设置默认或保存的宽高
                nodeElement.style.width = data.width ? `${data.width}px` : '200px';
                nodeElement.style.height = data.height ? `${data.height}px` : '150px';
                
                // 创建图片标题
                const titleElement = document.createElement('div');
                titleElement.className = 'image-title';
                titleElement.textContent = data.title || '图片';
                if (data.showTitle === false) {
                    titleElement.style.display = 'none';
                }
                
                // 创建图片容器
                const imageContainer = document.createElement('div');
                imageContainer.className = 'image-container';
                
                // 如果有图片URL，创建图片元素
                if (data.imageUrl) {
                    const imageElement = document.createElement('img');
                    imageElement.src = data.imageUrl;
                    imageElement.alt = data.title || '图片';
                    
                    // 设置图片缩放
                    if (data.imageScale) {
                        imageElement.style.transform = `scale(${data.imageScale})`;
                    }
                    
                    imageContainer.appendChild(imageElement);
                } else {
                    // 没有图片URL，显示提示
                    imageContainer.innerHTML = '<div class="no-image">请添加图片</div>';
                }
                
                // 创建调整大小的手柄
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'resize-handle';
                
                // 添加调整大小的事件处理
                resizeHandle.addEventListener('mousedown', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    
                    // 记录初始位置和大小
                    const startX = e.clientX;
                    const startY = e.clientY;
                    const startWidth = nodeElement.offsetWidth;
                    const startHeight = nodeElement.offsetHeight;
                    
                    // 创建调整大小的函数
                    function resize(e) {
                        // 计算新的宽度和高度
                        const newWidth = Math.max(150, startWidth + e.clientX - startX);
                        const newHeight = Math.max(100, startHeight + e.clientY - startY);
                        
                        // 应用新的宽度和高度
                        nodeElement.style.width = `${newWidth}px`;
                        nodeElement.style.height = `${newHeight}px`;
                        
                        // 如果当前节点被选中，更新配置面板中的尺寸值
                        if (nodeElement.classList.contains('selected')) {
                            document.getElementById('imageWidth').value = newWidth;
                            document.getElementById('imageHeight').value = newHeight;
                        }
                    }
                    
                    // 创建停止调整大小的函数
                    function stopResize() {
                        document.removeEventListener('mousemove', resize);
                        document.removeEventListener('mouseup', stopResize);
                    }
                    
                    // 添加事件监听器
                    document.addEventListener('mousemove', resize);
                    document.addEventListener('mouseup', stopResize);
                });
                
                // 添加元素到节点
                nodeElement.appendChild(titleElement);
                nodeElement.appendChild(imageContainer);
                nodeElement.appendChild(resizeHandle);
                
                // 添加点击事件（选中节点）
                nodeElement.addEventListener('click', function(event) {
                    event.stopPropagation();
                    selectNode(nodeElement);
                });
                
                // 添加到画布
                canvas.appendChild(nodeElement);
                
                // 使节点可拖动
                jsPlumbInstance.draggable(nodeId, {
                    grid: [10, 10]
                });
                
                return nodeElement;
            }
            
            // 创建图表节点
            function createChartNode(data, x, y) {
                const nodeId = `chart-${Date.now()}`;
                
                // 创建节点元素
                const nodeElement = document.createElement('div');
                nodeElement.id = nodeId;
                nodeElement.className = 'chart-node';
                nodeElement.setAttribute('data-component-type', data.type);
                nodeElement.style.left = `${x}px`;
                nodeElement.style.top = `${y}px`;
                
                // 设置自定义宽高（如果有）
                if (data.width) {
                    nodeElement.style.width = `${data.width}px`;
                }
                if (data.height) {
                    nodeElement.style.height = `${data.height}px`;
                }
                
                // 设置背景和边框颜色（如果有）
                if (data.backgroundColor) {
                    nodeElement.style.backgroundColor = data.backgroundColor;
                }
                if (data.borderColor) {
                    nodeElement.style.borderColor = data.borderColor;
                }
                
                // 设置刷新间隔和数据源（如果有）
                if (data.refreshInterval) {
                    nodeElement.setAttribute('data-refresh-interval', data.refreshInterval);
                }
                if (data.dataSource) {
                    nodeElement.setAttribute('data-data-source', data.dataSource);
                }
                
                // 创建图表标题
                const titleElement = document.createElement('div');
                titleElement.className = 'chart-title';
                titleElement.textContent = data.title || '图表';
                if (data.showTitle === false) {
                    titleElement.style.display = 'none';
                }
                
                // 创建图表容器
                const chartContainer = document.createElement('div');
                chartContainer.className = 'chart-container';
                
                // 根据图表类型创建不同的图表
                switch (data.type) {
                    case 'line-chart':
                        chartContainer.innerHTML = '<div class="placeholder-chart">折线图</div>';
                        break;
                    case 'bar-chart':
                        chartContainer.innerHTML = '<div class="placeholder-chart">柱状图</div>';
                        break;
                    case 'gauge-chart':
                        chartContainer.innerHTML = '<div class="placeholder-chart">仪表盘</div>';
                        break;
                    default:
                        chartContainer.innerHTML = '<div class="placeholder-chart">请选择图表类型</div>';
                }
                
                // 创建调整大小的手柄
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'resize-handle';
                
                // 添加元素到节点
                nodeElement.appendChild(titleElement);
                nodeElement.appendChild(chartContainer);
                nodeElement.appendChild(resizeHandle);
                
                // 添加点击事件（选中节点）
                nodeElement.addEventListener('click', function(event) {
                    event.stopPropagation();
                    selectNode(nodeElement);
                });
                
                // 添加到画布
                canvas.appendChild(nodeElement);
                
                // 使节点可拖动
                jsPlumbInstance.draggable(nodeId, {
                    grid: [10, 10]
                });
                
                // 添加调整大小的功能
                if (resizeHandle) {
                    resizeHandle.addEventListener('mousedown', function(e) {
                        e.stopPropagation();
                        e.preventDefault();
                        
                        const startX = e.clientX;
                        const startY = e.clientY;
                        const startWidth = parseInt(nodeElement.style.width) || nodeElement.offsetWidth;
                        const startHeight = parseInt(nodeElement.style.height) || nodeElement.offsetHeight;
                        
                        function handleMouseMove(e) {
                            const newWidth = startWidth + e.clientX - startX;
                            const newHeight = startHeight + e.clientY - startY;
                            
                            nodeElement.style.width = `${Math.max(150, newWidth)}px`;
                            nodeElement.style.height = `${Math.max(150, newHeight)}px`;
                            
                            // 重绘jsPlumb连接
                            jsPlumbInstance.repaintEverything();
                        }
                        
                        function handleMouseUp() {
                            document.removeEventListener('mousemove', handleMouseMove);
                            document.removeEventListener('mouseup', handleMouseUp);
                        }
                        
                        document.addEventListener('mousemove', handleMouseMove);
                        document.addEventListener('mouseup', handleMouseUp);
                    });
                }
                
                // 选中新创建的节点
                selectNode(nodeElement);
            }
            
            // 添加图片节点的图片缩放滑动条事件
            document.getElementById('imageNodeScale').addEventListener('input', function(event) {
                const scale = parseFloat(event.target.value);
                document.getElementById('imageNodeScaleValue').textContent = scale.toFixed(1);
            });
            
            // 添加图片节点的背景透明度滑动条事件
            document.getElementById('imageBgOpacity').addEventListener('input', function(event) {
                document.getElementById('imageBgOpacityValue').textContent = event.target.value;
            });
            
            // 添加图片节点的边框透明度滑动条事件
            document.getElementById('imageBorderOpacity').addEventListener('input', function(event) {
                document.getElementById('imageBorderOpacityValue').textContent = event.target.value;
            });
            
            // 加载图片URL按钮事件
            document.getElementById('loadImageNodeUrl').addEventListener('click', function() {
                const imageUrl = document.getElementById('imageNodeUrl').value;
                if (!imageUrl) return;
                
                const imagePreview = document.getElementById('imageNodePreview');
                imagePreview.style.display = 'block';
                imagePreview.querySelector('img').src = imageUrl;
            });
            
            // 移除图片按钮事件
            document.getElementById('removeImageNodeImage').addEventListener('click', function() {
                document.getElementById('imageNodeUrl').value = '';
                document.getElementById('imageNodePreview').style.display = 'none';
            });
            
            // 处理图片节点的本地图片上传
            document.getElementById('imageNodeFile').addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件');
                    return;
                }
                
                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);
                
                // 显示上传中提示
                    const imagePreview = document.getElementById('imageNodePreview');
                    imagePreview.style.display = 'block';
                imagePreview.querySelector('img').src = '/images/loading.gif';
                
                // 上传图片到服务器
                fetch('/api/upload/image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '上传失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    // 更新预览和URL输入框
                    const imageUrl = data.url; // 使用服务器返回的完整URL
                    document.getElementById('imageNodeUrl').value = imageUrl;
                    imagePreview.querySelector('img').src = imageUrl;
                })
                .catch(error => {
                    alert('图片上传失败: ' + error.message);
                    imagePreview.style.display = 'none';
                });
            });
            
            // 应用图片配置按钮事件
            document.getElementById('applyImageConfig').addEventListener('click', function() {
                if (!selectedNode || !selectedNode.classList.contains('image-node')) return;
                
                // 获取配置值
                const imageTitle = document.getElementById('imageTitle').value;
                const imageBgColor = document.getElementById('imageBgColor').value;
                const imageBgOpacity = document.getElementById('imageBgOpacity').value;
                const imageBorderColor = document.getElementById('imageBorderColor').value;
                const imageBorderOpacity = document.getElementById('imageBorderOpacity').value;
                const imageUrl = document.getElementById('imageNodeUrl').value;
                const imageScale = parseFloat(document.getElementById('imageNodeScale').value);
                const imageWidth = parseInt(document.getElementById('imageWidth').value) || 200;
                const imageHeight = parseInt(document.getElementById('imageHeight').value) || 150;
                const showTitle = document.getElementById('showImageTitle').checked;
                
                // 更新图片标题
                const titleElement = selectedNode.querySelector('.image-title');
                if (titleElement) {
                    titleElement.textContent = imageTitle;
                    titleElement.style.display = showTitle ? '' : 'none';
                }
                
                // 计算带透明度的颜色值
                const bgOpacity = imageBgOpacity / 100;
                const borderOpacity = imageBorderOpacity / 100;
                
                // 将HEX颜色转换为RGBA
                const bgRgba = hexToRgba(imageBgColor, bgOpacity);
                const borderRgba = hexToRgba(imageBorderColor, borderOpacity);
                
                // 更新样式
                selectedNode.style.backgroundColor = bgRgba;
                selectedNode.style.borderColor = borderRgba;
                selectedNode.style.width = `${imageWidth}px`;
                selectedNode.style.height = `${imageHeight}px`;
                
                // 处理图片
                const imageContainer = selectedNode.querySelector('.image-container');
                
                if (imageUrl) {
                    // 检查是否已有图片元素
                    let imageElement = imageContainer.querySelector('img');
                    
                    if (!imageElement) {
                        // 创建图片元素
                        imageElement = document.createElement('img');
                        imageContainer.innerHTML = '';
                        imageContainer.appendChild(imageElement);
                    }
                    
                    // 设置图片属性
                    imageElement.src = imageUrl;
                    imageElement.alt = imageTitle;
                    imageElement.style.transform = `scale(${imageScale})`;
                } else {
                    // 移除图片元素，显示提示
                    imageContainer.innerHTML = '<div class="no-image">请添加图片</div>';
                }
                
                // 重绘jsPlumb连接
                jsPlumbInstance.repaintEverything();
            });

            // 删除布局按钮点击事件
            document.getElementById('deleteLayoutBtn').addEventListener('click', async function() {
                const layoutSelector = document.getElementById('layoutSelector');
                const layoutName = layoutSelector.value;
                if (!layoutName) return;
                
                if (!confirm(`确定要删除布局 "${layoutName}" 吗？`)) return;
                
                try {
                    const response = await fetch(`/topology/delete/${layoutName}`, {
                        method: 'DELETE'
                    });
                    
                    if (!response.ok) {
                        const data = await response.json();
                        throw new Error(data.message || '删除失败');
                    }
                    
                    // 重新加载布局列表
                    await loadLayoutList();
                    
                    // 清空布局名称
                    document.getElementById('layoutName').value = '';
                    
                    // 禁用删除按钮
                    document.getElementById('deleteLayoutBtn').disabled = true;
                    document.getElementById('previewLayoutBtn').disabled = true;
                    
                    // 重置选择器
                    layoutSelector.value = '';
                    
                    alert('布局删除成功');
                } catch (error) {
                    console.error('删除布局失败:', error);
                    alert('删除布局失败: ' + error.message);
                }
            });

            // 预览布局按钮点击事件
            document.getElementById('previewLayoutBtn').addEventListener('click', function() {
                const layoutName = document.getElementById('layoutSelector').value;
                if (!layoutName) return;
                
                // 在新窗口中打开预览页面
                window.open(`/topology/preview/${layoutName}`, '_blank');
            });

            // 文本对齐按钮点击事件
            document.querySelectorAll('#textConfig .btn-group button').forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    this.parentElement.querySelectorAll('button').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    // 添加当前按钮的active类
                    this.classList.add('active');
                    // 更新隐藏输入框的值
                    document.getElementById('textAlign').value = this.dataset.align;
                });
            });

            // 添加画布配置相关的事件处理
            document.getElementById('applyCanvasConfig').addEventListener('click', function() {
                const canvas = document.getElementById('canvas');
                const width = document.getElementById('canvasWidth').value;
                const height = document.getElementById('canvasHeight').value;
                const showGrid = document.getElementById('showGrid').checked;
                const gridSize = document.getElementById('gridSize').value;
                const gridColor = document.getElementById('gridColor').value;
                const backgroundColor = document.getElementById('canvasBackgroundColor').value;

                // 设置画布尺寸
                canvas.style.width = `${width}px`;
                canvas.style.height = `${height}px`;
                
                // 设置背景颜色
                canvas.style.backgroundColor = backgroundColor;

                // 设置网格
                if (showGrid) {
                    canvas.style.backgroundImage = `
                        linear-gradient(${gridColor} 1px, transparent 1px),
                        linear-gradient(90deg, ${gridColor} 1px, transparent 1px)
                    `;
                    canvas.style.backgroundSize = `${gridSize}px ${gridSize}px`;
                } else {
                    canvas.style.backgroundImage = 'none';
                }

                // 重绘所有连接
                jsPlumbInstance.repaintEverything();
            });

            // 初始化画布配置值
            function initCanvasConfig() {
                const canvas = document.getElementById('canvas');
                document.getElementById('canvasWidth').value = parseInt(canvas.style.width) || 3000;
                document.getElementById('canvasHeight').value = parseInt(canvas.style.height) || 2000;
                document.getElementById('showGrid').checked = canvas.style.backgroundImage !== 'none';
                
                // 初始化背景颜色值
                const backgroundColor = canvas.style.backgroundColor || '#ffffff';
                document.getElementById('canvasBackgroundColor').value = backgroundColor;
            }

            // 页面加载完成后初始化画布配置
            document.addEventListener('DOMContentLoaded', function() {
                initCanvasConfig();
                // ... existing code ...
            });
        });
    </script>
</body>
</html> 