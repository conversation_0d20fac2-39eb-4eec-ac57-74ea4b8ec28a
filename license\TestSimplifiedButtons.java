import javax.swing.*;
import java.awt.*;

/**
 * 测试简化后的按钮样式
 */
public class TestSimplifiedButtons {
    public static void main(String[] args) {
        // 设置Windows外观
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Windows".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        SwingUtilities.invokeLater(() -> {
            createTestFrame();
        });
    }
    
    private static void createTestFrame() {
        JFrame frame = new JFrame("简化按钮样式测试");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(700, 500);
        frame.setLocationRelativeTo(null);
        
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 主要按钮
        gbc.gridx = 0; gbc.gridy = 0;
        JButton generateButton = new JButton("生成序列号");
        generateButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 14));
        generateButton.setForeground(Color.BLACK);
        generateButton.setPreferredSize(new Dimension(150, 40));
        generateButton.setFocusPainted(false);
        panel.add(generateButton, gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        JButton copyButton = new JButton("复制到剪贴板");
        copyButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        copyButton.setForeground(Color.BLACK);
        copyButton.setPreferredSize(new Dimension(150, 40));
        copyButton.setFocusPainted(false);
        panel.add(copyButton, gbc);
        
        gbc.gridx = 2; gbc.gridy = 0;
        JButton parseButton = new JButton("许可解析");
        parseButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        parseButton.setForeground(Color.BLACK);
        parseButton.setPreferredSize(new Dimension(120, 40));
        parseButton.setFocusPainted(false);
        panel.add(parseButton, gbc);
        
        // 快捷按钮
        gbc.gridx = 0; gbc.gridy = 1;
        JButton todayButton = new JButton("今天");
        todayButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        todayButton.setForeground(Color.BLACK);
        todayButton.setFocusPainted(false);
        panel.add(todayButton, gbc);
        
        gbc.gridx = 1; gbc.gridy = 1;
        JButton randomButton = new JButton("随机生成");
        randomButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        randomButton.setForeground(Color.BLACK);
        randomButton.setFocusPainted(false);
        panel.add(randomButton, gbc);
        
        gbc.gridx = 2; gbc.gridy = 1;
        JButton browseButton = new JButton("浏览");
        browseButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        browseButton.setForeground(Color.BLACK);
        browseButton.setFocusPainted(false);
        panel.add(browseButton, gbc);
        
        // 快捷设置按钮
        gbc.gridx = 0; gbc.gridy = 2;
        JButton day1Button = new JButton("1天激活期");
        day1Button.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        day1Button.setForeground(Color.BLACK);
        day1Button.setFocusPainted(false);
        panel.add(day1Button, gbc);
        
        gbc.gridx = 1; gbc.gridy = 2;
        JButton month1Button = new JButton("1个月许可");
        month1Button.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        month1Button.setForeground(Color.BLACK);
        month1Button.setFocusPainted(false);
        panel.add(month1Button, gbc);
        
        gbc.gridx = 2; gbc.gridy = 2;
        JButton year1Button = new JButton("1年许可");
        year1Button.setFont(new Font("Microsoft YaHei", Font.PLAIN, 11));
        year1Button.setForeground(Color.BLACK);
        year1Button.setFocusPainted(false);
        panel.add(year1Button, gbc);
        
        // 测试文本区域
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 3;
        JTextArea textArea = new JTextArea(8, 50);
        textArea.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        textArea.setEditable(false);
        textArea.setBackground(new Color(248, 249, 250));
        textArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        String testText = "按钮样式简化测试结果：\n\n" +
                         "✅ 移除了所有背景色设置\n" +
                         "✅ 移除了所有边框设置\n" +
                         "✅ 统一使用黑色文字\n" +
                         "✅ 保持Microsoft YaHei字体\n" +
                         "✅ 保持系统默认按钮样式\n\n" +
                         "现在按钮应该显示为：\n" +
                         "- 系统默认的浅灰色背景\n" +
                         "- 清晰的黑色文字\n" +
                         "- 简洁的外观，无复杂颜色\n" +
                         "- 良好的可读性和兼容性";
        
        textArea.setText(testText);
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        panel.add(scrollPane, gbc);
        
        // 测试按钮点击
        parseButton.addActionListener(e -> {
            JOptionPane.showMessageDialog(frame, 
                "按钮样式简化完成！\n\n" +
                "特点：\n" +
                "• 系统默认外观\n" +
                "• 黑色文字，清晰可读\n" +
                "• 无复杂颜色干扰\n" +
                "• 兼容所有系统主题", 
                "测试结果", 
                JOptionPane.INFORMATION_MESSAGE);
        });
        
        frame.add(panel);
        frame.setVisible(true);
        
        System.out.println("简化按钮样式测试程序已启动");
        System.out.println("✅ 移除了复杂的背景色和边框");
        System.out.println("✅ 统一使用黑色文字");
        System.out.println("✅ 保持系统默认按钮样式");
        System.out.println("✅ 提升了兼容性和可读性");
    }
}
