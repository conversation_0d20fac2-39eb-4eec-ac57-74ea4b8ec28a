/**
 * BI组件配置定义
 * 参考report项目的组件配置结构
 */

// 通用配置项
const commonConfigItems = {
    // 基础设置
    layerName: {
        type: 'el-input-text',
        label: '图层名称',
        name: 'layerName',
        required: false,
        placeholder: '',
        value: ''
    },
    background: {
        type: 'vue-color',
        label: '背景颜色',
        name: 'background',
        required: false,
        placeholder: '',
        value: ''
    },
    
    // 标题设置组
    titleGroup: {
        name: '标题设置',
        list: [
            {
                type: 'el-switch',
                label: '标题显示',
                name: 'isShowTitle',
                required: false,
                placeholder: '',
                value: true
            },
            {
                type: 'el-input-text',
                label: '标题名',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
            },
            {
                type: 'vue-color',
                label: '标题颜色',
                name: 'titleColor',
                required: false,
                placeholder: '',
                value: '#333333'
            },
            {
                type: 'el-input-number',
                label: '标题字号',
                name: 'titleFontSize',
                required: false,
                placeholder: '',
                value: 16
            },
            {
                type: 'el-select',
                label: '标题位置',
                name: 'titlePosition',
                required: false,
                placeholder: '',
                selectOptions: [
                    {code: 'left', name: '左对齐'},
                    {code: 'center', name: '居中'},
                    {code: 'right', name: '右对齐'}
                ],
                value: 'center'
            }
        ]
    },
    
    // 图例设置组
    legendGroup: {
        name: '图例设置',
        list: [
            {
                type: 'el-switch',
                label: '图例显示',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true
            },
            {
                type: 'el-select',
                label: '图例位置',
                name: 'legendPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                    {code: 'top', name: '顶部'},
                    {code: 'bottom', name: '底部'},
                    {code: 'left', name: '左侧'},
                    {code: 'right', name: '右侧'}
                ],
                value: 'top'
            },
            {
                type: 'vue-color',
                label: '图例颜色',
                name: 'legendColor',
                required: false,
                placeholder: '',
                value: '#666666'
            },
            {
                type: 'el-input-number',
                label: '图例字号',
                name: 'legendFontSize',
                required: false,
                placeholder: '',
                value: 12
            }
        ]
    }
};

// 组件配置定义
const widgetConfigs = {
    'line-chart': {
        name: '折线图',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '折线图',
            background: '',
            
            // 标题配置
            isShowTitle: true,
            titleText: '折线图',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',
            
            // 图例配置
            isShowLegend: true,
            legendPosition: 'top',
            legendColor: '#666666',
            legendFontSize: 12,
            
            // 折线图特有配置
            smooth: true,
            showSymbol: true,
            symbolSize: 6,
            lineWidth: 2,
            showArea: false,
            
            // 坐标轴配置
            showXAxis: true,
            showYAxis: true,
            xAxisColor: '#cccccc',
            yAxisColor: '#cccccc',
            gridLineColor: '#f0f0f0',
            
            // 数据标签
            showDataLabel: false,
            dataLabelColor: '#333333',
            dataLabelFontSize: 12,
            
            // 主题配置
            theme: 'default',
            colorScheme: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '折线图'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },
            
            // 标题设置组
            {
                name: '标题设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '标题显示',
                        name: 'isShowTitle',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-input-text',
                        label: '标题名',
                        name: 'titleText',
                        required: false,
                        placeholder: '',
                        value: '折线图'
                    },
                    {
                        type: 'vue-color',
                        label: '标题颜色',
                        name: 'titleColor',
                        required: false,
                        placeholder: '',
                        value: '#333333'
                    },
                    {
                        type: 'el-input-number',
                        label: '标题字号',
                        name: 'titleFontSize',
                        required: false,
                        placeholder: '',
                        value: 16
                    }
                ]
            },
            
            // 折线设置组
            {
                name: '折线设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '平滑曲线',
                        name: 'smooth',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-switch',
                        label: '显示数据点',
                        name: 'showSymbol',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-input-number',
                        label: '数据点大小',
                        name: 'symbolSize',
                        required: false,
                        placeholder: '',
                        value: 6
                    },
                    {
                        type: 'el-input-number',
                        label: '线条宽度',
                        name: 'lineWidth',
                        required: false,
                        placeholder: '',
                        value: 2
                    },
                    {
                        type: 'el-switch',
                        label: '显示面积',
                        name: 'showArea',
                        required: false,
                        placeholder: '',
                        value: false
                    }
                ]
            }
        ]
    },
    
    'bar-chart': {
        name: '柱状图',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '柱状图',
            background: '',
            
            // 标题配置
            isShowTitle: true,
            titleText: '柱状图',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',
            
            // 图例配置
            isShowLegend: true,
            legendPosition: 'top',
            legendColor: '#666666',
            legendFontSize: 12,
            
            // 柱状图特有配置
            barWidth: '60%',
            borderRadius: 4,
            barGap: '20%',
            
            // 坐标轴配置
            showXAxis: true,
            showYAxis: true,
            xAxisColor: '#cccccc',
            yAxisColor: '#cccccc',
            gridLineColor: '#f0f0f0',
            
            // 数据标签
            showDataLabel: false,
            dataLabelColor: '#333333',
            dataLabelFontSize: 12,
            
            // 主题配置
            theme: 'default',
            colorScheme: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '柱状图'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },
            
            // 标题设置组
            {
                name: '标题设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '标题显示',
                        name: 'isShowTitle',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-input-text',
                        label: '标题名',
                        name: 'titleText',
                        required: false,
                        placeholder: '',
                        value: '柱状图'
                    },
                    {
                        type: 'vue-color',
                        label: '标题颜色',
                        name: 'titleColor',
                        required: false,
                        placeholder: '',
                        value: '#333333'
                    },
                    {
                        type: 'el-input-number',
                        label: '标题字号',
                        name: 'titleFontSize',
                        required: false,
                        placeholder: '',
                        value: 16
                    }
                ]
            },
            
            // 柱体设置组
            {
                name: '柱体设置',
                list: [
                    {
                        type: 'el-input-text',
                        label: '柱体宽度',
                        name: 'barWidth',
                        required: false,
                        placeholder: '',
                        value: '60%'
                    },
                    {
                        type: 'el-input-number',
                        label: '圆角半径',
                        name: 'borderRadius',
                        required: false,
                        placeholder: '',
                        value: 4
                    },
                    {
                        type: 'el-input-text',
                        label: '柱体间距',
                        name: 'barGap',
                        required: false,
                        placeholder: '',
                        value: '20%'
                    }
                ]
            }
        ]
    },

    'multi-line-chart': {
        name: '多折线图',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '多折线图',
            background: '',

            // 标题配置
            isShowTitle: true,
            titleText: '多折线图',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',

            // 图例配置（使用项目标准命名）
            isShowLegend: true,
            legendPosition: 'top',
            legendColor: '#666666',
            legendFontSize: 12,

            // 多折线图特有配置
            enableDualYAxis: false,  // 是否启用双Y轴
            smooth: true,            // 平滑曲线（使用项目标准命名）
            showSymbol: true,        // 显示标记点
            symbolSize: 6,           // 标记点大小（使用项目标准值）
            lineWidth: 2,            // 线条宽度
            showArea: false,         // 启用面积填充（使用项目标准命名）

            // 数据同步配置
            enableDataSync: true,    // 启用数据同步等待
            dataSyncDelay: 500,      // 数据同步延迟时间(ms)

            // 坐标轴配置（使用项目标准命名）
            showXAxis: true,
            showYAxis: true,
            xAxisColor: '#cccccc',
            yAxisColor: '#cccccc',
            gridLineColor: '#f0f0f0',

            // 数据标签（使用项目标准命名）
            showDataLabel: false,
            dataLabelColor: '#333333',
            dataLabelFontSize: 12,

            // 主题配置（使用项目标准）
            theme: 'default',
            colorScheme: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '多折线图'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 折线设置（使用项目标准配置项）
            {
                type: 'el-switch',
                label: '双Y轴模式',
                name: 'enableDualYAxis',
                required: false,
                placeholder: '',
                value: false
            },
            {
                type: 'el-switch',
                label: '平滑曲线',
                name: 'smooth',
                required: false,
                placeholder: '',
                value: true
            },
            {
                type: 'el-switch',
                label: '显示标记点',
                name: 'showSymbol',
                required: false,
                placeholder: '',
                value: true
            },
            {
                type: 'el-slider',
                label: '标记点大小',
                name: 'symbolSize',
                required: false,
                placeholder: '',
                value: 6,
                min: 2,
                max: 20
            },
            {
                type: 'el-switch',
                label: '面积填充',
                name: 'showArea',
                required: false,
                placeholder: '',
                value: false
            },
            {
                type: 'el-slider',
                label: '线条宽度',
                name: 'lineWidth',
                required: false,
                placeholder: '',
                value: 2,
                min: 1,
                max: 10
            },

            // 各折线样式配置
            {
                name: '各折线样式',
                list: [
                    {
                        type: 'el-switch',
                        label: '启用单独样式',
                        name: 'useIndividualStyles',
                        required: false,
                        placeholder: '',
                        value: false
                    },
                    {
                        type: 'el-input-number',
                        label: '折线数量',
                        name: 'lineCount',
                        required: false,
                        placeholder: '',
                        value: 1,
                        min: 1,
                        max: 10
                    },
                    {
                        type: 'custom-html',
                        label: '折线样式配置',
                        name: 'lineStylesConfig',
                        html: `
                            <div id="multiLineStylesContainer">
                                <div class="alert alert-info">
                                    <small>启用"单独样式"后，可为每条折线设置独立的样式配置</small>
                                </div>
                                <div id="lineStylesList"></div>
                                <button type="button" class="btn btn-sm btn-primary mt-2" onclick="generateMultiLineStylesConfig()">
                                    生成样式配置
                                </button>
                            </div>
                        `
                    }
                ]
            },

            // 数据同步配置
            {
                name: '数据同步设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '启用数据同步',
                        name: 'enableDataSync',
                        required: false,
                        placeholder: '',
                        value: true,
                        help: '启用后，多个数据集更新时会等待一定时间再刷新图表，避免折线中断'
                    },
                    {
                        type: 'el-input-number',
                        label: '同步延迟时间(ms)',
                        name: 'dataSyncDelay',
                        required: false,
                        placeholder: '',
                        value: 500,
                        min: 100,
                        max: 2000,
                        help: '数据同步等待时间，建议300-1000ms'
                    }
                ]
            }
        ]
    },

    'pie-chart': {
        name: '饼图',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '饼图',
            background: '',

            // 标题配置
            isShowTitle: true,
            titleText: '饼图',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',

            // 图例配置
            isShowLegend: true,
            legendPosition: 'right',
            legendColor: '#666666',
            legendFontSize: 12,

            // 饼图特有配置
            radius: ['0%', '75%'],
            center: ['50%', '50%'],
            roseType: false,
            showLabel: true,
            labelPosition: 'outside',

            // 数据标签
            showDataLabel: true,
            dataLabelColor: '#333333',
            dataLabelFontSize: 12,

            // 主题配置
            theme: 'default',
            colorScheme: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '饼图'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 标题设置组
            {
                name: '标题设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '标题显示',
                        name: 'isShowTitle',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-input-text',
                        label: '标题名',
                        name: 'titleText',
                        required: false,
                        placeholder: '',
                        value: '饼图'
                    },
                    {
                        type: 'vue-color',
                        label: '标题颜色',
                        name: 'titleColor',
                        required: false,
                        placeholder: '',
                        value: '#333333'
                    }
                ]
            },

            // 饼图设置组
            {
                name: '饼图设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '环形图',
                        name: 'roseType',
                        required: false,
                        placeholder: '',
                        value: false
                    },
                    {
                        type: 'el-switch',
                        label: '显示标签',
                        name: 'showLabel',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-select',
                        label: '标签位置',
                        name: 'labelPosition',
                        required: false,
                        placeholder: '',
                        selectOptions: [
                            {code: 'outside', name: '外部'},
                            {code: 'inside', name: '内部'},
                            {code: 'center', name: '中心'}
                        ],
                        value: 'outside'
                    }
                ]
            }
        ]
    },

    'gauge-chart': {
        name: '仪表盘',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '仪表盘',
            background: '',

            // 标题配置
            isShowTitle: true,
            titleText: '仪表盘',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',

            // 仪表盘特有配置
            min: 0,
            max: 100,
            unit: '%',
            radius: '80%',
            center: ['50%', '60%'],

            // 指针配置
            pointerColor: '#5470c6',
            pointerWidth: 6,

            // 刻度配置
            showTick: true,
            tickColor: '#cccccc',
            showSplitLine: true,
            splitLineColor: '#cccccc',

            // 数据标签
            showDataLabel: true,
            dataLabelColor: '#333333',
            dataLabelFontSize: 20,

            // 主题配置
            theme: 'default'
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '仪表盘'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 数值设置组
            {
                name: '数值设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '最小值',
                        name: 'min',
                        required: false,
                        placeholder: '',
                        value: 0
                    },
                    {
                        type: 'el-input-number',
                        label: '最大值',
                        name: 'max',
                        required: false,
                        placeholder: '',
                        value: 100
                    },
                    {
                        type: 'el-input-text',
                        label: '单位',
                        name: 'unit',
                        required: false,
                        placeholder: '',
                        value: '%'
                    }
                ]
            }
        ]
    },

    'water-chart': {
        name: '水波图',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '水波图',
            background: '',

            // 标题配置
            isShowTitle: true,
            titleText: '水波图',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',

            // 水波图特有配置
            borderWidth: 5,
            borderColor: '#5470c6',
            waveColor: '#5470c6',
            amplitude: 20,
            waveLength: '80%',

            // 目标值配置
            enableTarget: false,
            targetValue: 100,
            targetColor: '#ff6b6b',

            // 数据标签
            showDataLabel: true,
            dataLabelColor: '#ffffff',
            dataLabelFontSize: 20,

            // 主题配置
            theme: 'default'
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '水波图'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 水波设置组
            {
                name: '水波设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '边框宽度',
                        name: 'borderWidth',
                        required: false,
                        placeholder: '',
                        value: 5
                    },
                    {
                        type: 'vue-color',
                        label: '边框颜色',
                        name: 'borderColor',
                        required: false,
                        placeholder: '',
                        value: '#5470c6'
                    },
                    {
                        type: 'vue-color',
                        label: '水波颜色',
                        name: 'waveColor',
                        required: false,
                        placeholder: '',
                        value: '#5470c6'
                    },
                    {
                        type: 'el-input-number',
                        label: '波浪幅度',
                        name: 'amplitude',
                        required: false,
                        placeholder: '',
                        value: 20
                    }
                ]
            }
        ]
    },

    'text-label': {
        name: '文本组件',
        type: 'text',
        defaultConfig: {
            // 基础配置
            layerName: '文本组件',
            background: '',

            // 文本配置
            text: '文本内容',
            fontSize: 14,
            fontColor: '#333333',
            fontWeight: 'normal',
            fontStyle: 'normal',
            fontFamily: 'Arial',
            textAlign: 'left',
            lineHeight: 1.5,

            // 边框配置
            borderWidth: 0,
            borderColor: '#cccccc',
            borderStyle: 'solid',
            borderRadius: 0,

            // 内边距
            padding: 10
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '文本组件'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 文本设置组
            {
                name: '文本设置',
                list: [
                    {
                        type: 'el-input-text',
                        label: '文本内容',
                        name: 'text',
                        required: false,
                        placeholder: '',
                        value: '文本内容'
                    },
                    {
                        type: 'el-input-number',
                        label: '字体大小',
                        name: 'fontSize',
                        required: false,
                        placeholder: '',
                        value: 14
                    },
                    {
                        type: 'vue-color',
                        label: '字体颜色',
                        name: 'fontColor',
                        required: false,
                        placeholder: '',
                        value: '#333333'
                    },
                    {
                        type: 'el-select',
                        label: '字体粗细',
                        name: 'fontWeight',
                        required: false,
                        placeholder: '',
                        selectOptions: [
                            {code: 'normal', name: '正常'},
                            {code: 'bold', name: '粗体'},
                            {code: 'bolder', name: '特粗体'},
                            {code: 'lighter', name: '细体'}
                        ],
                        value: 'normal'
                    },
                    {
                        type: 'el-select',
                        label: '文本对齐',
                        name: 'textAlign',
                        required: false,
                        placeholder: '',
                        selectOptions: [
                            {code: 'left', name: '左对齐'},
                            {code: 'center', name: '居中'},
                            {code: 'right', name: '右对齐'}
                        ],
                        value: 'left'
                    }
                ]
            }
        ]
    },

    'status-indicator': {
        name: '状态指示器',
        type: 'indicator',
        defaultConfig: {
            // 基础配置
            layerName: '状态指示器',
            background: '',

            // 形状配置
            shape: 'circle',
            size: 60,
            borderWidth: 2,
            borderColor: '#cccccc',

            // 状态配置
            condition1: {
                min: 0,
                max: 30,
                name: '正常',
                color: '#28a745'
            },
            condition2: {
                min: 31,
                max: 70,
                name: '警告',
                color: '#ffc107'
            },
            condition3: {
                min: 71,
                max: 100,
                name: '危险',
                color: '#dc3545'
            },
            offlineColor: '#6c757d',

            // 显示配置
            showConditionName: true,
            fontSize: 12,
            fontColor: '#333333',
            fontWeight: 'normal',

            // 动画配置
            enableAnimation: true,
            animationDuration: 300
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '状态指示器'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 形状设置组
            {
                name: '形状设置',
                list: [
                    {
                        type: 'el-select',
                        label: '指示器形状',
                        name: 'shape',
                        required: false,
                        placeholder: '',
                        selectOptions: [
                            {code: 'circle', name: '圆形'},
                            {code: 'square', name: '方形'},
                            {code: 'rectangle', name: '长方形'},
                            {code: 'diamond', name: '菱形'}
                        ],
                        value: 'circle'
                    },
                    {
                        type: 'el-input-number',
                        label: '指示器大小',
                        name: 'size',
                        required: false,
                        placeholder: '',
                        value: 60,
                        min: 20,
                        max: 200
                    },
                    {
                        type: 'el-input-number',
                        label: '边框宽度',
                        name: 'borderWidth',
                        required: false,
                        placeholder: '',
                        value: 2,
                        min: 0,
                        max: 10
                    },
                    {
                        type: 'vue-color',
                        label: '边框颜色',
                        name: 'borderColor',
                        required: false,
                        placeholder: '',
                        value: '#cccccc'
                    }
                ]
            },

            // 条件设置组
            {
                name: '条件设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '条件1最小值',
                        name: 'condition1.min',
                        required: false,
                        placeholder: '',
                        value: 0
                    },
                    {
                        type: 'el-input-number',
                        label: '条件1最大值',
                        name: 'condition1.max',
                        required: false,
                        placeholder: '',
                        value: 30
                    },
                    {
                        type: 'el-input-text',
                        label: '条件1名称',
                        name: 'condition1.name',
                        required: false,
                        placeholder: '',
                        value: '正常'
                    },
                    {
                        type: 'vue-color',
                        label: '条件1颜色',
                        name: 'condition1.color',
                        required: false,
                        placeholder: '',
                        value: '#28a745'
                    },
                    {
                        type: 'el-input-number',
                        label: '条件2最小值',
                        name: 'condition2.min',
                        required: false,
                        placeholder: '',
                        value: 31
                    },
                    {
                        type: 'el-input-number',
                        label: '条件2最大值',
                        name: 'condition2.max',
                        required: false,
                        placeholder: '',
                        value: 70
                    },
                    {
                        type: 'el-input-text',
                        label: '条件2名称',
                        name: 'condition2.name',
                        required: false,
                        placeholder: '',
                        value: '警告'
                    },
                    {
                        type: 'vue-color',
                        label: '条件2颜色',
                        name: 'condition2.color',
                        required: false,
                        placeholder: '',
                        value: '#ffc107'
                    },
                    {
                        type: 'el-input-number',
                        label: '条件3最小值',
                        name: 'condition3.min',
                        required: false,
                        placeholder: '',
                        value: 71
                    },
                    {
                        type: 'el-input-number',
                        label: '条件3最大值',
                        name: 'condition3.max',
                        required: false,
                        placeholder: '',
                        value: 100
                    },
                    {
                        type: 'el-input-text',
                        label: '条件3名称',
                        name: 'condition3.name',
                        required: false,
                        placeholder: '',
                        value: '危险'
                    },
                    {
                        type: 'vue-color',
                        label: '条件3颜色',
                        name: 'condition3.color',
                        required: false,
                        placeholder: '',
                        value: '#dc3545'
                    },
                    {
                        type: 'vue-color',
                        label: '离线状态颜色',
                        name: 'offlineColor',
                        required: false,
                        placeholder: '',
                        value: '#6c757d'
                    }
                ]
            },

            // 显示设置组
            {
                name: '显示设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '显示条件名称',
                        name: 'showConditionName',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-input-number',
                        label: '字体大小',
                        name: 'fontSize',
                        required: false,
                        placeholder: '',
                        value: 12,
                        min: 8,
                        max: 24
                    },
                    {
                        type: 'vue-color',
                        label: '字体颜色',
                        name: 'fontColor',
                        required: false,
                        placeholder: '',
                        value: '#333333'
                    },
                    {
                        type: 'el-select',
                        label: '字体粗细',
                        name: 'fontWeight',
                        required: false,
                        placeholder: '',
                        selectOptions: [
                            {code: 'normal', name: '正常'},
                            {code: 'bold', name: '粗体'}
                        ],
                        value: 'normal'
                    },
                    {
                        type: 'el-switch',
                        label: '启用动画',
                        name: 'enableAnimation',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-input-number',
                        label: '动画时长(ms)',
                        name: 'animationDuration',
                        required: false,
                        placeholder: '',
                        value: 300,
                        min: 100,
                        max: 1000
                    }
                ]
            }
        ]
    },

    'html-widget': {
        name: 'HTML组件',
        type: 'html',
        defaultConfig: {
            // 基础配置
            layerName: 'HTML组件',
            background: '',

            // HTML配置
            htmlSnippetId: null,
            htmlContent: '',
            htmlTitle: '',

            // 显示配置
            opacity: 100,

            // 边框配置
            borderWidth: 0,
            borderColor: '#cccccc',
            borderStyle: 'solid',
            borderRadius: 0
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: 'HTML组件'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // HTML设置组
            {
                name: 'HTML设置',
                list: [
                    {
                        type: 'html-snippet-select',
                        label: 'HTML样式',
                        name: 'htmlSnippetId',
                        required: false,
                        placeholder: '请选择HTML样式',
                        value: null
                    },

                    {
                        type: 'el-input-number',
                        label: '透明度',
                        name: 'opacity',
                        required: false,
                        placeholder: '',
                        value: 100,
                        min: 0,
                        max: 100
                    }
                ]
            }
        ]
    },

    'video-widget': {
        name: '视频组件',
        type: 'media',
        defaultConfig: {
            // 基础配置
            layerName: '视频组件',
            background: '',

            // 视频配置
            videoUrl: '',
            autoplay: false,
            loop: false,
            muted: true,
            controls: true,
            poster: '',

            // 样式配置
            borderRadius: 0,
            opacity: 100,
            borderWidth: 0,
            borderColor: '#dee2e6',
            shadow: false
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '视频组件'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 视频设置组
            {
                name: '视频设置',
                list: [
                    {
                        type: 'el-input-text',
                        label: '视频URL',
                        name: 'videoUrl',
                        required: true,
                        placeholder: 'https://example.com/video.mp4',
                        value: ''
                    },
                    {
                        type: 'el-input-text',
                        label: '封面图片',
                        name: 'poster',
                        required: false,
                        placeholder: 'https://example.com/poster.jpg',
                        value: ''
                    },
                    {
                        type: 'el-switch',
                        label: '自动播放',
                        name: 'autoplay',
                        required: false,
                        placeholder: '',
                        value: false
                    },
                    {
                        type: 'el-switch',
                        label: '循环播放',
                        name: 'loop',
                        required: false,
                        placeholder: '',
                        value: false
                    },
                    {
                        type: 'el-switch',
                        label: '静音播放',
                        name: 'muted',
                        required: false,
                        placeholder: '',
                        value: true
                    },
                    {
                        type: 'el-switch',
                        label: '显示控制条',
                        name: 'controls',
                        required: false,
                        placeholder: '',
                        value: true
                    }
                ]
            },

            // 样式设置组
            {
                name: '样式设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '圆角大小',
                        name: 'borderRadius',
                        required: false,
                        placeholder: '',
                        value: 0,
                        min: 0,
                        max: 50
                    },
                    {
                        type: 'el-input-number',
                        label: '透明度',
                        name: 'opacity',
                        required: false,
                        placeholder: '',
                        value: 100,
                        min: 0,
                        max: 100
                    },
                    {
                        type: 'el-input-number',
                        label: '边框宽度',
                        name: 'borderWidth',
                        required: false,
                        placeholder: '',
                        value: 0,
                        min: 0,
                        max: 10
                    },
                    {
                        type: 'vue-color',
                        label: '边框颜色',
                        name: 'borderColor',
                        required: false,
                        placeholder: '',
                        value: '#dee2e6'
                    },
                    {
                        type: 'el-switch',
                        label: '阴影效果',
                        name: 'shadow',
                        required: false,
                        placeholder: '',
                        value: false
                    }
                ]
            }
        ]
    },

    'column-percentage-chart': {
        name: '柱状百分比图',
        type: 'chart',
        defaultConfig: {
            // 基础配置
            layerName: '柱状百分比图',
            background: '',

            // 标题配置
            isShowTitle: true,
            titleText: '柱状百分比图',
            titleColor: '#333333',
            titleFontSize: 16,
            titlePosition: 'center',

            // 柱状百分比图特有配置
            maxValue: 100,
            borderRadius: 8,
            barWidth: 40,
            orientation: 'vertical', // 显示方向：vertical(纵向) | horizontal(横向)

            // 渐变色配置
            useGradient: true,
            startColor: '#4facfe',
            endColor: '#00f2fe',

            // 阴影配置
            enableShadow: true,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10,

            // 数值标签配置
            showDataLabel: true,
            dataLabelPosition: 'center',
            dataLabelColor: '#333333',
            dataLabelFontSize: 14,
            showPercentage: true,

            // 目标值配置
            enableTarget: false,
            targetSource: 'manual',
            targetValue: 80,
            targetDevice: '',
            targetDataItem: '',
            targetLineColor: '#ff4757',
            targetLineWidth: 2,
            showColumnTargetLabel: false,

            // 柱状背景配置
            showBarBackground: true,
            barBackgroundColor: '#f5f5f5',
            barBackgroundOpacity: 100,

            // 超额显示配置
            allowOverflow: true,

            // 主题配置
            theme: 'default'
        },
        configOptions: [
            // 基础设置
            {
                type: 'el-input-text',
                label: '图层名称',
                name: 'layerName',
                required: false,
                placeholder: '',
                value: '柱状百分比图'
            },
            {
                type: 'vue-color',
                label: '背景颜色',
                name: 'background',
                required: false,
                placeholder: '',
                value: ''
            },

            // 数值设置组
            {
                name: '数值设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '最大值',
                        name: 'maxValue',
                        required: false,
                        placeholder: '',
                        value: 100
                    },
                    {
                        type: 'el-switch',
                        label: '显示百分比',
                        name: 'showPercentage',
                        required: false,
                        value: true
                    },
                    {
                        type: 'el-switch',
                        label: '允许超额显示',
                        name: 'allowOverflow',
                        required: false,
                        value: true
                    }
                ]
            },

            // 样式设置组
            {
                name: '样式设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '柱子宽度',
                        name: 'barWidth',
                        required: false,
                        placeholder: '',
                        value: 40
                    },
                    {
                        type: 'el-select',
                        label: '显示方向',
                        name: 'orientation',
                        required: false,
                        value: 'vertical',
                        selectOptions: [
                            { label: '纵向', value: 'vertical' },
                            { label: '横向', value: 'horizontal' }
                        ]
                    },
                    {
                        type: 'el-input-number',
                        label: '圆角大小',
                        name: 'borderRadius',
                        required: false,
                        placeholder: '',
                        value: 8
                    },
                    {
                        type: 'el-switch',
                        label: '启用渐变',
                        name: 'useGradient',
                        required: false,
                        value: true
                    },
                    {
                        type: 'vue-color',
                        label: '起始颜色',
                        name: 'startColor',
                        required: false,
                        placeholder: '',
                        value: '#4facfe'
                    },
                    {
                        type: 'vue-color',
                        label: '结束颜色',
                        name: 'endColor',
                        required: false,
                        placeholder: '',
                        value: '#00f2fe'
                    }
                ]
            },

            // 边框设置组
            {
                name: '边框设置',
                list: [
                    {
                        type: 'el-input-number',
                        label: '边框宽度',
                        name: 'borderWidth',
                        required: false,
                        placeholder: '',
                        value: 2
                    },
                    {
                        type: 'vue-color',
                        label: '边框颜色',
                        name: 'borderColor',
                        required: false,
                        placeholder: '',
                        value: '#e0e0e0'
                    }
                ]
            },

            // 数据标签设置组
            {
                name: '数据标签',
                list: [
                    {
                        type: 'el-switch',
                        label: '显示数据标签',
                        name: 'showDataLabel',
                        required: false,
                        value: true
                    },
                    {
                        type: 'el-select',
                        label: '标签位置',
                        name: 'dataLabelPosition',
                        required: false,
                        options: [
                            { label: '顶部', value: 'top' },
                            { label: '中间', value: 'inside' },
                            { label: '底部', value: 'bottom' }
                        ],
                        value: 'top'
                    },
                    {
                        type: 'vue-color',
                        label: '标签颜色',
                        name: 'dataLabelColor',
                        required: false,
                        placeholder: '',
                        value: '#333333'
                    },
                    {
                        type: 'el-input-number',
                        label: '标签字体大小',
                        name: 'dataLabelFontSize',
                        required: false,
                        placeholder: '',
                        value: 14
                    }
                ]
            },

            // 目标值设置组
            {
                name: '目标值设置',
                list: [
                    {
                        type: 'el-switch',
                        label: '启用目标值',
                        name: 'enableTarget',
                        required: false,
                        value: false
                    },
                    {
                        type: 'el-select',
                        label: '目标值来源',
                        name: 'targetSource',
                        required: false,
                        options: [
                            { label: '手动输入', value: 'manual' },
                            { label: '监控项数据', value: 'dataItem' }
                        ],
                        value: 'manual'
                    },
                    {
                        type: 'el-input-number',
                        label: '目标值',
                        name: 'targetValue',
                        required: false,
                        placeholder: '',
                        value: 80
                    },
                    {
                        type: 'vue-color',
                        label: '目标线颜色',
                        name: 'targetLineColor',
                        required: false,
                        placeholder: '',
                        value: '#ff4757'
                    },
                    {
                        type: 'el-input-number',
                        label: '目标线宽度',
                        name: 'targetLineWidth',
                        required: false,
                        placeholder: '',
                        value: 2,
                        min: 1,
                        max: 10
                    },
                    {
                        type: 'el-switch',
                        label: '显示目标值标签',
                        name: 'showColumnTargetLabel',
                        required: false,
                        value: false
                    }
                ]
            },

            // 柱状背景设置组
            {
                name: '柱状背景',
                list: [
                    {
                        type: 'el-switch',
                        label: '显示柱状背景',
                        name: 'showBarBackground',
                        required: false,
                        value: true
                    },
                    {
                        type: 'vue-color',
                        label: '柱状背景颜色',
                        name: 'barBackgroundColor',
                        required: false,
                        placeholder: '',
                        value: '#f5f5f5'
                    },
                    {
                        type: 'el-input-number',
                        label: '背景透明度(%)',
                        name: 'barBackgroundOpacity',
                        required: false,
                        placeholder: '',
                        value: 100,
                        min: 0,
                        max: 100
                    }
                ]
            }
        ]
    }
};

// 获取组件默认配置
function getWidgetDefaultConfig(type) {
    const config = widgetConfigs[type];
    if (!config) {
        console.warn('未找到组件配置:', type);
        return {};
    }
    return JSON.parse(JSON.stringify(config.defaultConfig));
}

// 获取组件配置选项
function getWidgetConfigOptions(type) {
    const config = widgetConfigs[type];
    if (!config) {
        console.warn('未找到组件配置选项:', type);
        return [];
    }
    return config.configOptions;
}

// 获取组件名称
function getWidgetName(type) {
    const config = widgetConfigs[type];
    return config ? config.name : type;
}
