<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'预览大屏 - ' + ${dashboard.name} + ' - 胜大PLC管理系统'">预览大屏</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/bootstrap-icons.min.css" rel="stylesheet">
    <link href="/css/bi-status-indicator.css?v=20250730-enhanced-v2" rel="stylesheet">
    <style>
        body {
            margin: 0; padding: 0; overflow: hidden;
            background: #000; color: white; font-family: 'Microsoft YaHei', sans-serif;
        }
        .preview-container {
            width: 100vw; height: 100vh; position: relative;
            display: flex; align-items: center; justify-content: center;
            overflow: hidden;
        }
        .dashboard-canvas {
            position: relative; background: #ffffff;
            transform-origin: 0 0; box-shadow: 0 0 20px rgba(255,255,255,0.1);
            background-position: 0% 0%;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-attachment: initial;
            background-origin: initial;
            background-clip: initial;
        }
        .widget {
            position: absolute; border: none; background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: default; min-width: 100px; min-height: 80px;
        }
        .widget-header {
            background: #f8f9fa; padding: 0.25rem 0.5rem; font-size: 0.875rem;
            border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center;
        }
        .widget-content {
            padding: 0.5rem; height: calc(100% - 32px);
        }
        .chart-container { width: 100%; height: 100%; }
        .data-value {
            font-size: 2rem; font-weight: bold; color: #0d6efd;
            text-align: center; line-height: 1.2;
        }
        .data-label {
            font-size: 0.9rem; color: #6c757d;
            text-align: center; margin-top: 0.5rem;
        }
        .loading-indicator {
            display: flex; align-items: center; justify-content: center;
            color: #6c757d; font-size: 0.9rem;
        }
        .loading-spinner {
            width: 1rem; height: 1rem; border: 2px solid #f3f3f3;
            border-top: 2px solid #0d6efd; border-radius: 50%;
            animation: spin 1s linear infinite; margin-right: 0.5rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .control-panel {
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: rgba(0,0,0,0.8); border-radius: 8px; padding: 1rem;
        }
        .control-panel button {
            margin: 0.25rem; background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3); color: white;
        }
        .control-panel button:hover {
            background: rgba(255,255,255,0.2);
        }
        .fullscreen-mode .control-panel {
            background: rgba(0,0,0,0.5);
        }
        .status-indicator {
            position: fixed; bottom: 20px; left: 20px; z-index: 1000;
            background: rgba(0,0,0,0.8); border-radius: 8px; padding: 0.5rem 1rem;
            font-size: 0.8rem; color: #28a745;
        }
        .error-message {
            color: #dc3545; text-align: center; padding: 2rem;
            background: rgba(220,53,69,0.1); border-radius: 8px; margin: 1rem;
        }

        /* 表格组件样式（完整复刻设计器实现） */
        .table-container {
            transition: all 0.3s ease;
        }

        .table-container.has-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .table-container.has-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .table-container.has-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .table-container.has-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 无滚动条时隐藏滚动条 */
        .table-container:not(.has-scrollbar)::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
    </style>
</head>
<body>
    <div class="preview-container" id="previewContainer">
        <!-- 控制面板 -->
        <div class="control-panel">
            <button class="btn btn-sm" onclick="toggleFullscreen()" title="全屏切换">
                <i class="bi bi-fullscreen"></i>
            </button>
            <button class="btn btn-sm" onclick="refreshData()" title="刷新数据">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-sm" onclick="goBack()" title="返回设计">
                <i class="bi bi-arrow-left"></i>
            </button>
        </div>

        <!-- 状态指示器 -->
        <div class="status-indicator" id="statusIndicator">
            <i class="bi bi-wifi"></i> 实时连接
        </div>

        <!-- 大屏画布 -->
        <div class="dashboard-canvas" id="dashboardCanvas">
            <!-- 组件将动态加载到这里 -->
            <div class="loading-indicator" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <div class="loading-spinner"></div>
                正在加载大屏数据...
            </div>
        </div>
    </div>

    <!-- 隐藏的数据 -->
    <script th:inline="javascript">
        window.dashboardData = {
            id: /*[[${dashboard.id}]]*/ 0,
            name: /*[[${dashboard.name}]]*/ '',
            canvasConfig: /*[[${dashboard.canvasConfig}]]*/ '{"width":1920,"height":1080,"backgroundColor":"#ffffff"}',
            widgets: /*[[${dashboard.widgets}]]*/ []
        };
    </script>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- Apache ECharts -->
    <script src="/js/echarts.min.js"></script>
    <!-- ECharts LiquidFill Plugin for Water Charts -->
    <script src="/js/echarts-liquidfill.min.js"></script>
    <script src="/js/bi-echarts-components.js"></script>
    <!-- 数据源管理器 - 与设计器保持一致 -->
    <script src="/js/bi-data-source-manager.js"></script>
    <!-- 状态指示器组件 -->
    <script src="/js/bi-status-indicator.js?v=20250730-enhanced-v2"></script>
    <script>
        let charts = {}; // ECharts实例存储
        let refreshIntervals = {};
        let isFullscreen = false;

        // 存储组件实例，用于数据刷新
        window.widgetInstances = {};

        // 防抖函数
        function debounce(fn, delay) {
            let timer = null;
            return function(...args) {
                if (timer) {
                    clearTimeout(timer);
                }
                const context = this;
                timer = setTimeout(() => {
                    fn.apply(context, args);
                }, delay);
            };
        }

        // 初始化预览
        document.addEventListener('DOMContentLoaded', function() {
            // 检查ECharts是否加载
            if (typeof echarts === 'undefined') {
                console.error('ECharts库未加载！');
                const canvas = document.getElementById('dashboardCanvas');
                canvas.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        <h4>ECharts库加载失败</h4>
                        <p>图表组件可能无法正常工作</p>
                    </div>
                `;
                return;
            } else {
                console.log('ECharts库加载成功，版本:', echarts.version);
            }

            // 初始化数据源管理器
            initializeDataSourceManager();

            initializePreview();
            setupAutoRefresh();
        });

        // 初始化数据源管理器（标准化版本）
        function initializeDataSourceManager() {
            console.log('预览页面 - 初始化数据源管理器');

            // 使用标准化初始化函数
            if (typeof initializeBiDataSourceManager === 'function') {
                const initResult = initializeBiDataSourceManager({
                    enableLogging: true,
                    enableValidation: true,
                    enableContextManagement: false // 预览页面不需要复杂的上下文管理
                });

                if (initResult.success) {
                    console.log('预览页面 - BiDataSourceManager标准化初始化成功');
                } else {
                    console.error('预览页面 - BiDataSourceManager标准化初始化失败:', initResult.message);
                    // 降级处理
                    this.fallbackInitialization();
                }
            } else {
                console.warn('预览页面 - initializeBiDataSourceManager函数未定义，使用降级初始化');
                this.fallbackInitialization();
            }

            // 验证初始化结果
            if (typeof checkBiDataSourceManagerAvailability === 'function') {
                const availability = checkBiDataSourceManagerAvailability();
                console.log('预览页面 - BiDataSourceManager可用性检查:', availability);

                if (!availability.isInstanceAvailable) {
                    console.error('预览页面 - BiDataSourceManager实例不可用，尝试降级初始化');
                    this.fallbackInitialization();
                }
            }
        }

        // 降级初始化方法
        function fallbackInitialization() {
            try {
                if (typeof BiDataSourceManager !== 'undefined') {
                    if (!window.biDataSourceManager) {
                        window.biDataSourceManager = new BiDataSourceManager();
                        console.log('预览页面 - BiDataSourceManager降级初始化成功');
                    }
                } else {
                    console.error('预览页面 - BiDataSourceManager类未定义，请检查bi-data-source-manager.js是否正确加载');
                }
            } catch (error) {
                console.error('预览页面 - BiDataSourceManager降级初始化失败:', error);
            }
        }

        // 初始化预览
        function initializePreview() {
            const canvas = document.getElementById('dashboardCanvas');
            const canvasConfig = JSON.parse(window.dashboardData.canvasConfig);

            // 设置画布尺寸
            canvas.style.width = canvasConfig.width + 'px';
            canvas.style.height = canvasConfig.height + 'px';

            // 设置画布背景
            setCanvasBackground(canvas, canvasConfig);

            // 计算缩放比例并应用
            applyCanvasScale(canvas, canvasConfig);

            // 加载组件
            loadWidgets();
        }

        // 设置画布背景
        function setCanvasBackground(canvas, config) {
            // 清除现有背景
            canvas.style.background = '';
            canvas.style.backgroundColor = '';
            canvas.style.backgroundImage = '';

            switch(config.backgroundType) {
                case 'color':
                    canvas.style.backgroundColor = config.backgroundColor || '#ffffff';
                    break;

                case 'gradient':
                    const direction = config.gradientDirection || 'to bottom';
                    const startColor = config.gradientStartColor || '#667eea';
                    const endColor = config.gradientEndColor || '#764ba2';
                    canvas.style.backgroundImage = `linear-gradient(${direction}, ${startColor}, ${endColor})`;
                    break;

                case 'image':
                    if (config.backgroundImage) {
                        const opacity = (config.backgroundImageOpacity || 100) / 100;
                        const mode = config.backgroundImageMode || 'cover';

                        if (opacity < 1) {
                            canvas.style.backgroundImage = `linear-gradient(rgba(255,255,255,${1-opacity}), rgba(255,255,255,${1-opacity})), url(${config.backgroundImage})`;
                        } else {
                            canvas.style.backgroundImage = `url(${config.backgroundImage})`;
                        }

                        switch(mode) {
                            case 'cover':
                                canvas.style.backgroundSize = 'cover';
                                canvas.style.backgroundPosition = 'center';
                                break;
                            case 'contain':
                                canvas.style.backgroundSize = 'contain';
                                canvas.style.backgroundPosition = 'center';
                                break;
                            case 'stretch':
                                canvas.style.backgroundSize = '100% 100%';
                                canvas.style.backgroundPosition = 'center';
                                break;
                            case 'repeat':
                                canvas.style.backgroundSize = 'auto';
                                canvas.style.backgroundPosition = 'top left';
                                canvas.style.backgroundRepeat = 'repeat';
                                break;
                            case 'center':
                                canvas.style.backgroundSize = 'auto';
                                canvas.style.backgroundPosition = 'center';
                                break;
                        }
                    } else {
                        canvas.style.backgroundColor = config.backgroundColor || '#ffffff';
                    }
                    break;

                default:
                    canvas.style.backgroundColor = config.backgroundColor || '#ffffff';
            }
        }

        // 应用画布缩放
        function applyCanvasScale(canvas, config) {
            const scale = calculateScale(config.width, config.height);
            canvas.style.transform = `scale(${scale})`;

            // 居中显示
            const container = document.getElementById('previewContainer');
            const scaledWidth = config.width * scale;
            const scaledHeight = config.height * scale;

            canvas.style.position = 'absolute';
            canvas.style.left = '50%';
            canvas.style.top = '50%';
            canvas.style.marginLeft = `-${scaledWidth / 2}px`;
            canvas.style.marginTop = `-${scaledHeight / 2}px`;
        }

        // 计算缩放比例
        function calculateScale(canvasWidth, canvasHeight) {
            const containerWidth = window.innerWidth;
            const containerHeight = window.innerHeight;

            const scaleX = containerWidth / canvasWidth;
            const scaleY = containerHeight / canvasHeight;

            return Math.min(scaleX, scaleY, 1);
        }

        // 加载组件
        function loadWidgets() {
            const canvas = document.getElementById('dashboardCanvas');
            canvas.innerHTML = '';

            console.log('开始加载组件数据...');

            // 从服务器获取组件数据
            fetch(`/api/bi/dashboard/${window.dashboardData.id}/widgets`)
            .then(response => {
                console.log('组件数据响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('组件数据响应:', data);

                if (data.success && data.data && data.data.length > 0) {
                    console.log(`获取到 ${data.data.length} 个组件`);

                    // 按zIndex升序排序，确保高zIndex的组件后渲染（显示在上层）
                    const sortedWidgets = data.data.sort((a, b) => {
                        const aZIndex = a.zIndex || 1000;
                        const bZIndex = b.zIndex || 1000;
                        return aZIndex - bZIndex;
                    });

                    console.log('预览页面 - 组件按zIndex排序:', sortedWidgets.map(w => ({
                        id: w.id,
                        type: w.widgetType,
                        zIndex: w.zIndex || 1000
                    })));

                    sortedWidgets.forEach(widgetData => {
                        console.log('处理组件数据:', widgetData);

                        // 处理样式配置：优先使用setup字段，回退到styleConfig字段（与设计器保持一致）
                        let styleConfig = '{}';
                        if (widgetData.setup) {
                            // 从setup字段解析样式配置
                            try {
                                const setup = typeof widgetData.setup === 'string' ? JSON.parse(widgetData.setup) : widgetData.setup;
                                // 分离基础配置和样式配置
                                const { title, chartType, ...extractedStyleConfig } = setup;
                                styleConfig = JSON.stringify(extractedStyleConfig);
                                console.log(`预览页面 - 组件 ${widgetData.id} 从setup字段提取样式配置:`, extractedStyleConfig);
                            } catch (error) {
                                console.warn(`预览页面 - 解析组件 ${widgetData.id} setup字段失败:`, error);
                                styleConfig = widgetData.styleConfig || '{}';
                            }
                        } else {
                            styleConfig = widgetData.styleConfig || '{}';
                        }

                        console.log(`预览页面 - 组件 ${widgetData.id} 样式配置详情:`, {
                            hasSetup: !!widgetData.setup,
                            hasStyleConfig: !!widgetData.styleConfig,
                            setupContent: widgetData.setup,
                            finalStyleConfig: styleConfig
                        });

                        // 精确处理zIndex，优先从setup字段提取（与设计器保持一致）
                        let zIndex;
                        let zIndexSource = 'default';

                        // 1. 优先从setup字段中提取zIndex
                        if (widgetData.setup) {
                            try {
                                const setup = typeof widgetData.setup === 'string' ? JSON.parse(widgetData.setup) : widgetData.setup;
                                if (setup.zIndex !== null && setup.zIndex !== undefined) {
                                    zIndex = setup.zIndex;
                                    zIndexSource = 'setup';
                                    console.log(`预览页面 - 组件 ${widgetData.id} 从setup字段提取zIndex: ${zIndex}`);
                                }
                            } catch (error) {
                                console.warn(`预览页面 - 解析组件 ${widgetData.id} setup字段失败:`, error);
                            }
                        }

                        // 2. 如果setup中没有，检查数据库zIndex字段
                        if (zIndex === undefined && widgetData.zIndex !== null && widgetData.zIndex !== undefined) {
                            zIndex = widgetData.zIndex;
                            zIndexSource = 'database';
                            console.log(`预览页面 - 组件 ${widgetData.id} 使用数据库zIndex字段: ${zIndex}`);
                        }

                        // 3. 最后使用默认值
                        if (zIndex === undefined) {
                            zIndex = 1000 + widgetData.id;
                            zIndexSource = 'default';
                            console.log(`预览页面 - 组件 ${widgetData.id} 使用默认zIndex: ${zIndex}`);
                        }

                        console.log(`预览页面 - 组件 ${widgetData.id} zIndex最终值: ${zIndex} (来源: ${zIndexSource})`);

                        const widget = {
                            id: widgetData.id,
                            widgetType: widgetData.widgetType,
                            positionX: widgetData.positionX,
                            positionY: widgetData.positionY,
                            width: widgetData.width,
                            height: widgetData.height,
                            zIndex: zIndex,
                            config: widgetData.config || '{}',
                            styleConfig: styleConfig,
                            dataSourceConfig: widgetData.dataSourceConfig || '{}'
                        };

                        // 解析基础配置（优先从setup字段提取title等基础配置）
                        let baseConfig = {};
                        if (widgetData.setup) {
                            try {
                                const setup = typeof widgetData.setup === 'string' ? JSON.parse(widgetData.setup) : widgetData.setup;
                                // 提取基础配置
                                baseConfig = {
                                    title: setup.title || '',
                                    chartType: setup.chartType || widget.widgetType
                                };
                                console.log(`预览页面 - 组件 ${widgetData.id} 从setup字段提取基础配置:`, baseConfig);
                            } catch (error) {
                                console.warn(`预览页面 - 解析组件 ${widgetData.id} setup字段失败:`, error);
                            }
                        }

                        // 解析config字段（如果是字符串）
                        if (typeof widget.config === 'string') {
                            try {
                                const configObj = JSON.parse(widget.config);
                                // 合并setup中的基础配置和config配置
                                widget.config = { ...configObj, ...baseConfig };
                            } catch (e) {
                                console.warn('解析组件配置失败:', e);
                                widget.config = baseConfig;
                            }
                        } else {
                            // 合并基础配置
                            widget.config = { ...widget.config, ...baseConfig };
                        }

                        if (typeof widget.styleConfig === 'string') {
                            try {
                                widget.styleConfig = JSON.parse(widget.styleConfig);
                            } catch (e) {
                                console.warn('解析样式配置失败:', e);
                                widget.styleConfig = {};
                            }
                        }

                        if (typeof widget.dataSourceConfig === 'string') {
                            try {
                                widget.dataSourceConfig = JSON.parse(widget.dataSourceConfig);
                            } catch (e) {
                                console.warn('解析数据源配置失败:', e);
                                widget.dataSourceConfig = {};
                            }
                        }

                        // 存储组件实例
                        window.widgetInstances[widget.id] = widget;

                        createWidgetElement(widget);
                    });
                } else {
                    console.log('没有组件数据或数据为空');
                    canvas.innerHTML = `
                        <div class="error-message">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                            <h4>暂无组件</h4>
                            <p>请返回设计器添加组件</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('加载组件失败:', error);
                canvas.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        <h4>加载失败</h4>
                        <p>无法加载组件数据: ${error.message}</p>
                    </div>
                `;
            });
        }

        // 创建组件元素
        function createWidgetElement(widget) {
            const canvas = document.getElementById('dashboardCanvas');
            const widgetElement = document.createElement('div');
            widgetElement.className = 'widget';
            widgetElement.id = `widget-${widget.id}`;
            widgetElement.style.left = widget.positionX + 'px';
            widgetElement.style.top = widget.positionY + 'px';
            widgetElement.style.width = widget.width + 'px';
            widgetElement.style.height = widget.height + 'px';

            // 设置图层顺序（zIndex）
            const zIndex = widget.zIndex || 1000;
            widgetElement.style.zIndex = zIndex;
            console.log(`预览页面 - 组件 ${widget.id} 设置图层顺序: ${zIndex}`);

            const title = widget.config.title || getDefaultTitle(widget.widgetType);

            widgetElement.innerHTML = `
                <div class="widget-header">
                    <span>${title}</span>
                </div>
                <div class="widget-content" id="content-${widget.id}">
                    <div class="loading-indicator">
                        <div class="loading-spinner"></div>
                        加载中...
                    </div>
                </div>
            `;

            canvas.appendChild(widgetElement);

            // 应用样式配置
            applyWidgetStyleConfig(widget, widgetElement);

            // 初始化组件内容（使用真实数据）
            setTimeout(() => {
                initializeWidgetContentWithData(widget);
            }, 100);
        }

        // 获取默认标题
        function getDefaultTitle(widgetType) {
            const titles = {
                'line-chart': '折线图',
                'bar-chart': '柱状图',
                'horizontal-bar-chart': '水平柱状图',
                'pie-chart': '饼图',
                'gauge-chart': '仪表盘',
                'water-chart': '水波图',
                'data-table': '数据表格',
                'text-label': '文本标签',
                'image-widget': '图片组件',
                'decoration-widget': '装饰组件',
                'html-widget': 'HTML组件',
                'status-indicator': '状态指示器'
            };
            return titles[widgetType] || '未知组件';
        }

        // 应用组件样式配置（完整复制设计器实现）
        function applyWidgetStyleConfig(widget, widgetElement) {
            if (!widget || !widgetElement) return;

            try {
                // 修复重复解析问题：widget.styleConfig在前面已经被解析为对象
                const styleConfig = (typeof widget.styleConfig === 'string') ?
                    JSON.parse(widget.styleConfig) : (widget.styleConfig || {});
                console.log(`=== 预览页面 - 应用组件 ${widget.id} 样式配置 ===`);
                console.log('样式配置类型:', typeof widget.styleConfig);
                console.log('样式配置原始值:', widget.styleConfig);
                console.log('解析后样式配置:', styleConfig);

                // 1. 处理透明背景（精准修复CSS覆盖问题）
                if (styleConfig.transparent === true) {
                    // 使用setProperty和!important确保样式优先级
                    widgetElement.style.setProperty('background-color', 'transparent', 'important');
                    widgetElement.style.setProperty('background', 'transparent', 'important');
                    widgetElement.style.setProperty('box-shadow', 'none', 'important');
                    console.log(`组件 ${widget.id} 应用透明背景（强制优先级）`);
                } else {
                    // 恢复默认背景
                    widgetElement.style.removeProperty('background-color');
                    widgetElement.style.removeProperty('background');
                    widgetElement.style.removeProperty('box-shadow');
                }

                // 2. 处理显示标题栏（精准修复状态恢复问题）
                const headerElement = widgetElement.querySelector('.widget-header');
                if (headerElement) {
                    if (styleConfig.showTitle === false) {
                        headerElement.style.display = 'none';
                        console.log(`组件 ${widget.id} 隐藏标题栏`);
                    } else {
                        headerElement.style.display = 'flex';
                        // 更新标题内容
                        const headerSpan = headerElement.querySelector('span');
                        if (headerSpan && widget.config && widget.config.title) {
                            headerSpan.textContent = widget.config.title;
                        }
                        console.log(`组件 ${widget.id} 显示标题栏`);
                    }
                }

                // 3. 处理其他样式配置
                if (styleConfig.backgroundColor && styleConfig.transparent !== true) {
                    widgetElement.style.backgroundColor = styleConfig.backgroundColor;
                    console.log(`组件 ${widget.id} 应用背景色: ${styleConfig.backgroundColor}`);
                }

                // 4. 处理边框配置（默认不显示边框）
                // 特殊处理：水波图的边框配置仅用于ECharts内部，不应用到组件容器
                if (widget.widgetType === 'water-chart') {
                    // 水波图强制无边框，边框由ECharts内部的outline控制
                    widgetElement.style.border = 'none';
                    console.log(`组件 ${widget.id} 水波图强制无边框`);
                } else if (styleConfig.borderWidth !== undefined && styleConfig.borderColor) {
                    if (styleConfig.borderWidth > 0) {
                        widgetElement.style.border = `${styleConfig.borderWidth}px solid ${styleConfig.borderColor}`;
                    } else {
                        widgetElement.style.border = 'none';
                    }
                    console.log(`组件 ${widget.id} 应用边框样式: ${styleConfig.borderWidth}px solid ${styleConfig.borderColor}`);
                } else {
                    // 确保默认无边框
                    widgetElement.style.border = 'none';
                }

                if (styleConfig.borderRadius !== undefined) {
                    widgetElement.style.borderRadius = styleConfig.borderRadius + 'px';
                    console.log(`组件 ${widget.id} 应用圆角: ${styleConfig.borderRadius}px`);
                }

                if (styleConfig.opacity !== undefined) {
                    const opacityValue = styleConfig.opacity / 100;
                    widgetElement.style.opacity = opacityValue;
                    console.log(`组件 ${widget.id} 应用透明度: ${opacityValue}`);
                }

                // 5. 处理标题栏样式
                if (headerElement && styleConfig.showTitle !== false) {
                    if (styleConfig.titleBackgroundColor) {
                        headerElement.style.backgroundColor = styleConfig.titleBackgroundColor;
                        console.log(`组件 ${widget.id} 应用标题背景色: ${styleConfig.titleBackgroundColor}`);
                    }
                    if (styleConfig.titleColor) {
                        headerElement.style.color = styleConfig.titleColor;
                        console.log(`组件 ${widget.id} 应用标题文字颜色: ${styleConfig.titleColor}`);
                    }
                    if (styleConfig.titleFontSize) {
                        headerElement.style.fontSize = styleConfig.titleFontSize + 'px';
                        console.log(`组件 ${widget.id} 应用标题字体大小: ${styleConfig.titleFontSize}px`);
                    }
                }

                // 6. 处理内容区域样式
                const widgetContent = widgetElement.querySelector('.widget-content');
                if (widgetContent) {
                    if (styleConfig.contentBackgroundColor) {
                        widgetContent.style.backgroundColor = styleConfig.contentBackgroundColor;
                        console.log(`组件 ${widget.id} 应用内容背景色: ${styleConfig.contentBackgroundColor}`);
                    }
                    if (styleConfig.contentPadding !== undefined) {
                        widgetContent.style.padding = styleConfig.contentPadding + 'px';
                        console.log(`组件 ${widget.id} 应用内容内边距: ${styleConfig.contentPadding}px`);
                    }
                }

                console.log(`=== 预览页面 - 组件 ${widget.id} 样式配置应用完成 ===`);

            } catch (error) {
                console.error(`预览页面 - 应用组件 ${widget.id} 样式配置失败:`, error);
            }
        }

        // 使用真实数据初始化组件内容
        function initializeWidgetContentWithData(widget) {
            console.log(`预览页面 - 初始化组件 ${widget.id} 内容，类型: ${widget.widgetType}`);

            const contentElement = document.getElementById(`content-${widget.id}`);
            if (!contentElement) {
                console.error(`预览页面 - 找不到组件 ${widget.id} 的内容容器`);
                return;
            }

            // 获取数据并渲染组件
            fetchWidgetDataAndRender(widget, contentElement);
        }

        // 获取组件数据并渲染（简化版，参照设计页面）
        async function fetchWidgetDataAndRender(widget, contentElement) {
            try {
                console.log(`预览页面 - 开始获取组件 ${widget.id} 数据`);

                // 直接使用数据源管理器获取数据（与设计页面一致）
                const data = window.biDataSourceManager ?
                    await window.biDataSourceManager.fetchWidgetData(widget) :
                    { success: false, error: '数据源管理器未初始化' };

                console.log(`预览页面 - 组件 ${widget.id} 数据获取结果:`, data);

                if (data.success) {
                    // 根据组件类型渲染（简化处理）
                    renderWidgetWithData(widget, contentElement, data);
                } else {
                    console.error('预览页面 - 数据获取失败:', data.error);
                    // 使用示例数据作为回退
                    const fallbackData = getExampleData(widget.widgetType);
                    renderWidgetWithData(widget, contentElement, fallbackData);
                }

            } catch (error) {
                console.error(`预览页面 - 组件 ${widget.id} 数据获取失败:`, error);
                contentElement.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>数据加载失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 根据数据渲染组件
        function renderWidgetWithData(widget, contentElement, data) {
            console.log(`预览页面 - 渲染组件 ${widget.id}，类型: ${widget.widgetType}`);

            switch (widget.widgetType) {
                case 'line-chart':
                    renderEChartsLineChart(widget, contentElement, data);
                    break;
                case 'multi-line-chart':
                    renderEChartsMultiLineChart(widget, contentElement, data);
                    break;
                case 'bar-chart':
                    renderEChartsBarChart(widget, contentElement, data);
                    break;
                case 'horizontal-bar-chart':
                    renderEChartsHorizontalBarChart(widget, contentElement, data);
                    break;
                case 'pie-chart':
                    renderEChartsPieChart(widget, contentElement, data);
                    break;
                case 'gauge-chart':
                    renderEChartsGauge(widget, contentElement, data);
                    break;
                case 'water-chart':
                    renderEChartsWaterChart(widget, contentElement, data);
                    break;
                case 'column-percentage-chart':
                    renderEChartsColumnPercentageChart(widget, contentElement, data);
                    break;
                case 'data-table':
                    renderDataTable(widget, contentElement, data);
                    break;
                case 'text-label':
                    renderTextLabel(widget, contentElement, data);
                    break;
                case 'image-widget':
                    renderImageWidget(widget, contentElement, data);
                    break;
                case 'hyperlink-widget':
                    renderHyperlinkWidget(widget, contentElement, data);
                    break;
                case 'decoration-widget':
                    renderDecorationWidget(widget, contentElement, data);
                    break;
                case 'html-widget':
                    renderHtmlWidget(widget, contentElement, data);
                    break;
                case 'video-widget':
                    renderVideoWidget(widget, contentElement, data);
                    break;
                case 'time-widget':
                    renderTimeWidget(widget, contentElement, data);
                    break;
                case 'status-indicator':
                    renderStatusIndicator(widget, contentElement, data);
                    break;
                default:
                    contentElement.innerHTML = `<div class="error-message">不支持的组件类型: ${widget.widgetType}</div>`;
            }
        }

        // 渲染ECharts折线图
        function renderEChartsLineChart(widget, container, data) {
            console.log(`预览页面 - 渲染折线图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 合并基础配置和样式配置
                const mergedConfig = {
                    ...widget.config,
                    ...widget.styleConfig
                };
                console.log(`预览页面 - 折线图组件 ${widget.id} 合并配置:`, mergedConfig);

                // 使用ECharts组件系统创建折线图
                const chart = createEChartsLineChart(chartId, data, mergedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 折线图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts折线图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 折线图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染ECharts多折线图
        function renderEChartsMultiLineChart(widget, container, data) {
            console.log(`预览页面 - 渲染多折线图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 合并基础配置和样式配置
                const mergedConfig = {
                    ...widget.config,
                    ...widget.styleConfig
                };
                console.log(`预览页面 - 多折线图组件 ${widget.id} 合并配置:`, mergedConfig);

                // 使用ECharts组件系统创建多折线图
                const chart = createEChartsMultiLineChart(chartId, data, mergedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 多折线图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts多折线图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 多折线图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染ECharts柱状图
        function renderEChartsBarChart(widget, container, data) {
            console.log(`预览页面 - 渲染柱状图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 合并基础配置和样式配置
                const mergedConfig = {
                    ...widget.config,
                    ...widget.styleConfig
                };
                console.log(`预览页面 - 柱状图组件 ${widget.id} 合并配置:`, mergedConfig);

                // 使用ECharts组件系统创建柱状图
                const chart = createEChartsBarChart(chartId, data, mergedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 柱状图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts柱状图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 柱状图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染ECharts水平柱状图
        function renderEChartsHorizontalBarChart(widget, container, data) {
            console.log(`预览页面 - 渲染水平柱状图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 合并基础配置和样式配置
                const mergedConfig = {
                    ...widget.config,
                    ...widget.styleConfig
                };
                console.log(`预览页面 - 水平柱状图组件 ${widget.id} 合并配置:`, mergedConfig);

                // 使用ECharts组件系统创建水平柱状图
                const chart = createEChartsHorizontalBarChart(chartId, data, mergedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 水平柱状图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts水平柱状图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 水平柱状图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染ECharts饼图
        function renderEChartsPieChart(widget, container, data) {
            console.log(`预览页面 - 渲染饼图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 合并基础配置和样式配置
                const mergedConfig = {
                    ...widget.config,
                    ...widget.styleConfig
                };
                console.log(`预览页面 - 饼图组件 ${widget.id} 合并配置:`, mergedConfig);

                // 使用ECharts组件系统创建饼图
                const chart = createEChartsPieChart(chartId, data, mergedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 饼图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts饼图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 饼图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染ECharts仪表盘（简化版，参照设计页面）
        function renderEChartsGauge(widget, container, data) {
            console.log(`预览页面 - 渲染仪表盘组件 ${widget.id}，数据:`, data);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                // 获取配置（与设计页面一致）
                const config = getPreviewWidgetEChartsConfig(widget);

                // 直接创建图表（与设计页面完全一致的调用方式）
                console.log(`预览页面 - 仪表盘组件 ${widget.id} 最终数据:`, data);
                const chart = createEChartsGauge(chartId, data, config);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 仪表盘组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts仪表盘创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 仪表盘组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染ECharts水波图
        function renderEChartsWaterChart(widget, container, data) {
            console.log(`预览页面 - 渲染水波图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 合并基础配置和样式配置
                const mergedConfig = {
                    ...widget.config,
                    ...widget.styleConfig
                };
                console.log(`预览页面 - 水波图组件 ${widget.id} 基础配置:`, widget.config);
                console.log(`预览页面 - 水波图组件 ${widget.id} 样式配置:`, widget.styleConfig);
                console.log(`预览页面 - 水波图组件 ${widget.id} 合并配置:`, mergedConfig);
                console.log(`预览页面 - 水波图组件 ${widget.id} 波浪幅度参数:`, {
                    waterAmplitude: mergedConfig.waterAmplitude,
                    amplitude: mergedConfig.amplitude,
                    finalAmplitude: mergedConfig.waterAmplitude || mergedConfig.amplitude || 20
                });

                // 使用ECharts组件系统创建水波图
                const chart = createEChartsWaterChart(chartId, data, mergedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 水波图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts水波图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 水波图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 获取柱状百分比图的映射配置（复制自设计器页面）
        function getColumnPercentageChartConfig(widget) {
            const styleConfig = widget.styleConfig || {};
            const dataSourceConfig = widget.dataSourceConfig || {};

            return {
                // 基础配置
                ...widget.config,

                // 标题显示控制（统一字段名）
                isShowTitle: styleConfig.showChartTitle !== false,

                // 从数据源配置中获取目标值配置
                enableColumnTarget: dataSourceConfig.enableColumnTarget === true,
                columnTargetSource: dataSourceConfig.columnTargetSource || 'manual',
                columnTargetValue: dataSourceConfig.columnTargetValue || 100,
                columnTargetDevice: dataSourceConfig.columnTargetDevice || '',
                columnTargetDataItem: dataSourceConfig.columnTargetDataItem || '',
                showColumnTargetLabel: dataSourceConfig.showColumnTargetLabel !== false,

                // 柱状百分比图样式配置
                barWidth: styleConfig.columnBarWidth || 40,
                borderRadius: styleConfig.columnBorderRadius || 8,
                orientation: styleConfig.columnOrientation || 'vertical',
                useGradient: styleConfig.columnUseGradient !== false,
                startColor: styleConfig.columnStartColor || '#4facfe',
                endColor: styleConfig.columnEndColor || '#00f2fe',
                enableShadow: styleConfig.columnEnableShadow !== false,
                shadowColor: styleConfig.columnShadowColor || '#000000',
                shadowBlur: styleConfig.columnShadowBlur || 10,
                showDataLabel: styleConfig.columnShowDataLabel !== false,
                dataLabelPosition: styleConfig.columnDataLabelPosition || 'top',
                dataLabelColor: styleConfig.columnDataLabelColor || '#333333',
                dataLabelFontSize: styleConfig.columnDataLabelFontSize || 14,
                showPercentage: styleConfig.columnShowPercentage !== false,
                showBarBackground: styleConfig.columnShowBarBackground !== false,
                barBackgroundColor: styleConfig.columnBarBackgroundColor || '#f5f5f5',
                allowOverflow: styleConfig.columnAllowOverflow !== false
            };
        }

        // 渲染ECharts柱状百分比图
        function renderEChartsColumnPercentageChart(widget, container, data) {
            console.log(`预览页面 - 渲染柱状百分比图组件 ${widget.id}`);

            const chartId = `echarts-${widget.id}`;
            container.innerHTML = `<div id="${chartId}" style="width: 100%; height: 100%;"></div>`;

            try {
                const chartContainer = document.getElementById(chartId);
                if (!chartContainer) {
                    throw new Error('图表容器创建失败');
                }

                // 使用配置映射函数获取正确的配置
                const mappedConfig = getColumnPercentageChartConfig(widget);
                console.log(`预览页面 - 柱状百分比图组件 ${widget.id} 原始配置:`, widget);
                console.log(`预览页面 - 柱状百分比图组件 ${widget.id} 映射配置:`, mappedConfig);

                // 使用ECharts组件系统创建柱状百分比图
                const chart = createEChartsColumnPercentageChart(chartId, data, mappedConfig);
                if (chart) {
                    charts[widget.id] = chart;
                    console.log(`预览页面 - 柱状百分比图组件 ${widget.id} 创建成功`);
                } else {
                    throw new Error('ECharts柱状百分比图创建失败');
                }
            } catch (error) {
                console.error(`预览页面 - 柱状百分比图组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图表渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        /**
         * 获取表格样式配置（完整复刻设计器实现）
         */
        function getTableStyleConfig(widget) {
            // 从styleConfig中读取表格样式配置
            let styleConfig = {};
            try {
                // 修复JSON解析问题：检查是否已经是对象
                if (typeof widget.styleConfig === 'string') {
                    styleConfig = JSON.parse(widget.styleConfig);
                } else if (typeof widget.styleConfig === 'object' && widget.styleConfig !== null) {
                    styleConfig = widget.styleConfig;
                } else {
                    styleConfig = {};
                }
            } catch (e) {
                console.error('解析表格样式配置失败:', e);
                styleConfig = {};
            }

            return {
                // 基础样式
                tableStyle: styleConfig.tableStyle || 'table',
                tableSize: styleConfig.tableSize || 'table-normal',
                showHeader: styleConfig.showTableHeader !== false,

                // 颜色配置
                headerBgColor: styleConfig.tableHeaderBgColor || '#f8f9fa',
                headerTextColor: styleConfig.tableHeaderTextColor || '#212529',
                bodyBgColor: styleConfig.tableBodyBgColor || '#ffffff',
                bodyTextColor: styleConfig.tableBodyTextColor || '#212529',
                borderColor: styleConfig.tableBorderColor || '#dee2e6',

                // 字体配置
                headerFontSize: styleConfig.tableHeaderFontSize || 14,
                bodyFontSize: styleConfig.tableBodyFontSize || 12,
                fontWeight: styleConfig.tableFontWeight || 'normal',

                // 轮播配置
                enableCarousel: styleConfig.enableTableCarousel || false,
                rowsPerPage: styleConfig.tableRowsPerPage || 5,
                carouselInterval: styleConfig.tableCarouselInterval || 3,
                showPagination: styleConfig.showTablePagination || false
            };
        }

        /**
         * 获取表格字段配置（完整复刻设计器实现）
         */
        function getTableFieldsFromWidget(widget) {
            // 修复JSON解析问题：检查是否已经是对象
            let dataSourceConfig = {};
            try {
                if (typeof widget.dataSourceConfig === 'string') {
                    dataSourceConfig = JSON.parse(widget.dataSourceConfig);
                } else if (typeof widget.dataSourceConfig === 'object' && widget.dataSourceConfig !== null) {
                    dataSourceConfig = widget.dataSourceConfig;
                } else {
                    dataSourceConfig = {};
                }
            } catch (e) {
                console.error('解析数据源配置失败:', e);
                dataSourceConfig = {};
            }

            if (dataSourceConfig.dataSourceType === 'externalData' && dataSourceConfig.tableFields) {
                return dataSourceConfig.tableFields;
            }

            return [];
        }

        /**
         * 获取列名显示（完整复刻设计器实现）
         */
        function getColumnName(key) {
            const columnNames = {
                'deviceName': '设备名称',
                'address': '地址',
                'value': '数值',
                'status': '状态',
                'timestamp': '时间戳',
                'name': '名称',
                'alias': '别名'
            };
            return columnNames[key] || key;
        }

        // 渲染数据表格（完整复刻设计器实现）
        function renderDataTable(widget, container, data) {
            console.log(`预览页面 - 渲染数据表格组件 ${widget.id}`);
            console.log('表格组件数据:', data);

            try {
                // 获取表格样式配置
                const tableConfig = getTableStyleConfig(widget);
                console.log('表格样式配置:', tableConfig);

                // 获取表格字段配置
                const tableFields = getTableFieldsFromWidget(widget);
                console.log('表格字段配置:', tableFields);

                // 处理数据格式
                let tableData = [];
                if (data && data.success) {
                    if (data.data && Array.isArray(data.data)) {
                        // 外部数据源格式
                        tableData = data.data;
                    } else if (data.labels && data.values) {
                        // 标准表格格式：转换为对象数组
                        tableData = [];
                        for (let i = 0; i < Math.min(data.labels.length, data.values.length); i++) {
                            tableData.push({
                                '项目': data.labels[i],
                                '数值': data.values[i]
                            });
                        }
                    } else if (data.tableData && Array.isArray(data.tableData)) {
                        // 复杂表格格式
                        tableData = data.tableData;
                    }
                }

                // 如果没有数据，使用示例数据
                if (tableData.length === 0) {
                    tableData = [
                        { '设备': '设备1', '状态': '正常', '数值': 45 },
                        { '设备': '设备2', '状态': '警告', '数值': 78 },
                        { '设备': '设备3', '状态': '故障', '数值': 12 }
                    ];
                }

                // 构建表格HTML
                const tableHtml = buildTableHTML(tableData, tableFields, tableConfig, widget.id);
                container.innerHTML = tableHtml;

                // 应用表格样式
                applyTableStyles(container, tableConfig, widget);

                console.log(`预览页面 - 数据表格组件 ${widget.id} 渲染成功`);
            } catch (error) {
                console.error(`预览页面 - 数据表格组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>表格渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        /**
         * 构建表格HTML（完整复刻设计器实现）
         */
        function buildTableHTML(data, tableFields, config, widgetId) {
            if (!data || data.length === 0) {
                return '<div class="text-center text-muted">暂无数据</div>';
            }

            console.log('构建表格HTML，配置:', config);

            // 构建表格类名
            let tableClasses = ['table'];

            // 添加表格样式类
            if (config.tableStyle && config.tableStyle !== 'table') {
                tableClasses.push(config.tableStyle);
            }

            // 添加表格大小类
            if (config.tableSize) {
                tableClasses.push(config.tableSize);
            }

            console.log('表格CSS类:', tableClasses);

            // 获取组件ID用于唯一标识
            const tableId = `data-table-${widgetId || Date.now()}`;

            // 构建表格容器，初始不设置样式，由applyTableStyles处理
            let tableHtml = `<div class="table-container">`;
            tableHtml += `<table class="${tableClasses.join(' ')}" id="${tableId}">`;

            // 表头
            if (config.showHeader) {
                tableHtml += '<thead><tr>';
                if (tableFields.length > 0) {
                    tableFields.forEach(field => {
                        tableHtml += `<th>${field.displayName}</th>`;
                    });
                } else {
                    Object.keys(data[0]).forEach(key => {
                        tableHtml += `<th>${getColumnName(key)}</th>`;
                    });
                }
                tableHtml += '</tr></thead>';
            }

            // 表格数据
            tableHtml += '<tbody>';
            const displayData = config.enableCarousel ? data.slice(0, config.rowsPerPage) : data;

            displayData.forEach((row, rowIndex) => {
                tableHtml += '<tr>';
                if (tableFields.length > 0) {
                    tableFields.forEach(field => {
                        const value = row[field.displayName] !== undefined ? row[field.displayName] : '';
                        tableHtml += `<td>${value}</td>`;
                    });
                } else {
                    Object.values(row).forEach(value => {
                        tableHtml += `<td>${value}</td>`;
                    });
                }
                tableHtml += '</tr>';
            });

            tableHtml += '</tbody></table></div>';

            return tableHtml;
        }

        /**
         * 应用表格样式（完整复刻设计器实现）
         */
        function applyTableStyles(contentElement, config, widget) {
            const table = contentElement.querySelector('table');
            const tableContainer = contentElement.querySelector('.table-container');
            if (!table) return;

            console.log('应用表格样式，配置:', config);

            // 获取组件的实际高度
            const componentHeight = widget ? widget.height : 200;
            console.log('组件高度:', componentHeight);

            // 应用表格头部样式
            const thead = table.querySelector('thead');
            if (thead) {
                const thElements = thead.querySelectorAll('th');
                thElements.forEach(th => {
                    th.style.backgroundColor = config.headerBgColor;
                    th.style.color = config.headerTextColor;
                    th.style.fontSize = config.headerFontSize + 'px';
                    th.style.fontWeight = config.fontWeight;
                    th.style.borderColor = config.borderColor;
                });
            }

            // 应用表格主体样式
            const tbody = table.querySelector('tbody');
            if (tbody) {
                const tdElements = tbody.querySelectorAll('td');
                tdElements.forEach(td => {
                    td.style.backgroundColor = config.bodyBgColor;
                    td.style.color = config.bodyTextColor;
                    td.style.fontSize = config.bodyFontSize + 'px';
                    td.style.fontWeight = config.fontWeight;
                    td.style.borderColor = config.borderColor;
                });
            }

            // 应用表格整体样式
            table.style.borderColor = config.borderColor;

            // 设置容器样式和智能滚动条 - 修复版，与设计页面保持一致
            if (tableContainer) {
                // 直接从DOM获取组件实际高度
                const widgetElement = tableContainer.closest('.widget-container');
                const actualComponentHeight = widgetElement ? widgetElement.offsetHeight : componentHeight;
                const maxHeight = Math.max(100, actualComponentHeight - 40);
                tableContainer.style.maxHeight = maxHeight + 'px';

                // 修复：初始不设置默认滚动条，使用更可靠的延迟检测
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        checkAndApplyScrollbar(tableContainer, table, maxHeight);
                    }, 150); // 与设计页面保持一致的延迟时间
                });
            }
        }

        /**
         * 检查并应用智能滚动条 - 增强版，修复刷新后滚动条判断错误
         */
        function checkAndApplyScrollbar(tableContainer, table, maxHeight) {
            if (!tableContainer || !table) return;

            // 如果maxHeight未提供，从DOM重新计算（增强容错性）
            if (!maxHeight) {
                const widgetElement = tableContainer.closest('.widget-container');
                const componentHeight = widgetElement ? widgetElement.offsetHeight : 300;
                maxHeight = Math.max(100, componentHeight - 40);
            }

            // 首先确保容器的最大高度是最新的
            tableContainer.style.maxHeight = maxHeight + 'px';

            // 强制重新计算布局
            tableContainer.offsetHeight;

            // 获取表格的实际内容高度
            const tableHeight = table.offsetHeight;

            // 增加容错机制，确保高度计算准确（修复边界情况下的误判）
            const needsScroll = tableHeight > maxHeight + 5; // 增加5px容错

            console.log('预览页面 - 表格高度检查 (增强版):', {
                tableHeight: tableHeight,
                maxHeight: maxHeight,
                needsScroll: needsScroll,
                componentHeight: tableContainer.closest('.widget-container')?.offsetHeight,
                heightDifference: tableHeight - maxHeight
            });

            // 根据实际需要设置滚动条（修复逻辑，更准确判断）
            if (needsScroll) {
                tableContainer.style.overflowY = 'auto';
                tableContainer.style.height = maxHeight + 'px'; // 强制设置高度
                tableContainer.classList.add('has-scrollbar');
                console.log('启用滚动条 - 内容高度超出组件范围，设置容器高度为:', maxHeight + 'px');
            } else {
                tableContainer.style.overflowY = 'visible';
                tableContainer.style.height = 'auto'; // 恢复自动高度
                tableContainer.classList.remove('has-scrollbar');
                console.log('禁用滚动条 - 内容高度在组件范围内');
            }
        }

        // 渲染文本标签（与设计页面保持完全一致）
        function renderTextLabel(widget, container, data) {
            console.log(`预览页面 - 渲染文本标签组件 ${widget.id}`);
            console.log('文本组件配置:', widget.config);
            console.log('文本组件样式配置:', widget.styleConfig);

            try {
                const styleConfig = widget.styleConfig || {};
                const dataSourceConfig = widget.dataSourceConfig || {};

                // 获取显示内容（与设计页面逻辑保持一致）
                let displayText = getTextDisplayContent(widget, styleConfig, dataSourceConfig, data);

                // 构建样式（与设计页面保持一致）
                const styles = buildTextLabelStyles(styleConfig);

                container.innerHTML = `<div id="text-content-${widget.id}" style="${styles}">${displayText}</div>`;

                console.log(`预览页面 - 文本标签组件 ${widget.id} 渲染成功，显示文本: ${displayText}`);
                console.log('应用的样式:', styles);
            } catch (error) {
                console.error(`预览页面 - 文本标签组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>文本渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 构建文本标签样式（完全复制设计页面逻辑）
        function buildTextLabelStyles(styleConfig) {
            const styles = [];

            // 字体配置
            if (styleConfig.textFontFamily) styles.push(`font-family: ${styleConfig.textFontFamily}`);
            if (styleConfig.textFontSize) styles.push(`font-size: ${styleConfig.textFontSize}px`);
            if (styleConfig.textFontColor) styles.push(`color: ${styleConfig.textFontColor}`);
            if (styleConfig.textFontWeight) styles.push(`font-weight: ${styleConfig.textFontWeight}`);
            if (styleConfig.textFontStyle) styles.push(`font-style: ${styleConfig.textFontStyle}`);
            if (styleConfig.textDecoration) styles.push(`text-decoration: ${styleConfig.textDecoration}`);

            // 布局配置
            if (styleConfig.textAlign) styles.push(`text-align: ${styleConfig.textAlign}`);
            if (styleConfig.textLineHeight) styles.push(`line-height: ${styleConfig.textLineHeight}`);
            if (styleConfig.textLetterSpacing) styles.push(`letter-spacing: ${styleConfig.textLetterSpacing}px`);
            if (styleConfig.textPadding) styles.push(`padding: ${styleConfig.textPadding}px`);

            // 垂直对齐
            if (styleConfig.textVerticalAlign) {
                styles.push('display: flex');
                styles.push('flex-direction: column');
                styles.push('height: 100%');
                if (styleConfig.textVerticalAlign === 'top') {
                    styles.push('justify-content: flex-start');
                } else if (styleConfig.textVerticalAlign === 'middle') {
                    styles.push('justify-content: center');
                } else if (styleConfig.textVerticalAlign === 'bottom') {
                    styles.push('justify-content: flex-end');
                }
            } else {
                // 默认垂直居中
                styles.push('display: flex');
                styles.push('align-items: center');
                styles.push('height: 100%');
            }

            // 背景渐变
            if (styleConfig.enableTextGradient && styleConfig.textGradientStartColor && styleConfig.textGradientEndColor) {
                if (styleConfig.textGradientType === 'radial') {
                    styles.push(`background: radial-gradient(circle, ${styleConfig.textGradientStartColor}, ${styleConfig.textGradientEndColor})`);
                } else {
                    const angle = styleConfig.textGradientAngle || 0;
                    styles.push(`background: linear-gradient(${angle}deg, ${styleConfig.textGradientStartColor}, ${styleConfig.textGradientEndColor})`);
                }
            }

            // 圆角
            if (styleConfig.textBorderRadius) {
                styles.push(`border-radius: ${styleConfig.textBorderRadius}px`);
            }

            // 默认样式
            if (!styleConfig.textFontFamily) styles.push('font-family: "Microsoft YaHei", sans-serif');
            if (!styleConfig.textFontSize) styles.push('font-size: 16px');
            if (!styleConfig.textFontColor) styles.push('color: #333333');
            if (!styleConfig.textAlign) styles.push('text-align: center');

            return styles.join('; ');
        }

        // 获取文本显示内容（完整复刻设计器实现）
        function getTextDisplayContent(widget, styleConfig, dataSourceConfig, data) {
            // 如果有数据传入，优先使用数据
            if (data && data.success) {
                if (data.displayValue !== undefined) {
                    return data.displayValue;
                } else if (data.value !== undefined) {
                    return data.value.toString();
                } else if (data.textContent !== undefined) {
                    return data.textContent;
                }
            }

            // 如果有静态数据配置，使用第一个数值作为文本内容
            if (dataSourceConfig.dataSourceType === 'static' && dataSourceConfig.staticValues) {
                const values = dataSourceConfig.staticValues.split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
                return values.length > 0 ? values[0] : '文本内容';
            }

            // 如果有监控项数据源，显示加载中
            if (dataSourceConfig.dataSourceType === 'dataItem' && dataSourceConfig.dataItemId) {
                return '加载中...'; // 数据将通过updateWidgetDisplay更新
            }

            // 如果有外部数据源，显示加载中
            if (dataSourceConfig.dataSourceType === 'externalData' && dataSourceConfig.dataSetId) {
                return '加载中...'; // 数据将通过updateWidgetDisplay更新
            }

            // 默认显示组件配置的文本
            return widget.config.text || '文本内容';
        }

        // 更新文本标签内容（完整复刻设计器实现）
        function updateTextLabelContent(widget, data, textElement) {
            console.log('=== 预览页面 - 更新文本标签内容 ===');
            console.log('Widget ID:', widget.id);
            console.log('Widget Type:', widget.widgetType);
            console.log('接收到的数据:', data);

            // 修复JSON解析问题：检查是否已经是对象
            let dataSourceConfig = {};
            try {
                if (typeof widget.dataSourceConfig === 'string') {
                    dataSourceConfig = JSON.parse(widget.dataSourceConfig);
                } else if (typeof widget.dataSourceConfig === 'object' && widget.dataSourceConfig !== null) {
                    dataSourceConfig = widget.dataSourceConfig;
                } else {
                    dataSourceConfig = {};
                }
            } catch (e) {
                console.error('解析数据源配置失败:', e);
                dataSourceConfig = {};
            }
            console.log('数据源配置:', dataSourceConfig);

            let content = '';

            if (dataSourceConfig.dataSourceType === 'static') {
                console.log('处理静态数据');
                // 静态数据：使用第一个数值作为文本内容，并应用数据转换
                if (dataSourceConfig.staticValues) {
                    const values = dataSourceConfig.staticValues.split('\n')
                        .map(line => line.trim())
                        .filter(line => line.length > 0);

                    if (values.length > 0) {
                        // 创建静态数据对象并应用转换
                        const staticDataObj = {
                            success: true,
                            value: parseFloat(values[0]) || values[0], // 尝试转换为数字，失败则保持原值
                            name: '静态数据'
                        };

                        // 优先使用转换后的显示值
                        if (staticDataObj.displayValue !== undefined) {
                            content = staticDataObj.displayValue;
                            console.log('使用转换后的静态显示值:', content);
                        } else {
                            content = staticDataObj.value.toString();
                            console.log('使用转换后的静态数值:', content);
                        }
                    } else {
                        content = '文本内容';
                        console.log('静态数据为空，使用默认内容');
                    }
                } else {
                    content = widget.config.text || '文本内容';
                    console.log('使用默认文本内容:', content);
                }
            } else if (dataSourceConfig.dataSourceType === 'dataItem') {
                console.log('处理监控项数据');
                console.log('数据成功状态:', data.success);
                console.log('数据值:', data.value);
                console.log('显示值:', data.displayValue);

                // 监控项数据：优先使用已转换的显示值（数据已在updateWidgetData中转换）
                if (data.success) {
                    if (data.displayValue !== undefined) {
                        // 优先使用格式化的显示值（包含后缀）
                        content = data.displayValue;
                        console.log('使用已转换的显示值:', content);
                    } else if (data.value !== undefined) {
                        // 标准格式：有value字段
                        content = data.value.toString();
                        console.log('使用已转换的数值:', content);
                    } else if (data.values && data.values.length > 0) {
                        // 数组格式：使用第一个值
                        content = data.values[0].toString();
                        console.log('使用values[0]:', content);
                    } else if (data.labels && data.labels.length > 0) {
                        // 标签格式：使用第一个标签
                        content = data.labels[0].toString();
                        console.log('使用labels[0]:', content);
                    } else {
                        // 检查是否有其他可用字段
                        const availableFields = Object.keys(data).filter(key =>
                            key !== 'success' && key !== 'timestamp' && key !== 'displayValue' &&
                            key !== 'enableDataTransform' && key !== 'transformOperation' &&
                            key !== 'transformValue' && key !== 'decimalPlaces' && key !== 'dataSuffix' &&
                            data[key] !== undefined
                        );
                        console.log('可用字段:', availableFields);

                        if (availableFields.length > 0) {
                            const firstField = availableFields[0];
                            content = data[firstField].toString();
                            console.log('使用第一个可用字段', firstField + ':', content);
                        } else {
                            content = '无数据';
                            console.log('数据格式不正确，显示无数据');
                        }
                    }
                } else {
                    content = '数据获取失败';
                    console.log('数据获取失败');
                }
            } else if (dataSourceConfig.dataSourceType === 'externalData') {
                console.log('处理外部数据源');
                // 外部数据源：使用textContent字段
                if (data.success && data.textContent !== undefined) {
                    content = data.textContent;
                    console.log('使用外部数据源文本内容:', content);
                } else {
                    content = '外部数据获取失败';
                    console.log('外部数据源获取失败');
                }
            } else {
                console.log('使用默认配置');
                // 默认：显示组件配置的文本
                content = widget.config.text || '文本内容';
            }

            console.log('最终显示内容:', content);
            textElement.innerHTML = content;
            console.log('=== 预览页面 - 文本标签内容更新完成 ===');
        }

        // 渲染图片组件
        function renderImageWidget(widget, container, data) {
            console.log(`预览页面 - 渲染图片组件 ${widget.id}`);
            console.log('图片组件配置:', widget.config);
            console.log('图片组件样式配置:', widget.styleConfig);

            try {
                const config = widget.config || {};
                let imageConfig = config;

                // 如果config是字符串，尝试解析
                if (typeof config === 'string') {
                    try {
                        imageConfig = JSON.parse(config);
                    } catch (e) {
                        console.warn('图片组件配置解析失败:', e);
                        imageConfig = {};
                    }
                }

                // 获取图片URL，优先从config中获取
                const imageUrl = imageConfig.imageUrl || imageConfig.src;
                console.log('图片组件URL:', imageUrl);

                // 获取缩放模式，支持多种配置来源（与设计页面保持一致）
                const styleConfig = widget.styleConfig || {};
                const objectFit = styleConfig.imageObjectFit || styleConfig.objectFit || imageConfig.objectFit || 'contain';
                console.log('图片组件缩放模式:', objectFit);
                console.log('样式配置中的适应方式参数:', {
                    imageObjectFit: styleConfig.imageObjectFit,
                    objectFit: styleConfig.objectFit,
                    configObjectFit: imageConfig.objectFit
                });

                if (imageUrl) {
                    // 构建完整的图片样式（与设计页面保持一致）
                    const imageStyles = [
                        'width: 100%',
                        'height: 100%',
                        'display: block',
                        `object-fit: ${objectFit}`
                    ];

                    // 圆角
                    if (styleConfig.imageBorderRadius) {
                        imageStyles.push(`border-radius: ${styleConfig.imageBorderRadius}px`);
                    }

                    // 旋转角度
                    if (styleConfig.imageRotation) {
                        imageStyles.push(`transform: rotate(${styleConfig.imageRotation}deg)`);
                    }

                    // 透明度
                    if (styleConfig.imageOpacity !== undefined) {
                        const opacity = styleConfig.imageOpacity / 100;
                        imageStyles.push(`opacity: ${opacity}`);
                    }

                    const finalStyle = imageStyles.join('; ');

                    container.innerHTML = `
                        <img src="${imageUrl}"
                             style="${finalStyle}"
                             alt="${imageConfig.alt || '图片'}"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="display: none; text-align: center; color: #6c757d; height: 100%; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="bi bi-image" style="font-size: 3rem;"></i>
                            <div>图片加载失败</div>
                        </div>
                    `;
                    console.log(`预览页面 - 图片组件 ${widget.id} 渲染成功，URL: ${imageUrl}, 样式: ${finalStyle}`);
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; color: #6c757d; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="bi bi-image" style="font-size: 3rem;"></i>
                            <div>未设置图片</div>
                        </div>
                    `;
                    console.log(`预览页面 - 图片组件 ${widget.id} 未配置图片URL`);
                }

            } catch (error) {
                console.error(`预览页面 - 图片组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>图片渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染超链接组件
        function renderHyperlinkWidget(widget, container, data) {
            console.log(`预览页面 - 渲染超链接组件 ${widget.id}`);
            console.log('超链接组件配置:', widget.config);
            console.log('超链接组件样式配置:', widget.styleConfig);

            try {
                const config = widget.config || {};
                let hyperlinkConfig = config;

                // 如果config是字符串，尝试解析
                if (typeof config === 'string') {
                    try {
                        hyperlinkConfig = JSON.parse(config);
                    } catch (e) {
                        console.warn('超链接组件配置解析失败:', e);
                        hyperlinkConfig = {};
                    }
                }

                // 获取样式配置
                let styleConfig = {};
                if (typeof widget.styleConfig === 'string') {
                    try {
                        styleConfig = JSON.parse(widget.styleConfig);
                    } catch (e) {
                        console.warn('超链接组件样式配置解析失败:', e);
                        styleConfig = {};
                    }
                } else if (typeof widget.styleConfig === 'object' && widget.styleConfig !== null) {
                    styleConfig = widget.styleConfig;
                }

                // 获取超链接配置
                const hyperlinkUrl = styleConfig.hyperlinkUrl || hyperlinkConfig.hyperlinkUrl || hyperlinkConfig.src;
                const hyperlinkText = styleConfig.hyperlinkText || hyperlinkConfig.hyperlinkText || '';
                const hyperlinkTarget = styleConfig.hyperlinkTarget || hyperlinkConfig.hyperlinkTarget || '_self';

                console.log('超链接组件URL:', hyperlinkUrl);
                console.log('超链接组件文本:', hyperlinkText);
                console.log('超链接组件打开方式:', hyperlinkTarget);

                if (hyperlinkUrl) {
                    // 构建容器样式
                    const containerStyles = [];
                    containerStyles.push('width: 100%');
                    containerStyles.push('height: 100%');
                    containerStyles.push('display: flex');
                    containerStyles.push('align-items: center');
                    containerStyles.push('cursor: pointer');
                    containerStyles.push('text-decoration: none');
                    containerStyles.push('transition: all 0.3s ease');
                    containerStyles.push('padding: 8px');
                    containerStyles.push('box-sizing: border-box');

                    // 字体配置
                    if (styleConfig.hyperlinkFontSize) {
                        containerStyles.push(`font-size: ${styleConfig.hyperlinkFontSize}px`);
                    } else {
                        containerStyles.push('font-size: 16px');
                    }

                    if (styleConfig.hyperlinkFontColor) {
                        containerStyles.push(`color: ${styleConfig.hyperlinkFontColor}`);
                    } else {
                        containerStyles.push('color: #007bff');
                    }

                    if (styleConfig.hyperlinkFontWeight) {
                        containerStyles.push(`font-weight: ${styleConfig.hyperlinkFontWeight}`);
                    }

                    if (styleConfig.hyperlinkFontStyle) {
                        containerStyles.push(`font-style: ${styleConfig.hyperlinkFontStyle}`);
                    }

                    if (styleConfig.hyperlinkTextDecoration) {
                        containerStyles.push(`text-decoration: ${styleConfig.hyperlinkTextDecoration}`);
                    }

                    // 文本对齐
                    if (styleConfig.hyperlinkTextAlign) {
                        if (styleConfig.hyperlinkTextAlign === 'center') {
                            containerStyles.push('justify-content: center');
                        } else if (styleConfig.hyperlinkTextAlign === 'right') {
                            containerStyles.push('justify-content: flex-end');
                        } else {
                            containerStyles.push('justify-content: flex-start');
                        }
                    } else {
                        containerStyles.push('justify-content: center');
                    }



                    // 透明度
                    if (styleConfig.hyperlinkOpacity !== undefined) {
                        const opacity = styleConfig.hyperlinkOpacity / 100;
                        containerStyles.push(`opacity: ${opacity}`);
                    }

                    const finalStyle = containerStyles.join('; ');

                    container.innerHTML = `
                        <div style="${finalStyle}" onclick="window.open('${hyperlinkUrl}', '${hyperlinkTarget}')"
                             onmouseover="this.style.filter='brightness(1.1)'"
                             onmouseout="this.style.filter='brightness(1)'">
                            ${hyperlinkText}
                        </div>
                    `;
                    console.log(`预览页面 - 超链接组件 ${widget.id} 渲染成功，URL: ${hyperlinkUrl}`);
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; color: #6c757d; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="bi bi-link-45deg" style="font-size: 3rem;"></i>
                            <div>未设置超链接</div>
                        </div>
                    `;
                    console.log(`预览页面 - 超链接组件 ${widget.id} 未配置超链接URL`);
                }

            } catch (error) {
                console.error(`预览页面 - 超链接组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>超链接渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染装饰组件（与设计页面保持一致，支持完整样式配置）
        function renderDecorationWidget(widget, container, data) {
            console.log(`预览页面 - 渲染装饰组件 ${widget.id}`);
            console.log('装饰组件配置:', widget.config);
            console.log('装饰组件样式配置:', widget.styleConfig);

            try {
                const config = widget.config || {};
                let decorationConfig = config;

                // 如果config是字符串，尝试解析
                if (typeof config === 'string') {
                    try {
                        decorationConfig = JSON.parse(config);
                    } catch (e) {
                        console.warn('装饰组件配置解析失败:', e);
                        decorationConfig = {};
                    }
                }

                // 获取样式配置（与设计页面保持一致）
                const styleConfig = widget.styleConfig || {};

                // 获取缩放模式，支持多种配置来源（与图片组件保持一致）
                const objectFit = styleConfig.decorationObjectFit || styleConfig.objectFit || decorationConfig.objectFit || 'contain';
                console.log('装饰组件缩放模式:', objectFit);
                console.log('样式配置中的适应方式参数:', {
                    decorationObjectFit: styleConfig.decorationObjectFit,
                    objectFit: styleConfig.objectFit,
                    configObjectFit: decorationConfig.objectFit
                });

                // 获取素材URL
                const materialUrl = decorationConfig.materialUrl;
                console.log('装饰组件素材URL:', materialUrl);

                if (materialUrl) {
                    // 构建完整的装饰样式（与图片组件保持一致）
                    const decorationStyles = [
                        'width: 100%',
                        'height: 100%',
                        'display: block',
                        `object-fit: ${objectFit}`
                    ];

                    // 旋转角度
                    if (styleConfig.decorationRotation) {
                        decorationStyles.push(`transform: rotate(${styleConfig.decorationRotation}deg)`);
                    }

                    // 透明度
                    if (styleConfig.decorationOpacity !== undefined) {
                        const opacity = styleConfig.decorationOpacity / 100;
                        decorationStyles.push(`opacity: ${opacity}`);
                    }

                    // 混合模式
                    if (styleConfig.decorationBlendMode) {
                        decorationStyles.push(`mix-blend-mode: ${styleConfig.decorationBlendMode}`);
                    }

                    const finalStyle = decorationStyles.join('; ');

                    container.innerHTML = `
                        <img src="${materialUrl}"
                             style="${finalStyle}"
                             alt="装饰素材"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div style="display: none; text-align: center; color: #6c757d; height: 100%; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="bi bi-star" style="font-size: 3rem;"></i>
                            <div>装饰素材加载失败</div>
                        </div>
                    `;
                    console.log(`预览页面 - 装饰组件 ${widget.id} 渲染成功，素材URL: ${materialUrl}, 样式: ${finalStyle}`);
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; color: #6c757d; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="bi bi-star" style="font-size: 3rem;"></i>
                            <div>装饰组件</div>
                        </div>
                    `;
                    console.log(`预览页面 - 装饰组件 ${widget.id} 未配置素材URL`);
                }

            } catch (error) {
                console.error(`预览页面 - 装饰组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>装饰渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染HTML组件（与设计页面保持一致）
        function renderHtmlWidget(widget, container, data) {
            console.log(`预览页面 - 渲染HTML组件 ${widget.id}`);
            console.log('HTML组件配置:', widget.config);
            console.log('HTML组件样式配置:', widget.styleConfig);

            try {
                // 安全处理styleConfig，可能是字符串或对象
                let styleConfig = {};
                if (widget.styleConfig) {
                    if (typeof widget.styleConfig === 'string') {
                        try {
                            styleConfig = JSON.parse(widget.styleConfig);
                        } catch (e) {
                            console.warn('预览页面 - HTML组件样式配置解析失败:', e);
                            styleConfig = {};
                        }
                    } else if (typeof widget.styleConfig === 'object') {
                        styleConfig = widget.styleConfig;
                    }
                }

                const htmlContent = styleConfig.htmlContent || widget.config.htmlContent || '';
                const htmlTitle = styleConfig.htmlTitle || widget.config.htmlTitle || '';

                if (htmlContent) {
                    // 创建iframe来隔离HTML代码，确保响应式填满容器
                    const iframe = document.createElement('iframe');
                    iframe.srcdoc = htmlContent;
                    iframe.title = htmlTitle;
                    iframe.style.cssText = `
                        width: 100%;
                        height: 100%;
                        border: none;
                        display: block;
                        background: transparent;
                        opacity: ${(styleConfig.htmlOpacity || 100) / 100};
                    `;
                    iframe.setAttribute('sandbox', 'allow-scripts');

                    container.innerHTML = '';
                    container.appendChild(iframe);

                    console.log(`预览页面 - HTML组件 ${widget.id} 渲染成功`);
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; color: #6c757d; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                            <i class="bi bi-code-square" style="font-size: 3rem;"></i>
                            <div>HTML组件</div>
                        </div>
                    `;
                    console.log(`预览页面 - HTML组件 ${widget.id} 未配置HTML内容`);
                }

            } catch (error) {
                console.error(`预览页面 - HTML组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>HTML渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 检测视频文件格式
        function getVideoMimeType(url) {
            if (!url) return 'video/mp4'; // 默认返回mp4

            const extension = url.toLowerCase().split('.').pop().split('?')[0]; // 移除查询参数

            switch (extension) {
                case 'mp4':
                    return 'video/mp4';
                case 'webm':
                    return 'video/webm';
                case 'ogg':
                case 'ogv':
                    return 'video/ogg';
                case 'avi':
                    return 'video/x-msvideo';
                case 'mov':
                    return 'video/quicktime';
                case 'wmv':
                    return 'video/x-ms-wmv';
                case 'flv':
                    return 'video/x-flv';
                case 'mkv':
                    return 'video/x-matroska';
                default:
                    return 'video/mp4'; // 默认返回mp4
            }
        }

        // 渲染视频组件（与设计页面保持一致）
        function renderVideoWidget(widget, container, data) {
            console.log(`预览页面 - 渲染视频组件 ${widget.id}`);
            console.log('视频组件配置:', widget.config);
            console.log('视频组件样式配置:', widget.styleConfig);

            try {
                // 安全处理styleConfig，可能是字符串或对象
                let styleConfig = {};
                if (widget.styleConfig) {
                    if (typeof widget.styleConfig === 'string') {
                        try {
                            styleConfig = JSON.parse(widget.styleConfig);
                        } catch (e) {
                            console.warn('预览页面 - 视频组件样式配置解析失败:', e);
                            styleConfig = {};
                        }
                    } else if (typeof widget.styleConfig === 'object') {
                        styleConfig = widget.styleConfig;
                    }
                }

                // 获取视频配置
                const videoUrl = styleConfig.videoUrl || '';
                const poster = styleConfig.poster || '';
                const autoplay = styleConfig.autoplay || false;
                const loop = styleConfig.loop || false;
                const muted = styleConfig.muted !== false; // 默认静音
                const controls = styleConfig.controls !== false; // 默认显示控制条

                // 构建样式
                const styles = [];
                styles.push('width: 100%');
                styles.push('height: 100%');
                styles.push('display: block');
                styles.push('object-fit: contain');

                // 圆角
                if (styleConfig.borderRadius) {
                    styles.push(`border-radius: ${styleConfig.borderRadius}px`);
                }

                // 透明度
                if (styleConfig.opacity !== undefined) {
                    const opacity = styleConfig.opacity / 100;
                    styles.push(`opacity: ${opacity}`);
                }

                // 边框
                if (styleConfig.borderWidth && styleConfig.borderWidth > 0) {
                    const borderColor = styleConfig.borderColor || '#dee2e6';
                    styles.push(`border: ${styleConfig.borderWidth}px solid ${borderColor}`);
                }

                // 阴影
                if (styleConfig.shadow) {
                    styles.push('box-shadow: 0 4px 8px rgba(0,0,0,0.1)');
                }

                if (videoUrl) {
                    // 构建video标签属性
                    const videoAttributes = [];
                    if (autoplay) videoAttributes.push('autoplay');
                    if (loop) videoAttributes.push('loop');
                    if (muted) videoAttributes.push('muted');
                    if (controls) videoAttributes.push('controls');
                    if (poster) videoAttributes.push(`poster="${poster}"`);

                    // 获取正确的MIME类型
                    const mimeType = getVideoMimeType(videoUrl);
                    console.log('预览页面 - 视频URL:', videoUrl, '检测到的MIME类型:', mimeType);

                    container.innerHTML = `
                        <video
                            id="video-${widget.id}"
                            style="${styles.join('; ')}"
                            ${videoAttributes.join(' ')}
                            preload="metadata">
                            <source src="${videoUrl}" type="${mimeType}">
                            您的浏览器不支持视频播放
                        </video>
                    `;
                } else {
                    container.innerHTML = `
                        <div style="text-align: center; color: #6c757d; padding-top: 30%; height: 100%;">
                            <i class="bi bi-play-circle" style="font-size: 3rem;"></i>
                            <div>请配置视频URL</div>
                        </div>
                    `;
                }

                console.log(`预览页面 - 视频组件 ${widget.id} 渲染完成`);

            } catch (error) {
                console.error(`预览页面 - 视频组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>视频渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 渲染时间组件（与设计页面保持一致）
        function renderTimeWidget(widget, container, data) {
            console.log(`预览页面 - 渲染时间组件 ${widget.id}`);
            console.log('时间组件配置:', widget.config);
            console.log('时间组件样式配置:', widget.styleConfig);

            try {
                // 安全处理styleConfig，可能是字符串或对象
                let styleConfig = {};
                if (widget.styleConfig) {
                    if (typeof widget.styleConfig === 'string') {
                        try {
                            styleConfig = JSON.parse(widget.styleConfig);
                        } catch (e) {
                            console.warn('预览页面 - 时间组件样式配置解析失败:', e);
                            styleConfig = {};
                        }
                    } else if (typeof widget.styleConfig === 'object') {
                        styleConfig = widget.styleConfig;
                    }
                }

                // 构建样式
                const styles = buildPreviewTimeWidgetStyles(styleConfig);

                // 获取时间格式
                const format = styleConfig.timeFormat || widget.config.format || 'YYYY-MM-DD HH:mm:ss';

                // 格式化当前时间
                const currentTime = formatPreviewTime(new Date(), format);

                container.innerHTML = `
                    <div
                        id="preview-time-${widget.id}"
                        class="time-widget-content"
                        style="${styles.join('; ')}"
                        data-format="${format}">
                        ${currentTime}
                    </div>
                `;

                // 启动实时更新
                initializePreviewTimeWidget(widget);

                console.log(`预览页面 - 时间组件 ${widget.id} 渲染完成`);

            } catch (error) {
                console.error(`预览页面 - 时间组件 ${widget.id} 渲染失败:`, error);
                container.innerHTML = `
                    <div class="error-message">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>时间组件渲染失败</div>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // 构建预览页面时间组件样式
        function buildPreviewTimeWidgetStyles(styleConfig) {
            const styles = [];

            // 基础样式
            styles.push('width: 100%');
            styles.push('height: 100%');
            styles.push('display: flex');
            styles.push('align-items: center');
            styles.push('justify-content: center');
            styles.push('text-align: center');

            // 字体大小
            const fontSize = styleConfig.timeFontSize || 24;
            styles.push(`font-size: ${fontSize}px`);

            // 字体粗细
            const fontWeight = styleConfig.timeFontWeight || 'normal';
            styles.push(`font-weight: ${fontWeight}`);

            // 字体样式
            const fontStyle = styleConfig.timeFontStyle || 'normal';
            styles.push(`font-style: ${fontStyle}`);

            // 颜色处理（支持渐变色）
            if (styleConfig.timeColorType === 'gradient' && styleConfig.timeGradientStart && styleConfig.timeGradientEnd) {
                const direction = styleConfig.timeGradientDirection || 'to right';
                const gradientCSS = `linear-gradient(${direction}, ${styleConfig.timeGradientStart}, ${styleConfig.timeGradientEnd})`;
                styles.push(`background: ${gradientCSS}`);
                styles.push('-webkit-background-clip: text');
                styles.push('-webkit-text-fill-color: transparent');
                styles.push('background-clip: text');
            } else {
                const color = styleConfig.timeColor || '#333333';
                styles.push(`color: ${color}`);
            }

            // 透明度
            if (styleConfig.timeOpacity !== undefined) {
                const opacity = styleConfig.timeOpacity / 100;
                styles.push(`opacity: ${opacity}`);
            }

            // 文字阴影
            if (styleConfig.timeTextShadow) {
                styles.push('text-shadow: 2px 2px 4px rgba(0,0,0,0.3)');
            }

            return styles;
        }

        // 预览页面时间格式化函数
        function formatPreviewTime(date, format) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            const hours12 = String(date.getHours() % 12 || 12).padStart(2, '0');
            const ampm = date.getHours() >= 12 ? 'PM' : 'AM';

            return format
                .replace('YYYY', year)
                .replace('MM', month)
                .replace('DD', day)
                .replace('HH', hours)
                .replace('hh', hours12)
                .replace('mm', minutes)
                .replace('ss', seconds)
                .replace('A', ampm);
        }

        // 初始化预览页面时间组件
        function initializePreviewTimeWidget(widget) {
            console.log(`预览页面 - 初始化时间组件 ${widget.id}`);

            // 清除可能存在的旧定时器
            if (widget.previewTimeUpdateTimer) {
                clearInterval(widget.previewTimeUpdateTimer);
            }

            // 立即更新一次时间
            updatePreviewTimeWidget(widget);

            // 设置每秒更新时间的定时器
            widget.previewTimeUpdateTimer = setInterval(() => {
                updatePreviewTimeWidget(widget);
            }, 1000);

            console.log(`预览页面 - 时间组件 ${widget.id} 定时器已启动`);
        }

        // 更新预览页面时间组件显示
        function updatePreviewTimeWidget(widget) {
            const timeElement = document.getElementById(`preview-time-${widget.id}`);
            if (!timeElement) {
                console.warn(`预览页面 - 时间组件 ${widget.id} 的DOM元素未找到`);
                return;
            }

            let styleConfig = {};
            if (widget.styleConfig) {
                if (typeof widget.styleConfig === 'string') {
                    try {
                        styleConfig = JSON.parse(widget.styleConfig);
                    } catch (e) {
                        styleConfig = {};
                    }
                } else if (typeof widget.styleConfig === 'object') {
                    styleConfig = widget.styleConfig;
                }
            }

            const format = styleConfig.timeFormat || widget.config.format || 'YYYY-MM-DD HH:mm:ss';

            // 格式化当前时间
            const currentTime = formatPreviewTime(new Date(), format);

            // 更新显示内容
            timeElement.textContent = currentTime;
        }

        // 渲染状态指示器
        function renderStatusIndicator(widget, contentElement, data) {
            console.log('预览页面 - 渲染状态指示器:', widget.id);
            console.log('状态指示器数据:', data);

            // 使用BiStatusIndicator类渲染组件
            if (window.BiStatusIndicator) {
                const statusIndicator = new window.BiStatusIndicator();
                statusIndicator.render(widget, contentElement, data);
                console.log('预览页面 - 状态指示器渲染完成');
            } else {
                console.error('BiStatusIndicator类未加载');
                contentElement.innerHTML = '<div class="error-message">状态指示器组件未正确加载</div>';
            }
        }

        // 获取预览页面组件的ECharts配置（复刻设计页面逻辑）
        function getPreviewWidgetEChartsConfig(widget) {
            const styleConfig = widget.styleConfig || {};
            const dataSourceConfig = widget.dataSourceConfig || {};

            // 获取基础配置
            const baseConfig = {
                title: '',
                theme: 'default',
                gaugeMin: 0,
                gaugeMax: 100,
                gaugeUnit: '%'
            };

            return {
                ...baseConfig,
                title: widget.config.title || '',
                theme: styleConfig.theme || 'default',
                titleFontSize: styleConfig.titleFontSize || 18,
                titleColor: styleConfig.titleColor || '#333',
                primaryColor: styleConfig.primaryColor || '#5470c6',
                labelFontSize: styleConfig.labelFontSize || 12,
                showLegend: styleConfig.showLegend !== false,
                showChartTitle: styleConfig.showChartTitle !== false,
                borderlessMode: styleConfig.borderlessMode === true,
                showDataLabels: styleConfig.showDataLabels === true,

                // 关键样式配置（确保在图表创建时就应用）
                transparent: styleConfig.transparent === true,
                backgroundColor: styleConfig.transparent === true ? 'transparent' : (styleConfig.backgroundColor || '#ffffff'),

                // 仪表盘特有配置
                min: styleConfig.gaugeMin || 0,
                max: styleConfig.gaugeMax || 100,
                unit: styleConfig.gaugeUnit || '%',
                radius: (styleConfig.gaugeRadius || 80) + '%'
            };
        }

        // 获取示例数据
        function getExampleData(widgetType) {
            const exampleData = {
                'line-chart': {
                    success: true,
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    values: [12, 19, 15, 25, 22, 18]
                },
                'bar-chart': {
                    success: true,
                    labels: ['设备1', '设备2', '设备3', '设备4'],
                    values: [65, 59, 80, 81]
                },
                'horizontal-bar-chart': {
                    success: true,
                    labels: ['产品A', '产品B', '产品C', '产品D'],
                    values: [120, 200, 150, 80]
                },
                'pie-chart': {
                    success: true,
                    labels: ['正常', '警告', '故障'],
                    values: [70, 20, 10]
                },
                'gauge-chart': {
                    success: true,
                    value: 75,
                    name: '设备运行率'
                },
                'water-chart': {
                    success: true,
                    value: 0.65,
                    name: '完成进度'
                },
                'text-label': {
                    success: true,
                    value: '示例文本'
                },
                'data-table': {
                    success: true,
                    labels: ['设备1', '设备2', '设备3'],
                    values: [45, 78, 12]
                }
            };

            return exampleData[widgetType] || { success: true, value: '暂无数据' };
        }

        // 更新组件数据（复制自设计页面）
        async function updateWidgetData(widget) {
            console.log('预览页面 - 更新组件数据:', widget.id, widget.widgetType);

            try {
                // 使用统一的数据源管理器获取数据
                const data = window.biDataSourceManager ?
                    await window.biDataSourceManager.fetchWidgetData(widget) :
                    { success: false, error: '数据源管理器未初始化' };

                if (data.success) {
                    // 应用数值转换（如果需要的话）
                    const transformedData = data; // 预览页面暂时不需要复杂的数值转换
                    console.log('预览页面 - 转换后的数据:', transformedData);
                    updateWidgetDisplay(widget, transformedData);
                } else {
                    console.error('预览页面 - 数据获取失败:', data.error);
                }
            } catch (error) {
                console.error('预览页面 - 更新组件数据失败:', error);
            }
        }

        // 更新组件显示（复制自设计页面）
        function updateWidgetDisplay(widget, data) {
            console.log('预览页面 - 更新组件显示:', widget.id, widget.widgetType, data);

            // ECharts图表组件
            if (['line-chart', 'bar-chart', 'horizontal-bar-chart', 'pie-chart', 'gauge-chart', 'water-chart'].includes(widget.widgetType)) {
                if (charts[widget.id]) {
                    // 获取组件的完整配置
                    const widgetConfig = getPreviewWidgetEChartsConfig(widget);

                    if (widget.widgetType === 'pie-chart') {
                        // 饼图需要data数组或labels+values
                        if (data.data || (data.labels && data.values)) {
                            updateEChartsData(`echarts-${widget.id}`, data, widgetConfig);
                            console.log('预览页面 - 饼图数据更新完成');
                        } else {
                            console.error('预览页面 - 饼图数据格式错误');
                        }
                    } else if (widget.widgetType === 'gauge-chart') {
                        // 仪表盘需要value
                        if (data.value !== undefined) {
                            updateEChartsData(`echarts-${widget.id}`, data, widgetConfig);
                            console.log('预览页面 - 仪表盘数据更新完成');
                        } else {
                            console.error('预览页面 - 仪表盘数据格式错误');
                        }
                    } else if (widget.widgetType === 'water-chart') {
                        // 水波图需要value
                        if (data.value !== undefined) {
                            updateEChartsData(`echarts-${widget.id}`, data, widgetConfig);
                            console.log('预览页面 - 水波图数据更新完成');
                        } else {
                            console.error('预览页面 - 水波图数据格式错误');
                        }
                    } else {
                        // 其他图表需要labels和values
                        if (data.labels && data.values) {
                            updateEChartsData(`echarts-${widget.id}`, data, widgetConfig);
                            console.log('预览页面 - ECharts图表数据更新完成');
                        } else {
                            console.error('预览页面 - 图表数据格式错误');
                        }
                    }
                } else {
                    console.error('预览页面 - 图表实例不存在:', `echarts-${widget.id}`);
                }
            } else if (widget.widgetType === 'text-label') {
                // 文本标签组件（完整复刻设计器实现）
                console.log('预览页面 - 更新文本组件，widget:', widget.id);
                console.log('预览页面 - 文本组件数据:', data);

                const textElement = document.getElementById(`text-content-${widget.id}`);
                if (textElement) {
                    updateTextLabelContent(widget, data, textElement);
                    console.log('预览页面 - 文本组件更新完成');
                } else {
                    console.error('预览页面 - 文本组件元素不存在:', `text-content-${widget.id}`);
                }
            } else if (widget.widgetType === 'status-indicator') {
                // 状态指示器组件
                console.log('预览页面 - 更新状态指示器，widget:', widget.id);
                console.log('预览页面 - 状态指示器数据:', data);

                const contentElement = document.getElementById(`content-${widget.id}`);
                if (contentElement) {
                    // 使用BiStatusIndicator类更新组件
                    if (window.BiStatusIndicator) {
                        const statusIndicator = new window.BiStatusIndicator();
                        statusIndicator.updateWidget(widget, data, contentElement);
                        console.log('预览页面 - 状态指示器更新完成');
                    } else {
                        console.error('BiStatusIndicator类未加载');
                        contentElement.innerHTML = '<div class="error-message">状态指示器组件未正确加载</div>';
                    }
                } else {
                    console.error('预览页面 - 状态指示器元素不存在:', `content-${widget.id}`);
                }
            } else if (widget.widgetType === 'data-table') {
                // 数据表格组件 - 需要重新渲染
                const contentElement = document.getElementById(`content-${widget.id}`);
                if (contentElement && data.success) {
                    renderDataTable(widget, contentElement, data);
                    console.log('预览页面 - 数据表格重新渲染完成');
                }
            }
        }

        // 获取预览页面组件的ECharts配置
        function getPreviewWidgetEChartsConfig(widget) {
            const styleConfig = widget.styleConfig || {};
            const dataSourceConfig = widget.dataSourceConfig || {};

            return {
                title: widget.config.title || '',
                theme: styleConfig.theme || 'default',
                titleFontSize: styleConfig.titleFontSize || 18,
                titleColor: styleConfig.titleColor || '#333',
                primaryColor: styleConfig.primaryColor || '#5470c6',
                labelFontSize: styleConfig.labelFontSize || 12,
                showLegend: styleConfig.showLegend !== false,
                showChartTitle: styleConfig.showChartTitle !== false,
                transparent: styleConfig.transparent === true,
                backgroundColor: styleConfig.transparent === true ? 'transparent' : (styleConfig.backgroundColor || '#ffffff'),

                // 仪表盘特有配置
                min: styleConfig.gaugeMin || 0,
                max: styleConfig.gaugeMax || 100,
                unit: styleConfig.gaugeUnit || '%',
                radius: (styleConfig.gaugeRadius || 80) + '%',

                // 水波图特有配置
                enableTarget: styleConfig.enableTarget || false,
                targetValue: styleConfig.targetValue || 100,
                waveColor: styleConfig.waveColor || '#5470c6',
                borderColor: styleConfig.borderColor || '#5470c6'
            };
        }

        // 设置自动刷新（基于组件个性化刷新间隔）
        function setupAutoRefresh() {
            console.log('预览页面 - 设置个性化自动刷新机制');

            // 为每个组件设置独立的刷新定时器
            Object.keys(window.widgetInstances).forEach(widgetId => {
                const widget = window.widgetInstances[widgetId];
                if (widget && widget.dataSourceConfig) {
                    setupWidgetAutoRefresh(widget);
                }
            });
        }

        // 设置单个组件的自动刷新
        function setupWidgetAutoRefresh(widget) {
            // 清除之前的定时器
            if (widget.refreshTimer) {
                clearInterval(widget.refreshTimer);
            }

            let dataSourceConfig = {};
            try {
                if (typeof widget.dataSourceConfig === 'string') {
                    dataSourceConfig = JSON.parse(widget.dataSourceConfig);
                } else if (typeof widget.dataSourceConfig === 'object') {
                    dataSourceConfig = widget.dataSourceConfig;
                }
            } catch (error) {
                console.error('解析数据源配置失败:', error);
                return;
            }

            // 检查是否需要自动刷新
            const needsAutoRefresh = dataSourceConfig.dataItemId ||
                                    (dataSourceConfig.dataSourceType === 'multiData' &&
                                     dataSourceConfig.multiDataSources &&
                                     dataSourceConfig.multiDataSources.length > 0) ||
                                    (dataSourceConfig.dataSourceType === 'externalData' &&
                                     dataSourceConfig.dataSetId);

            if (needsAutoRefresh) {
                // 获取刷新间隔，默认5秒，最小1秒，最大300秒
                const refreshInterval = Math.max(1, Math.min(300, dataSourceConfig.refreshInterval || 5)) * 1000;

                console.log(`预览页面 - 设置组件 ${widget.id} 自动刷新间隔: ${refreshInterval}ms (数据源类型: ${dataSourceConfig.dataSourceType || 'dataItem'})`);

                // 设置定时器 - 使用平滑数据更新而不是重新渲染
                widget.refreshTimer = setInterval(async () => {
                    try {
                        console.log(`预览页面 - 自动刷新组件 ${widget.id} 数据`);
                        await updateWidgetData(widget);
                    } catch (error) {
                        console.error(`预览页面 - 组件 ${widget.id} 自动刷新失败:`, error);
                    }
                }, refreshInterval);
            } else {
                console.log(`预览页面 - 组件 ${widget.id} 不需要自动刷新 (数据源类型: ${dataSourceConfig.dataSourceType || 'static'})`);
            }
        }

        // 手动刷新所有数据
        function refreshData() {
            console.log('预览页面 - 开始手动刷新所有组件数据');

            // 更新状态指示器
            const indicator = document.getElementById('statusIndicator');
            indicator.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 正在刷新...';

            // 刷新所有组件的数据 - 使用平滑数据更新
            Object.keys(window.widgetInstances).forEach(async (widgetId) => {
                const widget = window.widgetInstances[widgetId];

                if (widget) {
                    try {
                        console.log(`预览页面 - 手动刷新组件 ${widgetId} 数据`);
                        await updateWidgetData(widget);
                    } catch (error) {
                        console.error(`预览页面 - 组件 ${widgetId} 手动刷新失败:`, error);
                    }
                }
            });

            // 更新状态指示器
            setTimeout(() => {
                indicator.innerHTML = '<i class="bi bi-wifi"></i> 数据已更新 ' + new Date().toLocaleTimeString();
            }, 1000);
        }

        // 切换全屏
        function toggleFullscreen() {
            if (!isFullscreen) {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
                document.body.classList.add('fullscreen-mode');
                isFullscreen = true;
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                document.body.classList.remove('fullscreen-mode');
                isFullscreen = false;
            }

            // 延迟调整画布缩放以适应全屏变化
            setTimeout(() => {
                const canvas = document.getElementById('dashboardCanvas');
                const canvasConfig = JSON.parse(window.dashboardData.canvasConfig);
                applyCanvasScale(canvas, canvasConfig);

                // 调整所有ECharts图表大小
                Object.keys(charts).forEach(widgetId => {
                    const chart = charts[widgetId];
                    if (chart && chart.resize) {
                        chart.resize();
                    }
                });
            }, 300);
        }

        // 返回设计器
        function goBack() {
            window.location.href = `/bi/dashboard/${window.dashboardData.id}/design`;
        }

        // 监听窗口大小变化
        window.addEventListener('resize', debounce(function() {
            console.log('预览页面 - 窗口大小变化，重新计算缩放');

            const canvas = document.getElementById('dashboardCanvas');
            const canvasConfig = JSON.parse(window.dashboardData.canvasConfig);
            applyCanvasScale(canvas, canvasConfig);

            // 调整所有ECharts图表大小
            Object.keys(charts).forEach(widgetId => {
                const chart = charts[widgetId];
                if (chart && chart.resize) {
                    chart.resize();
                }
            });
        }, 250));

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement) {
                document.body.classList.remove('fullscreen-mode');
                isFullscreen = false;
            }
        });

        // 监听WebKit全屏状态变化
        document.addEventListener('webkitfullscreenchange', function() {
            if (!document.webkitFullscreenElement) {
                document.body.classList.remove('fullscreen-mode');
                isFullscreen = false;
            }
        });

        // 监听MS全屏状态变化
        document.addEventListener('msfullscreenchange', function() {
            if (!document.msFullscreenElement) {
                document.body.classList.remove('fullscreen-mode');
                isFullscreen = false;
            }
        });
    </script>
</body>
</html>
